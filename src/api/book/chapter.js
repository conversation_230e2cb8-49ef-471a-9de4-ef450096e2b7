import request from '@/request'

// 查询数字教材章节目录列表
export function listChapter(query) {
  return request({
    url: '/book/chapter/list',
    method: 'get',
    params: query,
  })
}
export function processChapterList(query) {
  return request({
    url: '/book/chapter/processChapterList',
    method: 'get',
    params: query,
  })
}

// 查询数字教材章节目录列表
export function queryChapterList(bookId) {
  return request({
    url: '/book/chapter/queryChapterList/' + bookId,
    method: 'get',
  })
}

export function queryChapterListLastVersion(bookId) {
  return request({
    url: '/book/chapter/queryChapterListLastVersion/' + bookId,
    method: 'get',
  })
}


// 查询数字教材章节目录列表 为了下拉
export function listForSelect(query) {
  return request({
    url: '/book/chapter/listForSelect',
    method: 'get',
    params: query,
  })
}

// 查询数字教材章节目录列表
export function listForSort(query) {
  return request({
    url: '/book/chapter/listForSort',
    method: 'get',
    params: query,
  })
}

// 查询数字教材章节目录详细
export function getChapter(chapterId) {
  return request({
    url: `/book/chapter/${chapterId}`,
    method: 'get',
  })
}

// 查询数字教材章节回收站列表
export function listForRecycle(query) {
  return request({
    url: '/book/chapter/listForRecycle',
    method: 'get',
    params: query,
  })
}

// 新增数字教材章节目录
export function addChapter(data) {
  return request({
    url: '/book/chapter',
    method: 'post',
    data,
  })
}

// 修改数字教材章节目录
export function updateChapter(data) {
  return request({
    url: '/book/chapter',
    method: 'put',
    data,
  })
}

// 修改数字教材章节目录
export function updateChapterInfo(data) {
  return request({
    url: '/book/chapter/info',
    method: 'put',
    data,
  })
}

// 恢复数字教材章节目录
export function recycleChapter(data) {
  return request({
    url: '/book/chapter/recycleChapter',
    method: 'put',
    data,
  })
}

// 更新数字教材章节目录顺序
export function updateChapterSort(data) {
  return request({
    url: '/book/chapter/updateChapterSort',
    method: 'put',
    data,
  })
}

// 删除数字教材章节目录
export function delChapter(chapterId) {
  return request({
    url: `/book/chapter/${chapterId}`,
    method: 'delete',
  })
}

export function updateCharpterFree(data) {
  return request({
    url: '/book/chapter/updateCharpterFree',
    method: 'put',
    data,
  })
}

export function getChapterInfo(chapterId) {
  return request({
    url: `/book/chapter/chapterInfo/${chapterId}`,
    method: 'get',
  })
}

// 导出数字教材章节
export function exportBookChapter(data) {
  return request({
    url: '/book/chapter/export',
    method: 'post',
    data,
  })
}

// 查询小标题
export function chapterCatalogList(chapterId) {
  return request({
    url: '/book/chapter/chapterCatalogList/' + chapterId,
    method: 'get',
  })
}

// 获取token存入redis
export function getRedisToken(data) {
  return request({
    url: '/book/readerApi/getRedisToken/',
    method: 'get',
    params: data,
  })
}
