import { mergeAttributes, Node } from '@tiptap/core'
import { VueNodeViewRenderer } from '@tiptap/vue-3'

import NodeView from './node-view.vue'

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    setChapterHeader: {
      setChapterHeader: (options: any) => ReturnType
    }
  }
}
export default Node.create({
  name: 'chapterHeader',

  group: 'block',
  content: 'block+',
  addAttributes() {
    return {
      vnode: {
        default: true,
      },
      id: {
        default: null,
      },
      chapterHeaderUrl: {
        default: null,
      },
      chapterHeaderHeight: {
        default: null,
      },
      chapterHeaderPaddingTop: {
        default: 0,
      },
      chapterHeaderPaddingBottom: {
        default: 0,
      },
      chapterHeaderPaddingLeft: {
        default: 0,
      },
      chapterHeaderPaddingRight: {
        default: 0,
      },
      paddingLinkValue: {
        default: false,
      },
    }
  },
  parseHTML() {
    return [{ tag: 'chapterHeader' }]
  },
  renderHTML({ HTMLAttributes }) {
    return ['chapterHeader', mergeAttributes(HTMLAttributes), 0]
  },
  addNodeView() {
    return VueNodeViewRenderer(NodeView as Component)
  },
  addCommands() {
    return {
      setChapterHeader:
        (options) =>
        ({ commands, tr, dispatch, editor }) => {
          if (dispatch) {
            tr.replaceRangeWith(
              editor.state.selection.anchor,
              editor.state.selection.anchor,
              editor.schema.node(
                this.name,
                options,
                editor.schema.nodes.paragraph.create(
                  '',
                  editor.schema.text(options.title || '章头'),
                ),
              ),
            )
          }
          return true
        },
    }
  },
})
