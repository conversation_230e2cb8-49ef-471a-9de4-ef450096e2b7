<template>
  <node-view-wrapper :id="node.attrs.id" ref="containerRef" class="umo-node-view" @mouseenter="showChild"
    @mouseleave="cannelHideLayer">
    <div class="umo-node-view-extended-reading">
      <div v-show="mousemove" class="icon" @click.stop="keepOpen">
        <t-dropdown class="rc-icon" :tooltip="false" trigger="click">
          <EllipsisIcon />
          <template #dropdown>
            <t-dropdown-item :value="1">
              <div style="display: flex; align-items: center" @click="setting">
                <SettingIcon />
                <span style="margin-left: 5px">{{ t('rc.menu.setting') }}</span>
              </div>
            </t-dropdown-item>
            <t-dropdown-item :value="2">
              <div style="display: flex; align-items: center" @click="delRC">
                <DeleteIcon />
                <span style="margin-left: 5px">{{ t('rc.menu.delete') }}</span>
              </div>
            </t-dropdown-item>
          </template>
        </t-dropdown>
      </div>
      <div class="extended-reading-template-bg"
        :style="`background: url(${template?.orderTemplateBgUrl ?? node.attrs.bgSrc}) no-repeat center;height:55px;background-size:100% 100%;`">
        <div class="extended-left">
          <div class="extended-icon">
            <img :src="template?.theme == 'light' ? daskReadLight : daskRead" alt="" />
          </div>


          <div class="extended-name"
            :style="{ color: template?.theme == 'light' ? '#fff' : 'var(--umo-content-text-color)' }">
            <t-tooltip :content="node.attrs.name" :show-arrow="false">
              {{ node.attrs.name }}
            </t-tooltip>
          </div>

        </div>
        <!-- <div class="extended-right">收起</div> -->
      </div>

      <div class="extended-reading-template-content">
        <node-view-content />
      </div>
    </div>
  </node-view-wrapper>

  <!-- 设置弹窗 -->
  <modal :visible="titleModelVisibel" :header="t('rc.form.header')" :footer="false" :close-on-overlay-click="false"
    @close="titleModelVisibel = false">
    <div>
      <t-form ref="settingFormRef" :data="setttingForm" @submit="submit">
        <t-form-item :label="t('rc.form.title')" name="name" :rules="[
          {
            required: true,
            message: t('rc.form.titleRequired'),
            type: 'error',
          },
        ]">
          <t-input v-model="setttingForm.name" :placeholder="t('rc.form.titlePlaceholder')" :maxlength="100" />
        </t-form-item>

        <t-form-item :label="t('insert.fileDialog.isExpand')" name="isExpand">
          <t-switch v-model="setttingForm.isExpand"></t-switch>
        </t-form-item>

        <div class="footer-btn-group">
          <t-button type="submit" theme="primary">{{ t('insert.image.comfirm') }}</t-button>
        </div>
      </t-form>
    </div>
  </modal>
</template>

<script setup lang="ts">
import { NodeViewContent, nodeViewProps, NodeViewWrapper } from '@tiptap/vue-3'
import { DeleteIcon, EllipsisIcon, SettingIcon } from 'tdesign-icons-vue-next'

import daskRead from '@/assets/resources/dark/read.svg'
import daskReadLight from '@/assets/resources/light/read.svg'
import { useMouseOver } from '@/hooks/mouseOver'
const { cannelHideLayer, showChild, mousemove, keepOpen } = useMouseOver()

const { node, updateAttributes, deleteNode } = defineProps(nodeViewProps)
const { editor } = useStore()
const { pageTemplateId } = useTemplate()
const template = pageTemplateId.value
let setttingForm = $ref({})
const settingFormRef = ref(null)
let titleModelVisibel = $ref(false)

const setting = () => {
  setttingForm = { ...node.attrs }
  titleModelVisibel = true
}

const delRC = () => {
  deleteNode()
}
async function submit() {
  const valid = await settingFormRef.value.validate({
    showErrorMessage: true,
  })
  if (valid === true) {
    updateAttributes({
      name: setttingForm.name,
      isExpand: setttingForm.isExpand,
    })

    titleModelVisibel = false
  }
}
</script>

<style lang="less" scoped>
.umo-node-view {
  background-color: transparent;
}

.umo-node-view-extended-reading {
  position: relative;
  line-height: 30px;
  width: 100%;

  .icon {
    position: absolute;
    right: 10px;
    top: 14px;
    z-index: 99;
    font-size: 24px;
    cursor: pointer;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 4px;
    color: #fff;
    padding: 5px;
    width: 18px;
    height: 18px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .extended-reading-template-bg {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;

    .extended-left {
      display: flex;
      align-items: center;

      .extended-icon {
        width: 24px;
        height: 24px;

        img {
          width: 100%;
          height: 100%;
        }
      }

      .extended-title {
        padding: 0 10px;
        font-size: 16px;
        font-weight: 400;
      }

      .extended-name {
        color: var(--umo-content-text-color);
        margin-left: 10px;
        width: 550px;
        white-space: nowrap;
        /* 确保文本在一行内显示 */
        overflow: hidden;
        /* 超出容器部分的文本隐藏 */
        text-overflow: ellipsis;
        /* 使用省略号表示被截断的文本 */
      }
    }

    .extended-right {
      color: #666;
      font-size: 16px;
    }
  }

  .extended-reading-template-content {
    background-color: #f6f6f6;
    border: 1px solid #ebebeb;
    padding: 20px 16px;

  }
}

.footer-btn-group {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}
</style>
