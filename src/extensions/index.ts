import SearchReplace from '@sereneinserenade/tiptap-search-and-replace'
import Bold from '@tiptap/extension-bold'
import CharacterCount from '@tiptap/extension-character-count'
import Color from '@tiptap/extension-color'
import Document from '@tiptap/extension-document'
import Dropcursor from '@tiptap/extension-dropcursor'
import Focus from '@tiptap/extension-focus'
import { Heading } from '@tiptap/extension-heading'
import Highlight from '@tiptap/extension-highlight'
// 插入
import Link from '@tiptap/extension-link'
import { ListItem } from '@tiptap/extension-list-item'
import Subscript from '@tiptap/extension-subscript'
import Superscript from '@tiptap/extension-superscript'
import TableRow from '@tiptap/extension-table-row'
import TaskItem from '@tiptap/extension-task-item'
import TextColor from '@tiptap/extension-text-style'
import Typography from '@tiptap/extension-typography'
import Underline from '@tiptap/extension-underline'
import StarterKit from '@tiptap/starter-kit'
import type { Editor } from '@tiptap/vue-3'

import ColumnCount from '@/extensions/columnCount'
import ImageGallery from '@/extensions/imageGallery/imageGallery'
import LetterSpacing from '@/extensions/letterSpacing'
import TextGlow from '@/extensions/textGlow'
import TextShadow from '@/extensions/textShadow'
import TextStroke from '@/extensions/textStroke'
import { OssService } from '@/utils/aliOss'
import { getFileExtension } from '@/utils/file'

import Audio from './audio'
import audioHanlin from './audioHanlin'
import BatchUpdateMarkAndAttrs from './batchUpdateMarkAndAttrs'
import blockView from './blockView'
//气泡
import BubbleInline from './bubble'
import BulletList from './bullet-list'
import chapterHeader from './chapterHeader'
import CodeBlock from './code-block'
import { ColorHighlighter } from './color-highlighter'
import columnItem from './columnItem'
import CopyPasteNode from './CopyPaste'
// 纠错
import errorCorrection from './errorCorrection'
import ExtendedReading from './extendedReading'
import File from './file'
import FileHandler from './file-handler'
import fold from './fold'
import FontFamily from './font-family'
// 折叠
import FontSize from './font-size'
// 基本
import FormatPainter from './format-painter'
import formulaInLine from './formulaInLine'
import hr from './hr'
import Iframe from './iframe'
import Image from './image'
import ImageIcon from './imageIcon'
import ImageInLine from './imageInLine'
import ImageLayout from './imageLayout'
import Indent from './indent'
import inputCommon from './inputCommon'
// 互动
import Interaction from './interaction'
import jointHeader from './jointHeader'
import layoutColumn from './layoutColumn'
// 布局分栏
import LineHeight from './line-height'
import Links from './links'
// 表格
import Table from './list/table'
import TaskList from './list/tasklist'
import Margin from './margin'
import NodeAlign from './node-align'
import nodeFloat from './node-float'
import OrderedList from './ordered-list'
import {
  AUDIO,
  BACKGROUNDIMG,
  BLOCK_QUOTE,
  BLOCKVIEW,
  BUBBLEINLINE,
  BULLETLIST,
  CASSIE_BLOCK,
  CODE_BLOCK,
  EXTENDED_READING,
  FILE,
  FOLD,
  HEADING,
  HORIZONTALRULE,
  IFRAME,
  IMAGE,
  IMAGEGALLERY,
  IMAGELAYOUT,
  INTERACTION,
  LAYOUTCOLUMN,
  LISTITEM,
  ORDEREDLIST,
  PAGE,
  PAGEBREAK,
  PAPERWRAPPING,
  PARAGRAPH,
  PSYCHOLOGY_HEALTH,
  RESOURCE_COVER,
  SURROUND,
  TABLE,
  TABLE_ROW,
  TABLEHEADER,
  TEXT_BOX,
  TOC,
  VIDEO,
} from './page/node-names'
// 题型
// import Paper from './paper'
import paperWrapping from './paperWrapping'
//封面
// 设置背景图片
import paragraphBackground from './paragraph-background'
import PsychologyHealth from './psychologyHealth'
// 设置背景图片
// 题型
import Questions from './questions'
// 题型
// 拓展阅读预览组件
//备注
import RemarkInLine from './remark'
//备注
// 资源封面组件
import ResourceCover from './resource-cover'
// 资源封面组件
// 其他
import Selection from './selection'
import surround from './surround'
// 其他
import TableCell from './table-cell'
import TableHeader from './table-header'
import { getHierarchicalIndexes } from './tableOfContents'
import tableOfContentsPlus from './tableOfContents'
//表格扩展
import tablePlus from './tablePlus'
// 试卷/作业
import TestPaper from './testPaper'
//表格扩展
import TextAlign from './text-align'
import TextBox from './text-box'
import textBorder from './textBorder'
// 页面
import Toc from './toc'
import uniqueId from './uniqueId'
// 页面
import Video from './video'
const { options, container, editorUpdate, tableOfContents, loading } =
  useStore()

const { dicts, document: doc, file } = options.value

function idGenerateWithPrefix(from) {
  const id = generateUUID()
  // console.log('generateID', from, id)
  // 添加一个前缀，保证id数值可以作为有效的id选择器，在阅读器中使用。
  return `e${id}`
}

// 自动生成ID的节点列表
const types = [
  HEADING,
  PARAGRAPH,
  BULLETLIST,
  LISTITEM,
  ORDEREDLIST,
  TABLE,
  TABLE_ROW,
  CASSIE_BLOCK,
  TEXT_BOX,
  IMAGE,
  IFRAME,
  FILE,
  CODE_BLOCK,
  AUDIO,
  TOC,
  VIDEO,
  HORIZONTALRULE,
  PAGEBREAK,
  TABLEHEADER,
  'questions',
  PAGE,
  RESOURCE_COVER,
  INTERACTION,
  IMAGEGALLERY,
  IMAGELAYOUT,
  BLOCK_QUOTE,
  BUBBLEINLINE,
  PAPERWRAPPING,
  LAYOUTCOLUMN,
  'imageIcon',
  'imageInLine',
  'columnItem',
  'papers',
  EXTENDED_READING,
  audioHanlin,
  BLOCKVIEW,
  FOLD,
  PSYCHOLOGY_HEALTH,
  'formulaInLine',
  SURROUND,
  BACKGROUNDIMG,
]
import { v4 as generateUUID } from 'uuid'

import backgroundImg from './backgroundImg'

export const extensions = [
  StarterKit.configure({
    document: false,
    bold: false,
    bulletList: false,
    orderedList: false,
    codeBlock: false,
    horizontalRule: false,
    dropcursor: false,
    listItem: false,
    heading: false,
  }),
  ListItem.extend({
    content: 'block*',
  }),
  Heading.extend({
    addAttributes() {
      return {
        ...this.parent?.(),
        fontWeight: {
          default: null,
          renderHTML: (attributes) => {
            if (attributes.fontWeight) {
              return {
                style: `font-weight: ${attributes.fontWeight}`,
              }
            }
          },
        },
      }
    },
  }),
  Document.extend({ content: 'page+' }),
  uniqueId.configure({
    attributeName: 'id',
    generateID: () => idGenerateWithPrefix('content'),
    types,
  }),
  Focus.configure({
    className: 'umo-node-focused',
    mode: 'all',
  }),
  FormatPainter,
  FontFamily,
  FontSize,
  Bold.extend({
    renderHTML: ({ HTMLAttributes }) => ['b', HTMLAttributes, 0],
  }),
  Underline,
  Subscript,
  Superscript,
  Color,
  TextColor,
  Highlight.configure({
    multicolor: true,
  }),
  TextShadow,
  TextStroke,
  ImageGallery,
  LetterSpacing,
  TextGlow,
  ColumnCount.configure({
    types: ['heading', 'paragraph'],
  }),
  ,
  BulletList,
  OrderedList,
  Indent,
  TextAlign,
  NodeAlign,
  TaskItem.configure({ nested: true }),
  TaskList.configure({
    HTMLAttributes: {
      class: 'umo-task-list',
    },
  }),
  LineHeight.configure({
    types: ['heading', 'paragraph'],
    defaultLineHeight: dicts.lineHeights.find((item: any) => item.default)
      .value,
  }),
  Margin,
  backgroundImg,
  SearchReplace.configure({
    searchResultClass: 'umo-search-result',
  }),
  Link,
  Image,
  ImageLayout,
  ImageInLine,
  ImageIcon,
  Video,
  blockView,
  Audio,
  File,
  TextBox,
  textBorder,
  CodeBlock,
  ColorHighlighter,
  hr,
  Iframe,
  audioHanlin,
  // 表格
  Table.configure({
    allowTableNodeSelection: true,
    resizable: true,
  }),
  TableRow,
  TableHeader,
  TableCell,
  // 页面
  Toc,
  // 其他
  Selection,
  nodeFloat,

  tableOfContentsPlus.configure({
    getIndex: getHierarchicalIndexes,
    onUpdate: (content) => {
      // bugfix: 文档刚加载到页面上，左侧目录不显示内容
      tableOfContents.value = content
      if (content) {
        // bugfix: 文档刚加载到页面上，左侧目录不显示内容
        editorUpdate()
      }
    },
    scrollParent: () => document.querySelector(`.umo-main-bg`) as HTMLElement,
    getId: () => idGenerateWithPrefix('catalog'),
    anchorTypes: ['heading', 'jointHeader', 'imageLayout'],
  }),
  Typography.configure(doc.typographyRules),
  CharacterCount.configure({
    limit: doc.characterLimit !== 0 ? doc.characterLimit : undefined,
  }),
  FileHandler.configure({
    allowedMimeTypes: file.allowedMimeTypes,
    onPaste: async (editor: Editor, files: any) => {
      loading.value = true
      for (const file of files) {
        const { size, name: filename } = file
        const suffix = getFileExtension(filename) ?? 'png'
        // const fileSize = 20
        // // 文件大小
        // if (size > fileSize * 1024 * 1024) {
        //   loading.value = false
        //   useMessage('error', `文件大小不能超过${fileSize}MB`)
        //   return false
        // }

        const res: any = await OssService(file)
        editor.commands.insertFile({
          file: {
            url: res.url,
            type: file.type,
            fileSize: size,
            size,
            fileName: res.result.data.name,
            name: filename,
            originName: filename,
          },
          autoType: true,
        })
      }
      loading.value = false
    },
    onDrop: async (editor: Editor, files: any, pos: number) => {
      loading.value = true
      for (const file of files) {
        const { size, name: filename } = file
        const suffix = getFileExtension(filename) ?? 'png'
        // const fileSize = 20
        // // 文件大小
        // if (size > fileSize * 1024 * 1024) {
        //   loading.value = false
        //   useMessage('error', `文件大小不能超过${fileSize}MB`)
        //   return false
        // }

        const res: any = await OssService(file)
        editor.commands.insertFile({
          file: {
            url: res.url,
            type: file.type,
            fileSize: size,
            size,
            fileName: res.result.data.name,
            originName: filename,
          },
          autoType: true,
          pos,
        })
      }
      loading.value = false
    },
  }),
  Dropcursor.configure({
    color: 'var(--umo-primary-color)',
  }),
  ResourceCover,
  Links,
  Questions,
  TestPaper,
  PsychologyHealth,
  RemarkInLine,
  BubbleInline,
  Interaction,
  paragraphBackground,
  tablePlus,
  errorCorrection,
  inputCommon,
  chapterHeader,
  jointHeader,
  paperWrapping,
  layoutColumn,
  columnItem,
  ExtendedReading,
  fold,
  surround,
  formulaInLine,
  CopyPasteNode,
  BatchUpdateMarkAndAttrs,
]
