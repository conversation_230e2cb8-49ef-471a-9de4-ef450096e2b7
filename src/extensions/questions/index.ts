import { mergeAttributes, Node, type NodeViewProps } from '@tiptap/core'
import { VueNodeViewRenderer } from '@tiptap/vue-3'

import NodeView from './node-view.vue'

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    questions: {
      questions: (options: any) => ReturnType
    }
  }
}
export default Node.create({
  name: 'questions',
  group: 'block',
  atom: true,
  addAttributes() {
    return {
      vnode: {
        default: true,
      },
      id: {
        default: null,
      },
      width: {
        default: null,
      },
      height: {
        default: 200,
      },
      collapsed: {
        default: true,
      },
      questionsList: {
        default: null,
        /*
        parseHTML: (element) => {
          try {
            return JSON.parse(element.getAttribute('questionsList') || 'null')
          } catch (e) {
            return null
          }
        },
        renderHTML: (attributes) => {
          if (attributes.questionsList) {
            return { questions: JSON.stringify(attributes.questionsList) }
          }
          return {}
        },*/
      },
      optionHeight: {
        default: 200,
      },
      questionTitle: {
        default: '',
      },
    }
  }, // 添加属性
  parseHTML() {
    return [
      {
        tag: 'questions',
      },
    ]
  },
  renderHTML({ HTMLAttributes }) {
    return ['questions', mergeAttributes(HTMLAttributes)]
  },
  addNodeView() {
    return VueNodeViewRenderer(NodeView as Component<NodeViewProps>)
  },
  addCommands() {
    return {
      questions:
        (options) =>
        ({ commands, editor }) => {
          return commands.insertContentAt(editor.state.selection.anchor, {
            type: this.name,
            attrs: options,
          })
        },
    }
  },
})
