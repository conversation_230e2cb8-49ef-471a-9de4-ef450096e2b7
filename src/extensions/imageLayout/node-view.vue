<template>
  <node-view-wrapper :id="node.attrs.id" ref="containerRef" class="umo-node-view" :class="{
    'umo-node-view-empty': node.attrs.draggable,
  }" :style="nodeStyle" @dblclick="openImageViewer" @mouseenter="showChild" @mouseleave="cannelHideLayer">
    <div v-show="mousemove" class="top-node-mu" @click.stop="keepOpen">
      <t-dropdown class="rc-icon" :tooltip="false" trigger="click">
        <EllipsisIcon />
        <template #dropdown>
          <t-dropdown-item :value="1">
            <div style="display: flex; align-items: center" @click="viewerImg">
              <BrowseIcon />
              <span style="margin-left: 5px">{{
                t('insert.image.preview')
              }}</span>
            </div>
          </t-dropdown-item>
          <t-dropdown-item :value="2">
            <div style="display: flex; align-items: center" @click="imageEdit">
              <FileSearchIcon />
              <span style="margin-left: 5px">{{
                t('insert.image.setting')
              }}</span>
            </div>
          </t-dropdown-item>
          <t-dropdown-item :value="3">
            <div style="display: flex; align-items: center" @click="imgCropper">
              <TransformIcon />
              <span style="margin-left: 5px">{{ t('insert.image.crop') }}</span>
            </div>
          </t-dropdown-item>
          <t-dropdown-item :value="4">
            <div style="display: flex; align-items: center" @click="handleDelNode">
              <DeleteIcon />
              <span style="margin-left: 5px">{{
                t('insert.image.remove')
              }}</span>
            </div>
          </t-dropdown-item>
        </template>
      </t-dropdown>
    </div>
    <div class="umo-node-container umo-node-image" :class="{
      'is-loading': node.attrs.src && isLoading,
      'is-error': node.attrs.src && error,
      'is-draggable': node.attrs.draggable,
      'umo-hover-shadow': !options.document?.readOnly,
      'umo-select-outline': !node.attrs.draggable,
    }">

      <div v-if="node.attrs.src && isLoading" class="loading" :style="{ height: `${node.attrs.height}px` }">
        <icon name="loading" class="loading-icon" />
        {{ t('node.image.loading') }}
      </div>
      <div v-else-if="node.attrs.src && error" class="error" :style="{ height: `${node.attrs.height}px` }">
        <icon name="image-failed" class="error-icon" />
        {{ t('node.image.error') }}
      </div>

      <drager v-else :selected="selected" :boundary="false" :draggable="Boolean(node.attrs.draggable) && !options.document?.readOnly
        " :disabled="options.document?.readOnly" :angle="node.attrs.angle" :left="Number(node.attrs.left)"
        :top="Number(node.attrs.top)" :width="Number(node.attrs.width)" :height="Number(node.attrs.height)"
        :max-width="maxWidth" :max-height="node.attrs.equalProportion ? maxHeight : undefined" :min-width="14"
        :min-height="14" :z-index="10" :style="{
          margin: '0 auto',
        }" :equal-proportion="node.attrs.equalProportion" @drag="onDrag" @resize="onResize" @click="selected = true">
        <!--  -->

        <img ref="imageRef" :src="node.attrs.src" :data-id="node.attrs.id" loading="lazy" object-fit="contain"
          class="imgCss" :style="{
            maxWidth: `100%`,
          }" @load="onLoad" />

        <!--  -->
      </drager>
      <div class="gallery-title"
        :style="`width:${Number(node.attrs.width)}px;display:flex;justify-content:${node.attrs.titleAlign}`">
        <div v-if="node.attrs.isShowNo == 1" style="width: 40px;flex-shrink: 0;" contenteditable="false">
          {{ node.attrs.number }}
        </div>
        <div v-show="node.attrs.isShowImageTitle == '1'" class="titleText" style="word-break: break-all;">
          <node-view-content />
        </div>
      </div>
    </div>
  </node-view-wrapper>
  <t-dialog v-model:visible="viewerVisible" :header="t('insert.image.preview')" attach="body" width="30%"
    :confirm-btn="null" :close-on-overlay-click="false">
    <imgView :img-url="node.attrs.src" :img-title="node.textContent" :is-show-image-title="node.attrs.isShowImageTitle"
      :link-address="node.attrs.linkAddress" />
  </t-dialog>
  <t-dialog v-model:visible="imageEditPopup" attach="body" :header="t('insert.image.setting')" width="30%"
    :close-on-overlay-click="false" @confirm="onSubmit">
    <div>

      <t-form ref="formValidatorStatus" :data="formData" :label-width="120">
        <!-- 图片标题-->
        <!-- <t-form-item :label="t('insert.image.imageTitle')" name="imageTitle">
          <t-input
            v-model="formData.imageTitle"
            :placeholder="t('insert.image.imagePlaceholder')"
          ></t-input>
        </t-form-item> -->
        <!-- 链接地址-->
        <t-form-item :label="t('insert.image.linkAddress')" name="linkAddress">
          <t-input v-model="formData.linkAddress" :placeholder="t('insert.image.linkAddressPlaceholder')"></t-input>
        </t-form-item>
        <t-form-item :label="t('insert.image.originalName')" name="name">
          {{ node.attrs.name }}
        </t-form-item>
        <!-- 是否显示图片标题 -->
        <t-form-item :label="t('insert.image.isShowImgTitle')" name="isShowGalleryTitle">
          <t-radio-group v-model="formData.isShowImageTitle">
            <t-radio value="1">{{ t('insert.image.yes') }}</t-radio>
            <t-radio value="0">{{ t('insert.image.no') }}</t-radio>
          </t-radio-group>
        </t-form-item>
        <!-- 图片标题对齐方式 -->
        <t-form-item :label="t('insert.image.titleAlign')" name="titleAlign" v-if="formData.isShowImageTitle == '1'">
          <t-radio-group v-model="formData.titleAlign">
            <t-radio value="flex-start">{{ t('insert.image.left') }}</t-radio>
            <t-radio value="center">{{ t('insert.image.center') }}</t-radio>
            <t-radio value="flex-end">{{ t('insert.image.right') }}</t-radio>
          </t-radio-group>
        </t-form-item>
        <t-form-item :label="t('insert.image.proportionally')" name="equalProportion">
          <t-switch v-model="formData.equalProportion"></t-switch>
        </t-form-item>
        <t-form-item :label="t('insert.image.width')" name="width">
          <t-input-number v-model="formData.width" :step="1" :min="1" @blur="onWidthChange" style="width: 200px;"
            :decimal-places="2" suffix="px"></t-input-number>
        </t-form-item>
        <t-form-item :label="t('insert.image.height')" name="height">
          <t-input-number v-model="formData.height" :step="1" :decimal-places="2" :min="1"
            :readonly="formData.equalProportion" style="width: 200px;" suffix="px"></t-input-number>
        </t-form-item>
        <!-- 是否显示图号 -->
        <t-form-item :label="t('insert.image.isShowNo')" name="isShowNo">
          <t-radio-group v-model="formData.isShowNo">
            <t-radio :value="1">{{ t('insert.image.yes') }}</t-radio>
            <t-radio :value="0">{{ t('insert.image.no') }}</t-radio>
          </t-radio-group>
        </t-form-item>

        <!-- 是否作为目录 -->
        <t-form-item :label="t('insert.image.isAsCatalog')" name="isNotAsCatalog">
          <t-radio-group v-model="formData.isNotAsCatalog">
            <t-radio :value="false">{{ t('insert.image.yes') }}</t-radio>
            <t-radio :value="true">{{ t('insert.image.no') }}</t-radio>
          </t-radio-group>
        </t-form-item>
        <!-- 目录层级-->
        <t-form-item v-if="formData.isNotAsCatalog === false" required-mark :label="t('insert.image.level')"
          name="level" :rules="[
            { required: true, message: t('insert.image.levelRequired') },
            {
              validator: (val) => val >= 1 && val <= 6,
              message: t('insert.image.levelMax'),
            },
          ]">
          <t-input-number v-model="formData.level" :step="1" :max="6" :min="1"></t-input-number>
        </t-form-item>
        <!-- 修改图片 -->
        <t-form-item :label="t('insert.image.updateImage')" name="src">
          <t-button @click="resetImg">{{ t('insert.image.resetImage') }}</t-button>
        </t-form-item>
        <t-form-item :label="t('insert.image.resetResourceLibrary')" name="src">
          <t-button @click="library">{{ t('insert.image.resetResourceLibrary') }}</t-button>
        </t-form-item>
      </t-form>
    </div>
  </t-dialog>
  <cropper-dialog v-model:cropper-visible="imgVisible" :url="node.attrs.src" :file-name="node.attrs.name"
    @save-cropper="saveCropper"></cropper-dialog>

  <ResourceLibrary v-model:visible="show" :file-type="'1'" @insert-by-resource="insertByResource" />
</template>

<script setup lang="ts">
import { NodeViewContent, nodeViewProps, NodeViewWrapper } from '@tiptap/vue-3'
import Drager from 'es-drager'
import {
  BrowseIcon,
  DeleteIcon,
  EllipsisIcon,
  FileSearchIcon,
  TransformIcon,
} from 'tdesign-icons-vue-next'

import cropperDialog from '@/components/cropperDialog/index.vue'
import imgView from '@/components/menus/toolbar/insert/components/image/imgView.vue'
import { useMouseOver } from '@/hooks/mouseOver'
import { chooseFile, defaultOptPreChekck } from '@/utils/file'
const { node, updateAttributes, deleteNode } = defineProps(nodeViewProps)
const { options, editor, imageViewer, getCaptionStyle, batchInsert } =
  useStore()
const { isLoading, error } = useImage({ src: node.attrs.src })
const { cannelHideLayer, showChild, mousemove, keepOpen } = useMouseOver()
const show = ref(false)
const containerRef = ref(null)
const imageRef = $ref(null)
let selected = $ref(false)
let maxWidth = $ref(0)
let maxHeight = $ref(0)

// 表单验证
const formValidatorStatus = ref(null)
const rules = reactive({
  // imageTitle: [
  //   {
  //     required: true,
  //     message: t('insert.image.imagePlaceholder'),
  //     type: 'error',
  //     trigger: 'blur',
  //   },
  // ],
})

// 表单数据
const formData = ref({})

// 预览
const viewerVisible = ref(false)
const viewerImg = () => {
  viewerVisible.value = true
}

// 截图部分代码
const imgVisible = ref(false)

function imgCropper() {
  imgVisible.value = true
}

// 截取保存事件
const saveCropper = (url) => {
  updateAttributes({ src: url })
}

// 编辑弹窗标识
const imageEditPopup = ref(false)

// 开启dialog
const imageEdit = () => {
  formData.value = {
    // imageTitle: node.attrs.imageTitle,
    isShowNo: node.attrs.isShowNo,
    isShowImageTitle: node.attrs.isShowImageTitle + '',
    linkAddress: node.attrs.linkAddress,
    isNotAsCatalog: node.attrs.isNotAsCatalog,
    level: node.attrs.level,
    titleAlign: node.attrs.titleAlign,
    width: node.attrs.width,
    height: node.attrs.height,
    equalProportion: node.attrs.equalProportion,
  }
  imageEditPopup.value = true
}
// 关闭dialog
const onClose = () => {
  imageEditPopup.value = false
}


// 从资源库选择图片
const library = () => {
  show.value = true
}


const onWidthChange = (val) => {
  if (formData.value.equalProportion) {
    formData.value.height = node.attrs.height * (val / node.attrs.width)
  }
}

const insertByResource = (file) => {

  const imageWidth = imageRef.naturalWidth > 697 ? 697 : imageRef.naturalWidth;
  let imageHeight = imageRef.naturalHeight;
  const ratio = imageRef.naturalWidth / imageHeight
  let height = Number(imageWidth) / Number(ratio);
  updateAttributes({
    src: file.fileUrl,
    imageTitle: file.name,
    width: imageWidth,
    height: height
  })
}

// 保存事件
const onSubmit = () => {
  const validateResult = formValidatorStatus.value.validate({
    showErrorMessage: true,
  })
  validateResult.then((valid) => {
    if (valid === true) {
      const validProtocols = /^(?:(http|https|ftp|ftps|mailto):\/\/)|^www\./i
      if (
        formData.value.linkAddress != '' &&
        !validProtocols.test(formData.value.linkAddress)
      ) {
        useMessage('error', '请填写正确的链接地址')
        return false
      }
      if (formData.value.linkAddress.startsWith('www.')) {
        formData.value.linkAddress = `https://${formData.value.linkAddress}`
      }
      updateAttributes({
        // imageTitle: formData.value.imageTitle,
        isShowNo: Number(formData.value.isShowNo),
        isShowImageTitle: formData.value.isShowImageTitle,
        linkAddress: formData.value.linkAddress,
        isNotAsCatalog: formData.value.isNotAsCatalog,
        level: formData.value.level,
        titleAlign: formData.value.titleAlign,
        width: formData.value.width,
        height: formData.value.height,
      })
      imageEditPopup.value = false
    }
  })
}

// 删除事件
function handleDelNode() {
  deleteNode()
}

const nodeStyle = $computed(() => {
  const { nodeAlign, margin } = node.attrs
  const marginTop =
    margin?.top && margin?.top !== '' ? `${margin.top}px` : undefined
  const marginBottom =
    margin?.bottom && margin?.bottom !== '' ? `${margin.bottom}px` : undefined
  return {
    justifyContent: nodeAlign,
    marginTop,
    marginBottom,
    zIndex: selected ? 100 : node.attrs.draggable ? 95 : 0,
  }
})

const onLoad = async () => {
  if (node.attrs.number == '') {
    getCaptionStyle().then((res) => {
      if (res.imageNumberType == 1) {
        updateAttributes({
          isShowNo: 0,
          number: '图1',
        })
      } else if (res.imageNumberType == 2) {
        updateAttributes({
          isShowNo: 0,
          number: '图1-1',
        })
      } else {
        updateAttributes({
          isShowNo: 0,
        })
      }
    })
  }

  if (node.attrs.width === null) {
    const { clientWidth = 1, clientHeight = 1 } = imageRef ?? {}
    const ratio = clientWidth / clientHeight
    maxWidth = containerRef.value?.$el.clientWidth
    maxHeight = containerRef.value?.$el.clientWidth / ratio
    updateAttributes({ width: clientWidth })
  }
  if ([null, 'auto', 0].includes(node.attrs.height)) {
    await nextTick()
    const { height } = imageRef?.getBoundingClientRect() ?? {}
    updateAttributes({ height: (height as number).toFixed(0) })
  }
}

const onRotate = ({ angle }: { angle: number }) => {
  updateAttributes({ angle })
}
const onResize = ({ width, height }: { width: number; height: number }) => {
  updateAttributes({
    width: width.toFixed(2),
    height: height.toFixed(2),
  })
}

const onDrag = ({ left, top }: { left: number; top: number }) => {
  updateAttributes({ left, top })
}

onClickOutside(containerRef, () => {
  selected = false
})

const openImageViewer = () => {
  imageViewer.value.visible = true
  imageViewer.value.current = node.attrs.id
}

const resetImg = () => {
  chooseFile((file) => {
    updateAttributes({ src: file.fileUrl })
  }, {
    optPreChekck: defaultOptPreChekck
  })
}

const textAlign = computed(() => {
  const { titleAlign } = node.attrs
  if (titleAlign == 'flex-start') {
    return 'left'
  }
  if (titleAlign == 'flex-end') {
    return 'right'
  }
  return 'center'
})

watch(
  () => node.attrs.equalProportion,
  async () => {
    await nextTick()
    const width = imageRef?.offsetWidth ?? 1
    const height = imageRef?.offsetHeight ?? 1
    updateAttributes({ width, height })
  },
)


watch(() => node.attrs.titleAlign, async () => {
  await nextTick()
  updateAttributes({ titleAlign: node.attrs.titleAlign })
})



</script>

<style lang="less" scoped>
.umo-node-view {
  position: relative;

  .top-node-mu {
    position: absolute;
    right: 0px;
    top: 0px;
    z-index: 99;
    cursor: pointer;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 4px;
    color: #fff;
    padding: 5px;
    width: 24px;
    height: 24px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .umo-node-image {
    max-width: 100%;
    width: auto;
    position: relative;
    z-index: 20;

    &.is-loading,
    &.is-error {
      outline: none !important;
      box-shadow: none !important;
    }

    &:not(.is-draggable) .es-drager {
      max-width: 100%;
      max-height: 100%;
    }

    .imgCss {
      max-width: 100%;
    }

    // img {
    //   display: block;

    //   width: 100%;
    //   height: 100%;
    // }

    .loading {
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--umo-text-color-light);
      font-size: 12px;
      gap: 10px;

      .loading-icon {
        color: var(--umo-primary-color);
        font-size: 22px;
        animation: turn 1s linear infinite;
      }
    }

    .error {
      width: 200px;
      height: 200px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      color: var(--umo-text-color-light);
      font-size: 12px;

      .error-icon {
        font-size: 72px;
        margin: -8px 0 -2px;
      }
    }

    .uploading {
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.1);

      span {
        display: block;
        position: absolute;
        background: rgba(0, 0, 0, 0.2);
        height: 4px;
        border-radius: 2px;
        top: 50%;
        left: 20%;
        right: 20%;
        transform: translateY(-50%);
        overflow: hidden;

        &:after {
          content: '';
          display: block;
          height: 100%;
          background-color: var(--umo-primary-color);
          animation: progress 1s linear infinite;
        }
      }
    }
  }

  .image-title {
    width: 100%;
    padding: 10px 0;
    text-align: center;
    font-size: 12px;
  }

  .gallery-title {
    width: 96%;
    margin: 5px auto;
    display: flex;
    align-items: center;
    justify-content: center;
    // padding: 12px 0;

    color: var(--umo-content-text-color);

    .titleText,
    .titleText * {

      --titleAlign: v-bind('formData.titleAlign');
      --txtAlign: v-bind('textAlign');
      justify-content: var(--titleAlign) !important;
      text-align: var(--txtAlign) !important;
    }
  }
}

@keyframes turn {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

@keyframes progress {
  0% {
    width: 0;
  }

  100% {
    width: 100%;
  }
}
</style>
<style lang="less" scoped>
.cropper-container {
  width: 98%;
  height: 300px;
}
</style>
