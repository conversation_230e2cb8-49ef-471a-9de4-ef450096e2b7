import { type CommandProps, mergeAttributes } from '@tiptap/core'
import Image from '@tiptap/extension-image'
import { TextSelection } from '@tiptap/pm/state'
import { type CommandProps, VueNodeViewRenderer } from '@tiptap/vue-3'

import NodeView from './node-view.vue'
declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    setImageLayout: {
      setImageLayout: (options: any, replace?: any) => ReturnType
    }
  }
}
const tpypes = ['imageLayout', 'chapterHeader', 'jointHeader']
export default Image.extend({
  name: 'imageLayout',
  content: 'paragraph',
  addOptions() {
    return {
      types: ['imageLayout'],
      inline: false,
    }
  },

  addAttributes() {
    return {
      vnode: {
        default: true,
      },
      id: {
        default: null,
      },
      name: {
        default: null,
      },
      size: {
        default: null,
      },
      src: {
        default: null,
      },
      width: {
        default: null,
      },
      height: {
        default: 'auto',
      },
      left: {
        default: 0,
      },
      top: {
        default: 0,
      },
      angle: {
        default: null,
      },
      draggable: {
        default: false,
      },
      rotatable: {
        default: false,
      },
      equalProportion: {
        default: true,
      },
      flipX: {
        default: false,
      },
      flipY: {
        default: false,
      },
      imageTitle: {
        default: t('insert.image.imageTitle'),
      },
      linkAddress: {
        default: '',
      },
      isShowNo: {
        default: 0,
      },
      isShowImageTitle: {
        default: '1',
      },
      number: {
        default: '',
      },
      type: {
        default: 'image',
      },
      content: {
        default: null,
      },
      level: {
        default: 1,
      },
      isNotAsCatalog: {
        default: true,
      },
      titleAlign: {
        default: 'center',
      },
      
    }
  },

  parseHTML() {
    return [{ tag: 'imageLayout' }]
  },
  renderHTML({ HTMLAttributes }) {
    return ['imageLayout', mergeAttributes(HTMLAttributes), 0]
  },

  addNodeView() {
    return VueNodeViewRenderer(NodeView)
  },
  addCommands() {
    return {
      setImageLayout:
        (
          options: {
            src: string
            size?: number
            imageTitle?: string
            name?: string
            linkAddress?: string
            width?: number
            height?: number
          },
          replace?: boolean,
        ) =>
        ({ commands, editor, tr, dispatch }: CommandProps) => {
          if (dispatch) {
            if (replace) {
              return commands.insertContent({
                type: this.name,
                attrs: {
                  ...options,
                  imageTitle: options?.imageTitle?.replace(/.[^/.]+$/, ''),
                },
              })
            }

            return tr.replaceRangeWith(
              editor.state.selection.anchor,
              editor.state.selection.anchor,
              editor.schema.node(
                this.name,
                options,
                editor.schema.nodes.paragraph.create(
                  '',
                  editor.schema.text(options.imageTitle || '图片标题'),
                ),
              ),
            )
          }
        },
      createNewParagraphAndfocus:
        () =>
        ({ commands, editor, tr, dispatch }: CommandProps) => {
          if (dispatch) {
            const { state } = editor
            const { selection, schema } = state
            const { $from, $to } = selection
            if ($from.pos != $to.pos) {
              return false
            }
            if (
              !tpypes.includes($from.parent.type.name) &&
              !tpypes.includes($from.path[$from.path.length - 6].type.name)
            ) {
              return false
            }
            const isAtEnd = $from.parentOffset === $from.parent.nodeSize - 2
            if (!isAtEnd && $from.parentOffset != 0) {
              return false
            }
            const node = schema.nodes.paragraph.create()
            let pos = null
            if (tpypes.includes($from.parent.type.name)) {
              pos = $from.after($from.depth)
            }
            if (
              tpypes.includes($from.path[$from.path.length - 6].type.name) ||
              $from.parentOffset === 0
            ) {
              pos = $from.after($from.depth - 1)
            }
            if (pos) {
              tr.insert(pos, node)
              // 聚焦
              const newSelection = TextSelection.create(
                tr.doc,
                pos + 1,
                pos + 1,
              )
              tr.setSelection(newSelection)
              return true
            }
            return false
          }
          return true
        },
    }
  },
  addKeyboardShortcuts() {
    return {
      Enter: ({ editor }) => editor.commands.createNewParagraphAndfocus(),
    }
  },
})
