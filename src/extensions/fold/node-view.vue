<template>
  <node-view-wrapper :id="node.attrs.id" class="umo-node-view" @mouseenter="showChild" @mouseleave="cannelHideLayer">
    <div class="umo-node-view__content">
      <div v-show="mousemove" class="icon" @click.stop="keepOpen">
        <t-dropdown class="rc-icon" :tooltip="false" trigger="click">
          <EllipsisIcon />
          <template #dropdown>
            <t-dropdown-item :value="1" @click="openDialogEdit">
              <div style="display: flex; align-items: center">
                <SettingIcon />
                <span style="margin-left: 5px">{{
                  t('insert.image.setting')
                }}</span>
              </div>
            </t-dropdown-item>
            <t-dropdown-item :value="2" @click="handleDelNode">
              <div style="display: flex; align-items: center">
                <DeleteIcon />
                <span style="margin-left: 5px">{{
                  t('insert.image.remove')
                }}</span>
              </div>
            </t-dropdown-item>
          </template>
        </t-dropdown>
      </div>

      <div class="extended-reading-template-bg"
        :style="`${node.attrs.isBg ? `background: url(${template?.orderTemplateBgUrl}) no-repeat center;height:55px;background-size:100% 100%;` : ''}`">
        <div class="extended-left">
          <FlodIcon1 v-if="node.attrs.iconUrl === 'foldIcon1'" :color="node.attrs.iconColor" class="beforeIcon" />
          <FlodIcon2 v-else-if="node.attrs.iconUrl === 'foldIcon2'" :color="node.attrs.iconColor" />
          <FlodIcon3 v-else-if="node.attrs.iconUrl === 'foldIcon3'" :color="node.attrs.iconColor" />
          <FlodIcon4 v-else-if="node.attrs.iconUrl === 'foldIcon4'" :color="node.attrs.iconColor" />
          <FlodIcon5 v-else-if="node.attrs.iconUrl === 'foldIcon5'" :color="node.attrs.iconColor" />
          <FlodIcon6 v-else-if="node.attrs.iconUrl === 'foldIcon6'" :color="node.attrs.iconColor" />
          <div v-else class="extended-icon" @mousedown="backgroundImageShow = true"
            @mouseleave="backgroundImageShow = false">
            <img v-if="node.attrs.iconUrl" :src="node.attrs.iconUrl" alt="" />
          </div>
          <div class="extended-name" :style="titleCss">
            <t-tooltip :content="node.attrs.title" :show-arrow="false">
              {{ node.attrs.title }}
            </t-tooltip>
          </div>
        </div>
        <div class="extended-right" :style="titleCss">{{ node.attrs.expandText }}/{{ node.attrs.collapseText }}</div>
      </div>
      <div class="extended-reading-template-content">
        <t-alert theme="warning" message="请在输入文字后，在添加其他组件，若直接添加其他组件，块组件则被替代" v-if="foldTip">
          <template #operation>
            <span @click="handleOperation">我知道了</span>
          </template>
        </t-alert>
        <node-view-content />
      </div>
    </div>

    <modal v-model:visible="dialogVisible" attach="body" :header="t('insert.fold.text')" width="700"
      :close-on-overlay-click="false" @confirm="updateBlock">
      <div>
        <t-form ref="form" :data="formData" :label-width="150">

          <t-form-item :label="t('insert.fold.title')" name="title">
            <t-input v-model="formData.title" :placeholder="t('insert.fold.placeholder')"></t-input>
          </t-form-item>
          <t-form-item :label="t('insert.fold.fontSize')" name="fontSize">
            <t-input-number v-model="formData.fontSize" :min="14" :placeholder="t('insert.fold.placeholder')"
              style="width: 200px">
            </t-input-number>
          </t-form-item>
          <t-form-item :label="t('insert.fold.fontFamily')" name="fontFamily">
            <t-select v-model="formData.fontFamily" :placeholder="t('insert.fold.familyPlaceholder')"
              style="width: 300px;" @change="fontFamilyChange">
              <t-option-group v-for="(group, index) in allFonts" :key="index" :label="group.label" :divider="false">
                <t-option v-for="item in group.children" :key="item.value ?? ''" class="umo-font-family-item"
                  :value="item.value ?? ''" :label="l(item.label)">
                  <span :style="{ fontFamily: item.value ?? 'FZHTJW' }" v-text="l(item.label)"></span>
                </t-option>
              </t-option-group>
            </t-select>
          </t-form-item>
          <t-form-item :label="t('insert.fold.expand')" name="expandText" style="width: 300px;">
            <t-input v-model="formData.expandText" :placeholder="t('insert.fold.placeholder')"></t-input>
          </t-form-item>

          <t-form-item :label="t('insert.fold.collapseText')" name="collapseText" style="width: 300px;">
            <t-input v-model="formData.collapseText" :placeholder="t('insert.fold.placeholder')"></t-input>
          </t-form-item>


          <t-form-item :label="t('insert.fold.fontColor')" name="fontColor">
            <t-popup placement="right-bottom" attach="body" :destroy-on-close="false">
              <div class="umo-color-picker-more" style="display: flex; align-items: center; cursor: pointer">
                <div
                  :style="`border:1px solid #ddd;width:20px;height:20px;border-radius:5px;background:${formData.fontColor || 'var(--umo-content-text-color);'};margin-right:10px;`">
                </div>
                <div v-text="t('insert.fold.fontColor')"></div>
              </div>
              <template #content>
                <div style="padding: 10px">
                  <color-picker :default-color="defaultColor" @change="backgroundColorChange" />
                </div>
              </template>
            </t-popup>

          </t-form-item>

          <t-form-item :label="t('insert.fold.fontWeight')" name="isStrong">
            <t-switch v-model="formData.isStrong"></t-switch>
          </t-form-item>
          <t-form-item :label="t('insert.fold.isOpen')" name="isOpen">
            <t-switch v-model="formData.isOpen"></t-switch>
          </t-form-item>

          <t-form-item :label="t('insert.fold.isBg')" name="isBg">
            <t-switch v-model="formData.isBg"></t-switch>
          </t-form-item>


          <!-- 图标颜色 -->
          <t-form-item :label="t('insert.fold.iconColor')" name="iconColor">
            <t-popup placement="right-bottom" attach="body" :destroy-on-close="false">
              <div class="umo-color-picker-more" style="display: flex; align-items: center; cursor: pointer">
                <div
                  :style="`border:1px solid #ddd;width:20px;height:20px;border-radius:5px;background:${formData.iconColor};margin-right:10px;`">
                </div>
                <div v-text="t('insert.fold.iconColor')"></div>
              </div>
              <template #content>
                <div style="padding: 10px">

                  <color-picker :default-color="defaultColor" @change="iconColorChange" />
                </div>
              </template>
            </t-popup>

          </t-form-item>


          <t-form-item :label="t('insert.fold.iconText')">
            <t-radio-group v-model="formData.iconUrlGroup" size="large" :default-value="formData.iconUrlGroup"
              @change="changeIcon">
              <t-radio-button v-for="item in imgList" :key="item.name" :value="item.name">
                <img v-if="item.url" :src="item.url" style="
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
              " />
                <span v-if="item.url === ''">
                  {{ t('insert.fold.other') }}
                </span>
              </t-radio-button>
            </t-radio-group>
          </t-form-item>


          <t-form-item v-if="showIcon" :label="t('insert.fold.iconOther')" name="icon">
            <div style="display: flex; justify-content: space-between">
              <div style="display: flex; justify-content: space-between">
                <t-button @click="handleChooseFile">{{
                  t('insert.fold.uploadIcon')
                }}</t-button>

                <t-alert v-if="!formData.iconUrl" theme="error" style="padding: 5px; margin: 0 10px">{{
                  t('insert.fold.uploadIconTips') }}</t-alert>
              </div>
              <div v-if="formData.iconUrl !== 'other'" class="block-img" @mousedown="iconUrlShow = true"
                @mouseleave="iconUrlShow = false">
                <img v-if="formData.iconUrl" :src="formData.iconUrl" alt="" width="20" height="20" />
                <div v-show="iconUrlShow" class="block-delect" @click="backgroundDelclick">
                  <DeleteIcon color="#fff" />
                </div>
              </div>
            </div>
          </t-form-item>
        </t-form>
      </div>
    </modal>
  </node-view-wrapper>
</template>

<script setup lang="ts">
import { NodeViewContent, nodeViewProps, NodeViewWrapper } from '@tiptap/vue-3'
import { DeleteIcon, EllipsisIcon, SettingIcon } from 'tdesign-icons-vue-next'

import foldIcon1 from '@/assets/icons/fold1.svg'
import foldIcon2 from '@/assets/icons/fold2.svg'
import foldIcon3 from '@/assets/icons/fold3.svg'
import foldIcon4 from '@/assets/icons/fold4.svg'
import foldIcon5 from '@/assets/icons/fold5.svg'
import foldIcon6 from '@/assets/icons/fold6.svg'

import FlodIcon1 from './svg/flodIcon1.vue'
import FlodIcon2 from './svg/flodIcon2.vue'
import FlodIcon3 from './svg/flodIcon3.vue'
import FlodIcon4 from './svg/flodIcon4.vue'
import FlodIcon5 from './svg/flodIcon5.vue'
import FlodIcon6 from './svg/flodIcon6.vue'
const { pageTemplateId } = useTemplate()
const template = pageTemplateId.value
import { chooseFile, defaultOptPreChekck } from '@/utils/file'
const { node, updateAttributes, deleteNode } = defineProps(nodeViewProps)
const { options, editor, foldTip, setFoldTip } = useStore()
let dialogVisible = $ref(false)
let formData = $ref({
})
const backgroundImageShow = $ref(false)
const iconUrlShow = $ref(false)
const defaultColor = ref('')
let mousemove = $ref(false)
let isMenuActive = $ref(false)
let hideTimer = null


const imgList = [
  {
    name: 'foldIcon1',
    url: foldIcon1,
  },
  {
    name: 'foldIcon2',
    url: foldIcon2,
  },
  {
    name: 'foldIcon3',
    url: foldIcon3,
  },
  {
    name: 'foldIcon4',
    url: foldIcon4,
  },
  {
    name: 'foldIcon5',
    url: foldIcon5,
  },
  {
    name: 'foldIcon6',
    url: foldIcon6,
  },
  {
    name: 'other',
    url: ''
  }
]


const showChild = () => {
  mousemove = true
  clearTimeout(hideTimer)
}

const keepOpen = () => {
  isMenuActive = true
  clearTimeout(hideTimer)
}

const cannelHideLayer = () => {
  hideTimer = setTimeout(() => {
    if (!isMenuActive) {
      mousemove = false
    }
  }, 200)
}

const handleDelNode = () => {
  deleteNode()
}
const handleOperation = () => {
  setFoldTip(false)
}
const openDialogEdit = () => {
  dialogVisible = true
  console.log(node.attrs)
  if (node.attrs.iconUrl.includes('http')) {
    showIcon.value = true
    formData = {
      ...node.attrs,
      iconUrlGroup: 'other'

    }
  } else {

    if (node.attrs.iconUrl != 'other') {

      formData.iconUrl = ''
    }
    formData = {
      ...node.attrs,
      iconUrlGroup: node.attrs.iconUrl != 'other' ? node.attrs.iconUrl : 'other',
    }

    console.log(formData)
  }

}

const backgroundColorChange = (value) => {
  formData.fontColor = value
}

const handleChooseFile = (type) => {
  chooseFile((file) => {
    formData.iconUrl = file.fileUrl
  }, {
    optPreChekck: defaultOptPreChekck
  })
}

const updateBlock = () => {
  if (!formData.title) return useMessage('error', t(`insert.fold.error`))

  const { iconUrlGroup, ...rest } = formData
  updateAttributes({
    ...rest,
  })
  dialogVisible = false
}

const backgroundDelclick = () => {
  formData.iconUrl = ''
}

const titleCss = computed(() => {
  return {
    color: node.attrs.fontColor,
    fontSize: `${node.attrs.fontSize}px`,
    fontWeight: node.attrs.isStrong ? 'bold' : 'normal',
    fontFamily: node.attrs.fontFamily,
  }
})
const usedFonts = $ref<string[]>([])
const $recent = useState('recent')
const allFonts = computed(() => {
  const all = [
    {
      label: t('base.fontFamily.all'),
      children: options.value.dicts?.fonts ?? [],
    },
  ]
  // 通过字体值获取字体列表
  const getFontsByValues = (values: string[]) => {
    return values.map(
      (item) =>
        options.value.dicts?.fonts.find(
          ({ value }: { value: string }) => value === item,
        ) ?? {
          label: item,
          item,
        },
    )
  }
  if ($recent.value.fonts.length > 0) {
    all.unshift({
      label: t('base.fontFamily.recent'),
      children: getFontsByValues($recent.value.fonts) as any,
    })
  }
  if (usedFonts.length > 0) {
    all.unshift({
      label: t('base.fontFamily.used'),
      children: getFontsByValues(usedFonts) as any,
    })
  }
  return all
})


const showIcon = ref(false)
const changeIcon = (e) => {

  if (e === 'other') {
    showIcon.value = true
    formData.iconUrl = ''
  } else {
    formData.iconUrl = e
    showIcon.value = false
  }
}


const fontFamilyChange = (fontFamily: string) => {
  if (fontFamily) {
    formData.fontFamily = fontFamily
  }
}


const iconColorChange = (value) => {
  formData.iconColor = value
}




</script>

<style lang="less" scoped>
.umo-node-view__content {
  width: 100%;

  ::v-deep(.umo-node-audio) {
    margin-bottom: 16px;
  }

  .umo-node-view-extended-reading {
    position: relative;
    line-height: 30px;
    width: 100%;

    .icon {
      position: absolute;
      right: 10px;
      top: 14px;
      z-index: 99;
      font-size: 24px;
      cursor: pointer;
      background-color: rgba(0, 0, 0, 0.5);
      border-radius: 4px;
      color: #fff;
      padding: 5px;
      width: 18px;
      height: 18px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  .extended-reading-template-bg {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;

    .extended-left {
      display: flex;
      align-items: center;

      .extended-icon {
        width: 24px;
        height: 24px;

        img {
          width: 100%;
          height: 100%;
        }
      }

      .extended-title {
        padding: 0 10px;
        font-size: 16px;
        font-weight: 400;
      }

      .extended-name {
        color: var(--umo-content-text-color);
        margin-left: 10px;
        width: 450px;
        font-size: 16px;
        white-space: nowrap;
        /* 确保文本在一行内显示 */
        overflow: hidden;
        /* 超出容器部分的文本隐藏 */
        text-overflow: ellipsis;
        /* 使用省略号表示被截断的文本 */
      }
    }

    .extended-right {
      color: #666;
      font-size: 16px;
    }
  }

  .extended-reading-template-content {
    padding: 20px 16px;
    background-color: #f6f6f6;
    border: 1px solid #ebebeb;

    ::v-deep(.umo-node-view-rc) {
      margin-bottom: 16px;
    }

    ::v-deep(.umo-node-view) {
      margin-bottom: 16px;
    }

    ::v-deep(p) {
      margin-bottom: 16px;
    }

    ::v-deep(div>ol) {
      margin-bottom: 16px;
    }

    ::v-deep(div > ol > li >p) {
      margin-bottom: 0;
    }

  }
}

.block-img {
  width: 30px;
  height: 30px;
  position: relative;
  cursor: pointer;
  margin-left: 30px;

  img {
    width: 100%;
    height: 100%;
  }

  .block-delect {
    width: 30px;
    height: 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
  }
}

.bubble-icon {
  display: inline-block;
  min-width: 20px;
  /* 保持宽高比 */
}
</style>
