<template>
  <node-view-wrapper :id="node.attrs.id" ref="containerRef" class="umo-node-view" @mouseenter="showChild"
    @mouseleave="cannelHideLayer">
    <div class="jointHeaderbg" :style="style">
      <div v-show="mousemove" class="icon" @click.stop="keepOpen">
        <t-dropdown class="rc-icon" :tooltip="false" trigger="click">
          <EllipsisIcon />
          <template #dropdown>
            <t-dropdown-item :value="1">
              <div style="display: flex; align-items: center" @click="openSetting">
                <Edit2Icon />
                <span style="margin-left: 5px">{{
                  t('jointHeader.setting')
                }}</span>
              </div>
            </t-dropdown-item>
            <t-dropdown-item :value="2">
              <div style="display: flex; align-items: center" @click="deleteNode">
                <DeleteIcon />
                <span style="margin-left: 5px">{{
                  t('insert.jointHeader.delete')
                }}</span>
              </div>
            </t-dropdown-item>
          </template>
        </t-dropdown>
      </div>
      <div v-show="mousemove" class="toolbar">
        <t-tag theme="warning">节头不能与标题级别一同使用</t-tag>
      </div>
      <!--  -->
      <div class="jointText"
        :style="`padding: ${node.attrs.jointPaddingTop}px ${node.attrs.jointPaddingRight}px ${node.attrs.jointPaddingBottom}px ${node.attrs.jointPaddingLeft}px`">
        <node-view-content />
      </div>
    </div>
    <t-dialog v-model:visible="settingVisible" attach="body" :header="t('jointHeader.setting')" width="32%"
      :close-on-overlay-click="false" @confirm="onSubmit" @close="settingVisible = false"
      @cancel="settingVisible = false">
      <div>
        <t-form ref="formRef" :data="form" :label-width="120">
          <!-- 目录层级-->
          <t-form-item required-mark :label="t('jointHeader.level')" name="level" :rules="[
            { required: true, message: t('jointHeader.levelRequired') },
            {
              validator: (val) => val >= 1 && val <= 6,
              message: t('jointHeader.levelMax'),
            },
          ]">
            <t-input-number v-model="form.level" :step="1" :max="6" :min="1"></t-input-number>
          </t-form-item>
          <!-- 图片地址-->
          <t-form-item :label="t('jointHeader.jointHeaderUrl')" name="jointHeaderUrl">
            <t-image v-if="form.jointHeaderUrl" :src="form.jointHeaderUrl" style="cursor: pointer" @click="changeImg" />

            <t-button v-else @click="changeImg">
              {{ t('jointHeader.upload') }}
            </t-button>
          </t-form-item>
          <!-- 图片高度-->
          <t-form-item :label="t('jointHeader.jointHeight')" :rules="[{ min: 0, message: t('jointHeader.heightMax') }]"
            name="jointHeight">
            <t-input-number v-model="form.jointHeight" :step="1" :min="0"></t-input-number>
          </t-form-item>
          <!-- 图片内边距 -->
          <div class="background-bg">
            <div class="background-bg-title">{{ t('chapterHeader.chapterHeaderPadding') }}</div>
            <div class="padding-bg-icon">
              <t-tooltip :content="t('insert.block.tip')" :overlay-style="{ width: '200px' }" placement="right"
                show-arrow>
                <div class="raduis-bg-icon" @click="paddingClick">
                  <LinkIcon size="30" :color="paddingLinkValue ? '#1890ff' : ''" />
                </div>
              </t-tooltip>
            </div>
            <t-row :gutter="20" style="margin:0;">
              <t-col :span="3">
                <t-form-item :label="t('chapterHeader.top')" name="jointPaddingTop" labelAlign="top">
                  <t-input-number v-model="form.jointPaddingTop" theme="normal" :step="1" :min="0">
                    <template #suffix>
                      <span>px</span>
                    </template>
                  </t-input-number>
                </t-form-item>
              </t-col>
              <t-col :span="3">
                <t-form-item :label="t('chapterHeader.right')" name="jointPaddingRight" labelAlign="top">
                  <t-input-number v-model="form.jointPaddingRight" theme="normal" :step="1" :min="0">
                    <template #suffix>
                      <span>px</span>
                    </template>
                  </t-input-number>
                </t-form-item>
              </t-col>
              <t-col :span="3">
                <t-form-item :label="t('chapterHeader.bottom')" name="jointPaddingBottom" labelAlign="top">
                  <t-input-number v-model="form.jointPaddingBottom" theme="normal" :step="1" :min="0">
                    <template #suffix>
                      <span>px</span>
                    </template>
                  </t-input-number>
                </t-form-item>
              </t-col>
              <t-col :span="3">
                <t-form-item :label="t('chapterHeader.left')" name="jointPaddingLeft" labelAlign="top">
                  <t-input-number v-model="form.jointPaddingLeft" theme="normal" :step="1" :min="0">
                    <template #suffix>
                      <span>px</span>
                    </template>
                  </t-input-number>
                </t-form-item>
              </t-col>
            </t-row>
          </div>
        </t-form>
        <div style="padding: 20px 0">
          <t-alert theme="info" message="节头图片建议尺寸宽度为800px~500px,高度为300px~150px" />
        </div>
      </div>
    </t-dialog>
  </node-view-wrapper>
</template>
<script setup lang="ts">
import { NodeViewContent, nodeViewProps, NodeViewWrapper } from '@tiptap/vue-3'
import { DeleteIcon, Edit2Icon, EllipsisIcon, LinkIcon } from 'tdesign-icons-vue-next'

import { useMouseOver } from '@/hooks/mouseOver'
import { chooseFile, defaultOptPreChekck } from '@/utils/file'

const { node, updateAttributes, deleteNode } = defineProps(nodeViewProps)
const { options, editor, templateObject } = useStore()
const { pageTemplateId } = useTemplate()
const { cannelHideLayer, showChild, mousemove, keepOpen } = useMouseOver()
const paddingLinkValue = ref(node.attrs.paddingLinkValue)
let settingVisible = $ref(false)
let template = $ref(null)
const formRef = ref()
const form = ref({
  jointHeight: 150,
})

onMounted(() => {
  template = pageTemplateId.value
})

const style = computed(() => {
  const style = {
    background: `url(${template?.jointHeaderUrl}) no-repeat`,
    backgroundSize: '100% 100%',
    height: `${template?.jointHeight / 2.5}px`,
    theme: template?.theme,
    color: template?.jointFontColor
  }
  if (node.attrs.jointHeaderUrl != null) {
    style.background = `url(${node.attrs.jointHeaderUrl}) no-repeat`
  }
  if (node.attrs.jointHeight != null) {
    style.height = `${node.attrs.jointHeight / 2.5}px`
  }
  return style
})

watch(
  () => pageTemplateId.value,
  (val) => {
    template = val
  },
)


const paddingClick = () => {
  paddingLinkValue.value = !paddingLinkValue.value
  watch(() => form.value.jointPaddingTop, (value) => {
    if (paddingLinkValue.value) {
      form.value.jointPaddingLeft = value
      form.value.jointPaddingRight = value
      form.value.jointPaddingBottom = value
    }
  })
  paddingLinkValue.value ? useMessage('success', '内边距已联动') : useMessage('error', '内边距已取消联动')
}


// 打开设置
const openSetting = () => {
  form.value = {
    level: node.attrs.level,
    jointHeaderUrl: node.attrs.jointHeaderUrl,
    jointHeight:
      node.attrs.jointHeight == null
        ? template?.jointHeight
        : node.attrs.jointHeight,
    jointPaddingTop: node.attrs.jointPaddingTop,
    jointPaddingRight: node.attrs.jointPaddingRight,
    jointPaddingBottom: node.attrs.jointPaddingBottom,
    jointPaddingLeft: node.attrs.jointPaddingLeft,

  }
  settingVisible = true
}

// 更换图片
const changeImg = () => {
  chooseFile((file) => {
    form.value.jointHeaderUrl = file.fileUrl
  }, {
    optPreChekck: defaultOptPreChekck
  })
}

const onSubmit = async () => {
  const validateResult = await formRef.value.validate()
  if (validateResult === true) {
    updateAttributes({
      level: form.value.level,
      jointHeaderUrl: form.value.jointHeaderUrl,
      jointHeight: form.value.jointHeight == 0 ? 150 : form.value.jointHeight,
      jointPaddingTop: form.value.jointPaddingTop,
      jointPaddingRight: form.value.jointPaddingRight,
      jointPaddingBottom: form.value.jointPaddingBottom,
      jointPaddingLeft: form.value.jointPaddingLeft,

    })
    settingVisible = false
  }
}
</script>
<style lang="less" scoped>
.jointHeaderbg {
  width: 100%;
  display: flex;
  align-items: center;

  .jointText {
    width: 100%;
    word-wrap: break-all;
    overflow: hidden;
  }

  .icon {
    position: absolute;
    right: 10px;
    top: 14px;
    z-index: 99;
    font-size: 24px;
    cursor: pointer;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 4px;
    color: #fff;
    padding: 5px;
    width: 18px;
    height: 18px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .toolbar {
    position: absolute;
    right: 48px;
    top: 5px;
  }
}

.footer-btn {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;

  button {
    margin-left: 10px;
  }
}

.background-bg {
  margin-top: 30px;
  padding: 20px;
  border-radius: 10px;
  border: 1px solid #e5e5e5;
  position: relative;

  .background-bg-title {
    position: absolute;
    top: -15px;
    left: 10px;
    background: #fff;
    padding: 0 10px;
    color: #333;
    font-size: 16px;
  }

  .padding-bg-icon {
    position: absolute;
    left: 0px;
    top: 35%;
    transform: rotate(135deg);
    cursor: pointer;
  }
}
</style>
