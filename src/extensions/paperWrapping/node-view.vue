<template>
  <node-view-wrapper :id="node.attrs.id" ref="containerRef" class="umo-node-view" @mouseenter="showChild"
    @mouseleave="cannelHideLayer" style="
      padding: 0;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      margin: 0;
    ">
    <div class="icon" v-show="mousemove" @click.stop="keepOpen">
      <t-dropdown class="rc-icon" :tooltip="false" trigger="click">
        <EllipsisIcon />
        <template #dropdown>
          <t-dropdown-item :value="1">
            <div style="display: flex; align-items: center" @click="changeFile">
              <FileImportIcon />
              <span style="margin-left: 5px">{{
                t('rc.menu.changeFile')
              }}</span>
            </div>
          </t-dropdown-item>
          <t-dropdown-item :value="2">
            <div style="display: flex; align-items: center" @click="delRC">
              <DeleteIcon />
              <span style="margin-left: 5px">{{ t('rc.menu.delete') }}</span>
            </div>
          </t-dropdown-item>
        </template>
      </t-dropdown>
    </div>
    <img :src="node.attrs.src" alt="" :style="`width:100%;`" />
  </node-view-wrapper>
</template>

<script setup lang="ts">
import { nodeViewProps, NodeViewWrapper } from '@tiptap/vue-3'
import {
  DeleteIcon,
  EllipsisIcon,
  FileImportIcon,
} from 'tdesign-icons-vue-next'
import { chooseFile, defaultOptPreChekck } from '@/utils/file'
const { node, updateAttributes } = defineProps(nodeViewProps)
const { editor } = useStore()
let mousemove = $ref(false)
let isMenuActive = $ref(false)
let hideTimer = null
const changeFile = () => {
  chooseFile((file) => {
    updateAttributes({
      src: file.fileUrl,
      name: file.originName,
      size: file.fileSize,
    })
  }, {
    optPreChekck: defaultOptPreChekck
  })
}

const handleClickOutside = (e) => {
  if (!e.target.closest('.top-node-mu')) {
    mousemove = false
    isMenuActive = false
  }
}


onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
const cannelHideLayer = () => {
  hideTimer = setTimeout(() => {
    if (!isMenuActive) {
      mousemove = false
    }
  }, 200)
}

const showChild = () => {
  mousemove = true
  clearTimeout(hideTimer)
}
const keepOpen = () => {
  isMenuActive = true
  clearTimeout(hideTimer)
}

const delRC = () => {
  const element = document.querySelector('.umo-block-menu-hander')
  if (element) {
    element.style.display = 'block'
  }
  editor.value?.chain().focus().deleteSelection().run()
}
</script>
<style lang="less" scoped>
.icon {
  position: absolute;
  right: 10px;
  top: 14px;
  z-index: 99;
  font-size: 24px;
  cursor: pointer;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 4px;
  color: #fff;
  padding: 5px;
  width: 18px;
  height: 18px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.rc-main {
  display: flex;
  align-items: center;
  padding: 13px 16px;

  .rc-icon {
    width: 18px;
    height: 18px;
    background-color: #333;
    background-size: contain;
  }

  .rc-h1 {
    font-weight: 500;
    font-size: 16px;
    color: #333;
    margin: 0 10px 0 10px;
  }

  .rc-title {
    color: #0966b4;
    font-size: 16px;
  }

  .rc-btn-group {
    margin-left: 20px;

    .rc-btn {
      background: linear-gradient(180deg, #fcfeff 0%, #9bd4ff 100%);
      box-shadow: 0px 4px 4px 0px rgba(5, 86, 154, 0.28);
      border-radius: 57px 57px 57px 57px;
      border: 1px solid rgba(9, 102, 180, 0.5);
      color: #0966b4;
      border-radius: 57px;
      padding: 5px 20px;
      cursor: pointer;
      margin-top: 0px;
    }
  }
}
</style>
