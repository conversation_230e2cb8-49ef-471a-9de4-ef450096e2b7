import {
  type CommandProps,
  mergeAttributes,
  Node,
  type NodeViewProps,
} from '@tiptap/core'
import { VueNodeViewRenderer } from '@tiptap/vue-3'

import { SURROUND } from '../page/node-names'
import NodeView from './node-view.vue'
declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    setSurround: {
      setSurround: (options: any) => ReturnType
    }
  }
}
export default Node.create({
  name: SURROUND,
  group: 'block',
  content: 'block+',
  addAttributes() {
    return {
      id: {
        default: null,
      },
      // 背景图
      surroundType: {
        default: '',
      },
      spacing: {
        default: '',
      },
      imageUrl: {
        default: '',
      },
      text: {
        default: '',
      },
      color: {
        default: '',
      },
      width: {
        default: null,
      },
      height: {
        default: 100,
      },
      left: {
        default: 0,
      },
      top: {
        default: 0,
      },
      angle: {
        default: null,
      },
      flipX: {
        default: false,
      },
      flipY: {
        default: false,
      },
      equalProportion: {
        default: true,
      },
      isShowImageTitle: {
        default: 1,
      },
      isShowNo: {
        default: 1,
      },
      number: {
        default: '',
      },
      titleFamily: {
        default: '',
      },
      titleSize: {
        default: '18',
      },
    }
  },
  parseHTML() {
    return [{ tag: SURROUND }]
  },
  renderHTML({ HTMLAttributes }) {
    return [
      SURROUND,
      mergeAttributes(this.options.HTMLAttributes, HTMLAttributes),
      0,
    ]
  },
  addNodeView() {
    return VueNodeViewRenderer(NodeView as Component<NodeViewProps>)
  },
  addCommands() {
    return {
      setSurround:
        (options) =>
        ({ commands, editor, dispatch, tr }: CommandProps) => {
          console.log(options)
          if (dispatch) {
            tr.replaceRangeWith(
              editor.state.selection.anchor,
              editor.state.selection.anchor,
              editor.schema.node(
                this.name,
                {
                  ...options,
                },
                editor.schema.nodes.paragraph.create(),
              ),
            )
          }
          return true
        },
    }
  },
})
