<template>
  <node-view-wrapper :id="node.attrs.id" ref="containerRef" class="umo-node-view-rc">
    <div @mouseenter="showChild" @mouseleave="cannelHideLayer">
      <div v-show="mousemove" class="top-node-mu" @click.stop="keepOpen">
        <t-dropdown class="rc-icon" :tooltip="false" trigger="click">
          <EllipsisIcon />
          <template #dropdown>
            <t-dropdown-item :value="2">
              <div style="display: flex; align-items: center" @click="previewQuiz(node)">
                <FileSearchIcon />
                <!-- 预览按钮 -->
                <span style="margin-left: 5px">{{ t('editor.preview') }}</span>
              </div>
            </t-dropdown-item>
            <t-dropdown-item v-if="paperType == '2'" :value="5">
              <div style="display: flex; align-items: center" @click.stop="onOpen">
                <ComponentSwitchIcon />

                <span style="margin-left: 5px">{{
                  t('insert.schoolAssignment.isExpand')
                }}</span>
              </div>
            </t-dropdown-item>
            <t-dropdown-item :value="3">
              <div style="display: flex; align-items: center" @click="editQuiz">
                <EditIcon />
                <!-- 编辑试卷按钮 -->
                <span style="margin-left: 5px">{{
                  paperType == '1'
                    ? t('insert.testPaper.editPaper')
                    : t('insert.schoolAssignment.editPaper')
                }}</span>
              </div>
            </t-dropdown-item>
            <t-dropdown-item :value="4">
              <div style="display: flex; align-items: center" @click="handelDelNode">
                <DeleteIcon />
                <!-- 删除按钮 -->
                <span style="margin-left: 5px">{{
                  t('insert.questions.delBtnText')
                }}</span>
              </div>
            </t-dropdown-item>
          </template>
        </t-dropdown>
      </div>
      <div class="extended-reading-template-bg"
        :style="`background: url(${template?.orderTemplateBgUrl ?? node.attrs.bgSrc}) no-repeat center;height:55px;background-size:100% 100%;`">
        <div class="extended-left">
          <div class="extended-icon">
            <img
              :src="node.attrs.type == 'paper' ? template?.theme === 'light' ? papersDark : papers : template?.theme == 'light' ? testPaperDark : testPaper"
              class="icon-file" />
          </div>
          <div class="extended-title"
            :style="{ color: template?.theme === 'light' ? '#fff' : 'var(--umo-content-text-color)' }">{{
              localQuizData.paperTitle }}</div>
        </div>
      </div>
    </div>
  </node-view-wrapper>
  <t-drawer v-model:visible="showEditQuizPopup" :close-btn="true" :footer="false" attach="body" size="80%"
    mode="overlay" placement="right" confirm-btn="更新" :header="paperType == '1'
      ? t('insert.testPaper.editPaper')
      : t('insert.schoolAssignment.editPaper')
      " class="new-question-drawer" :destroy-on-close="true" @cancel="showEditQuizPopup = false">
    <template-paper-editSteps-update v-if="showEditQuizPopup" :type="'edit'" :paper-id="localQuizData.paperId"
      :paper-type="localQuizData.paperType.toString()" class="quiz-update-drawer" @confirm="onQuizDataSave" />
  </t-drawer>

  <t-dialog v-model:visible="testPaperShow" attach="body" :header="t('insert.image.setting')" width="20%"
    :close-on-overlay-click="false" @confirm="onSubmit" @close="onClose" @cancel="onClose">
    <div>是否在阅读器中展开： <t-switch v-model="formData.isExpand" /></div>
  </t-dialog>

  <template-paper-preview v-model="previewQuizVisibility" :paper-id="localQuizData.paperId"
    :paper-type="localQuizData.paperType"></template-paper-preview>
</template>

<script setup>
import { nodeViewProps, NodeViewWrapper } from '@tiptap/vue-3'
import {
  ComponentSwitchIcon,
  DeleteIcon,
  EditIcon,
  EllipsisIcon,
  FileSearchIcon,
} from 'tdesign-icons-vue-next'
import { MessagePlugin } from 'tdesign-vue-next'

import { checkBeforeEdit } from '@/api/book/paper.js'
import papers from '@/assets/icons/papers.svg' //default
import papersDark from '@/assets/icons/papersDark.svg' //200
//200
import testPaper from '@/assets/icons/testPaperAd.svg' //100
import testPaperDark from '@/assets/icons/testPaperAdDark.svg' //100
import { useMouseOver } from '@/hooks/mouseOver'

const { cannelHideLayer, showChild, mousemove, keepOpen } = useMouseOver()
const { pageTemplateId } = useTemplate()
const template = pageTemplateId.value
const { node, updateAttributes, deleteNode } = defineProps(nodeViewProps)
let showEditQuizPopup = $ref(false)
let localQuizData = $ref({})
const formData = $ref({
  isExpand: node.attrs.isExpand,
})
let previewQuizVisibility = $ref(false)
const { paperType } = node.attrs.paperInfo
let testPaperShow = $ref(false)
// 方法部分
function editQuiz(item) {
  // 先调用检测接口
  checkBeforeEdit(localQuizData.paperId)
    .then((response) => {
      if (response.code === 200 && response.data === true) {
        // 可以编辑，跳转到编辑页面
        showEditQuizPopup = true
      } else {
        // 不能编辑，显示提示信息
        MessagePlugin.error('试卷已被已出版的教材使用，不能编辑')
      }
    })
    .catch((error) => {
      console.error('检测编辑失败：', error)
      MessagePlugin.error('试卷已被已出版的教材使用，不能编辑')
    })
}
function previewQuiz() {
  previewQuizVisibility = true
}

const onClose = () => {
  testPaperShow = false
}

const onOpen = () => {
  testPaperShow = true
}

const onSubmit = () => {
  updateAttributes({
    isExpand: formData.isExpand,
  })
  testPaperShow = false
}
function handelDelNode() {
  deleteNode()
}
function onQuizDataSave(quizData) {
  showEditQuizPopup = false
  localQuizData.paperTitle = quizData.paperForm.title
  updateAttributes({
    paperInfo: localQuizData,
  })
}

onMounted(() => {
  localQuizData = JSON.parse(JSON.stringify(node.attrs.paperInfo))
})
</script>

<style lang="less" scoped>
@import '@/assets/styles/_common.less';

.top-node-mu {
  margin-bottom: -10px;
  width: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.questions-list {
  .question-collection {
    margin-bottom: 24px;

    .collection-header {
      margin-bottom: 16px;

      h3 {
        color: #303133;
        font-size: 18px;
        font-weight: 500;
      }
    }
  }

  .question-item {
    margin-bottom: 20px;

    .question-header {
      margin-bottom: 15px;

      .question-index {
        font-size: 16px;
        font-weight: bold;
        color: #303133;
        margin-right: 10px;
      }

      .question-score {
        color: #409eff;
        margin-left: 8px;
        font-size: 14px;
      }
    }
  }
}

.umo-node-view-rc {
  position: relative;

  .top-node-mu {
    position: absolute;
    right: 10px;
    top: 10px;
    z-index: 99;
    font-size: 24px;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 4px;
    padding: 5px;
    color: #fff;
    width: 24px;
    height: 24px;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .extended-reading-template-bg {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;

    .extended-left {
      display: flex;
      align-items: center;

      .extended-icon {
        width: 24px;
        height: 24px;

        img {
          width: 100%;
          height: 100%;
        }
      }

      .extended-title {
        padding: 0 10px;
        font-size: 16px;
        font-weight: 400;
      }

      .extended-name {
        color: var(--umo-content-text-color);
        margin-left: 20px;
        width: 450px;
        white-space: nowrap;
        /* 确保文本在一行内显示 */
        overflow: hidden;
        /* 超出容器部分的文本隐藏 */
        text-overflow: ellipsis;
        /* 使用省略号表示被截断的文本 */
      }
    }

    .extended-right {
      color: #666;
      font-size: 16px;
    }
  }

  .extended-reading-template-content {
    background-color: #f6f6f6;
    border: 1px solid #ebebeb;
    padding: 20px 16px;

  }
}

ul {
  list-style: none;
  padding: 0;
}

li {
  margin: 4px 0;
}

label {
  cursor: pointer;
}
</style>
<style lang="less">
.bellCss .umo-node-view-rc * {
  text-indent: 0;
}

.bellCss .umo-node-view-rc .question-item {
  .data-item {
    padding: 20px 10px;
    font-size: 14px;

    label {
      min-width: fit-content;
    }
  }
}
</style>
