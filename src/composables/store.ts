import type { Mark } from '@tiptap/pm/model'
import type { Editor } from '@tiptap/vue-3'
import type { TableOfContentDataItem } from '@tiptap-pro/extension-table-of-contents'
import { isRecord } from '@tool-belt/type-predicates'

import { queryCaptionStyle } from '@/api/book/book.js'
import { listUserCommon } from '@/api/book/userCommon'
import { codeLogin, getInfo, login, logout } from '@/api/login'
import { defaultOptions, ojbectSchema } from '@/options'
import { getToken, removeToken, setToken } from '@/request/token'
import type { PageOption, UmoEditorOptions } from '@/types'
import { shortId } from '@/utils/short-id'
export type TableOfContentItem = TableOfContentDataItem
export const useStore = createGlobalState(() => {
  const toolbarKey = ref<string>(shortId())
  const options = ref<UmoEditorOptions>(defaultOptions)
  const page = ref<PageOption>(defaultOptions.page)
  const contentText = ref<string>()
  const editor = ref<Editor>()

  const downloadWordDialog = ref<boolean>(false)
  const painter = ref<{
    enabled: boolean
    once: boolean
    marks: Mark[]
  }>({
    enabled: false,
    once: true,
    marks: [],
  })
  const bubbleVisible = ref(false)
  const blockMenu = ref(false)
  const assistantBox = ref(false)
  const commentBox = ref(false)
  const foldTip = ref(true)
  const tableOfContents = ref<TableOfContentItem[]>([])
  const imageViewer = ref({
    visible: false,
    current: null,
  })
  const searchReplace = ref(false)
  const settingsShow = ref(false) //控制设置弹出框显示
  const loading = ref(false)
  const savedAt = ref<number | null>(null)
  const printing = ref(false)
  const exportImage = ref(false)
  const exportPDF = ref(false)
  const hidePageHeader = ref(true)
  const hidePageFooter = ref(true)
  const editorDestroyed = ref(false)
  const chapterId = ref('')
  const bookId = ref('')
  const editorUpdateCallback: any = reactive({})
  const token = ref(getToken())
  const commentList = ref([])
  const captionStyleInfo = ref(null)
  const nickName = ref('')
  const userName = ref('')
  const avatar = ref('')
  const roleKey = ref(null)
  const templateObject = ref({})
  const pageLineObj = ref({
    switchValue: true,
    backgroundColor: 'red',
    width: 10,
  })

  const gridPatternObj = ref({
    switchValue: false,
    backgroundColor: '#ddd',
    size: 30,
  })
  const setChapterId = (val: any) => {
    chapterId.value = val
  }

  const setBookId = (val: any) => {
    bookId.value = val
  }

  const setFoldTip = (val: any) => {
    console.log('🚀 ~ useStore ~ setFoldTip:', val)
    foldTip.value = val
  }
  const setContentText = (value: any) => {
    contentText.value = value?.html
  }

  const LoginCode = (value: any) => {
    const { username, password, code, uuid } = value
    return new Promise((resolve, reject) => {
      login(username, password, code, uuid)
        .then((res) => {
          setToken(res.data.access_token)
          token.value = res.data.access_token
          resolve(res)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  const CodeLogin = (userInfo) => {
    return new Promise((resolve, reject) => {
      codeLogin(userInfo)
        .then((res) => {
          const { data } = res
          setToken(data.access_token)
          token.value = data.access_token
          resolve()
        })
        .catch((error) => {
          reject(error)
        })
    })
  }

  const batchInsert = (
    dataList,
    type,
    fn,
    isInsertParagraph,
    paragraphContent,
  ) => {
    const { state, dispatch } = editor.value?.view
    const { schema } = state
    let { tr } = state
    let currentPos = tr.selection.$head.pos
    // console.log("🚀 ~ useStore ~ currentPos:", currentPos)
    for (const item of dataList) {
      let wrapperOfChildren = null
      if (isInsertParagraph) {
        wrapperOfChildren = schema.nodes.paragraph.create(
          '',
          schema.text(paragraphContent(item)),
        )
      }
      const wrapper = schema.node(type, fn(item), wrapperOfChildren)
      // 选区为空p标签，则直接替换
      if (
        tr.selection.empty &&
        tr.selection?.$head?.parent?.type?.name === 'paragraph' &&
        tr.selection?.$head?.parent?.content.size === 0
      ) {
        // console.log("替换")
        tr = tr.replaceWith(currentPos - 1, currentPos + 1, wrapper)
      } else {
        // console.log("插入")
        tr = tr.insert(currentPos, wrapper)
      }

      currentPos += wrapper.nodeSize
    }
    dispatch(tr.scrollIntoView())
  }

  const getUserInfo = () => {
    return new Promise((resolve, reject) => {
      getInfo().then((res) => {
        nickName.value = res.user.nickName
        userName.value = res.user.userName
        roleKey.value = res.roles[0]
        avatar.value =
          res.user.avatar ||
          'https://dutp-test.oss-cn-beijing.aliyuncs.com/1743412473588.png'
        resolve()
      })
    })
  }

  const LoginOut = () => {
    return new Promise((resolve, reject) => {
      logout(token.value)
        .then((res) => {
          token.value = null
          removeToken()
          resolve()
        })
        .catch((err) => {
          reject(err)
        })
    })
  }

  const getCaptionStyle = async () => {
    if (!captionStyleInfo.value) {
      const res = await queryCaptionStyle({ bookId: bookId.value })
      captionStyleInfo.value = res.data
    }
    return captionStyleInfo.value
  }

  // 获取备注列表
  const getCommentList = () => {
    return new Promise((resolve, reject) => {
      listUserCommon({ chapterId: chapterId.value })
        .then((res) => {
          commentList.value = res.data
          resolve()
        })
        .catch((err) => {
          reject(err)
        })
    })
  }

  const setOptions = (value: unknown) => {
    const opts =
      isRecord(value) && Object.keys(value).includes('value')
        ? value.value
        : value

    options.value = ojbectSchema.merge(
      options.value,
      Object.keys(opts).reduce<Record<string, unknown>>(
        (acc: Record<string, unknown>, key: string) => {
          if (opts[key] !== undefined) {
            acc[key] = opts[key]
          }
          return acc
        },
        {},
      ),
    )
    const $locale = useState('locale')
    if (!$locale.value) {
      $locale.value = options.value.locale
    }
    return options.value
  }

  const editorUpdate = (
    key?: string,
    callback?: () => void,
    immediate?: boolean,
  ) => {
    if (callback && key) {
      editorUpdateCallback[key] = callback
      if (immediate) callback && callback()
    } else {
      Object.values(editorUpdateCallback).forEach((fun: any) => {
        fun?.()
      })
    }
  }

  const setPainter = ({
    enabled,
    once,
    marks,
  }: {
    enabled: boolean
    once: boolean
    marks: Mark[]
  }) => {
    painter.value.enabled = enabled
    painter.value.once = once
    painter.value.marks = marks
  }
  const togglePopup = (visible?: boolean) => {
    bubbleVisible.value = !!visible
  }

  watch(
    () => options.value.page,
    ({
      defaultBackground,
      defaultMargin,
      defaultOrientation,
      watermark,
    }: PageOption) => {
      page.value = {
        size: options.value.dicts?.pageSizes.find(
          (item: { default: boolean }) => item.default,
        ),
        margin: defaultMargin,
        background: defaultBackground,
        orientation: defaultOrientation,
        watermark,
        header: true,
        footer: true,
        showLineNumber: false,
        showToc: true,
        pagination: true,
        zoomLevel: 100,
        autoWidth: false,
        preview: {
          enabled: false,
          laserPointer: true,
        },
      }
    },
    { immediate: true, once: true },
  )

  // watch(
  //   () => [page.value.size, page.value.margin, page.value.orientation],
  //   () => {
  //     editor.value?.commands.autoPaging(false)
  //     changeComputedHtml()
  //     setTimeout(() => {
  //       editor.value?.commands.autoPaging(true)
  //     }, 1000)
  //   },
  //   { deep: true },
  // )

  const setEditor = (editorInstance: Editor) => {
    editor.value = editorInstance
  }
  const resetStore = () => {
    editor.value = undefined
    tableOfContents.value = []
    searchReplace.value = false
    savedAt.value = null
    editorDestroyed.value = true
  }

  const getTemplateObject = (item) => {
    templateObject.value =
      item ?? JSON.parse(localStorage.getItem('templateObject'))
  }

  const getPageLineObj = (item) => {
    console.log('getPageLineObj', item)
    pageLineObj.value = item
    console.log('pageLineObj.value', pageLineObj.value)
  }

  const getGridPattern = (item) => {
    gridPatternObj.value = item
  }
  watch(
    () => options.value.document?.readOnly,
    (val: boolean | undefined) => {
      editor.value?.setEditable(!val)
      toolbarKey.value = shortId()
    },
  )

  watch(
    () => bubbleVisible.value,
    (val) => {
      bubbleVisible.value = val
    },
  )

  watch(
    () => settingsShow.value,
    (val) => {
      settingsShow.value = val
    },
  )

  return {
    toolbarKey,
    container: `#umo-editor-${shortId(4)}`,
    options,
    contentText,
    page,
    editor,
    painter,
    blockMenu,
    assistantBox,
    commentBox,
    tableOfContents,
    imageViewer,
    searchReplace,
    settingsShow,
    savedAt,
    printing,
    exportImage,
    exportPDF,
    hidePageHeader,
    hidePageFooter,
    editorDestroyed,
    bubbleVisible,
    chapterId,
    bookId,
    togglePopup,
    setOptions,
    setEditor,
    setPainter,
    resetStore,
    setContentText,
    LoginCode,
    setChapterId,
    setBookId,
    loading,
    downloadWordDialog,
    editorUpdate,
    getUserInfo,
    nickName,
    userName,
    avatar,
    LoginOut,
    token,
    CodeLogin,
    getCommentList,
    getTemplateObject,
    getPageLineObj,
    getCaptionStyle,
    commentList,
    captionStyleInfo,
    templateObject,
    pageLineObj,
    batchInsert,
    getGridPattern,
    gridPatternObj,
    roleKey,
    foldTip,
    setFoldTip,
  }
})
