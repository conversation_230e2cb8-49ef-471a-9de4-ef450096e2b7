@import '@/assets/styles/_mixins.less';
//仿宋
@font-face {
  font-family: 'FZFangSong-Z02S';
  src: url('https://dutp-dtb-res.oss-cn-beijing.aliyuncs.com/fonts/FZFSJW.TTF')
    format('truetype');
  font-display: swap;
}

//宋体
@font-face {
  font-family: 'FZSSJW';
  src: url('https://dutp-dtb-res.oss-cn-beijing.aliyuncs.com/fonts/FZSSJW.TTF')
    format('truetype');
  font-display: swap;
}

//隶书
@font-face {
  font-family: 'FZHLK';
  src: url('https://dutp-dtb-res.oss-cn-beijing.aliyuncs.com/fonts/FZHLK.TTF')
    format('truetype');
  font-display: swap;
}

//楷体
@font-face {
  font-family: 'FZKTJW';
  src: url('https://dutp-dtb-res.oss-cn-beijing.aliyuncs.com/fonts/FZKTJW.TTF')
    format('truetype');
  font-display: swap;
}

//黑体
@font-face {
  font-family: 'FZHTJW';
  src: url('https://dutp-dtb-res.oss-cn-beijing.aliyuncs.com/fonts/FZHTJW.TTF')
    format('truetype');
  font-display: swap;
}

.umo-editor-container {
  flex: 1;
  width: 100%;

  @media print {
    overflow: auto;
    height: auto;
  }

  &.is-empty {
    .umo-page-node-content > :first-child {
      &::after {
        content: var(--umo-editor-placeholder);
        color: var(--umo-content-placeholder-color);
        position: absolute;
        top: 0;
        cursor: text;
      }
    }
  }

  &.show-line-number {
    .umo-page-node-content {
      > *:not([data-line-number='false']):not(div):not(.umo-node-focused) {
        position: relative;

        &::before {
          position: absolute;
          content: counter(title) '.';
          counter-increment: title;
          right: 100%;
          margin-right: 20px;
          color: var(--umo-content-line-number-color);
          font-family: var(--umo-font-family);
          font-size: 14px;
          font-weight: 400;
        }
      }
    }
  }

  &.format-painter {
    cursor: url('@/assets/images/format-painter.svg'), text;
  }

  .umo-editor {
    height: 100%;
    width: 100%;
    outline: none;
    counter-reset: title;
    color: var(--umo-content-text-color);

    @media print {
      height: auto;
      width: auto;
    }

    .umo-page-node-content {
      > * {
        z-index: 1;
        + * {
          margin-top: var(--umo-content-node-bottom);
        }

        &.umo-node-focused {
          &:not(hr):not(.tableWrapper):not(table):before {
            content: '';
            display: block;
            position: absolute;
            left: -8px;
            top: -5px;
            right: -8px;
            bottom: -5px;
            border-radius: var(--umo-radius);
            background: var(--umo-content-node-selected-background);
            pointer-events: none;
            z-index: -1;
          }

          &:is(div):not(.umo-node-view-empty) {
            &::before {
              top: -8px !important;
              bottom: -8px !important;
            }
          }
          &.umo-node-view-empty {
            &::before {
              top: 3px !important;
              bottom: auto;
              height: 2px;
              left: 0 !important;
              right: 0 !important;
              background: var(--umo-primary-color) !important;
            }
          }

          &.umo-page-divider {
            border-radius: var(--umo-radius);
            background: var(--umo-content-node-selected-background);
            margin-left: -8px;
            margin-right: -8px;
            padding-left: 8px;
            padding-right: 8px;
          }

          .umo-select-outline {
            outline: solid 1px var(--umo-primary-color);
          }
        }
      }
    }

    .is-editor-empty {
      &:first-child {
        > * {
          display: none;
        }

        &::before {
          color: var(--umo-content-placeholder-color);
          content: attr(data-placeholder);
          float: left;
          height: 0;
          pointer-events: none;
        }
      }
    }
  }

  .umo-text-selection {
    background-color: var(--umo-text-selection-background);
  }

  ::selection {
    background-color: var(--umo-text-selection-background);
  }

  a {
    color: var(--umo-primary-color);
    text-decoration: none;
    cursor: pointer;

    &:hover {
      opacity: 0.8;
      text-decoration: underline;
    }
  }

  code {
    color: var(--umo-content-code-color);
    background-color: var(--umo-content-code-background);
    font-family: var(--umo-content-code-family);
    border-radius: 3px;
    padding: 0.1em 0.4em;
    margin-left: 0.25em;
    margin-right: 0.25em;
  }

  ul,
  ol {
    counter-reset: section;
    list-style-type: none;

    li {
      word-break: break-all;
      list-style-type: none;
      padding: 0.25em 0;
      > p {
        flex-wrap: nowrap;
        overflow-x: auto;
        position: relative;
        padding-left: 30px; /* 为伪元素腾出空间 */
        line-height: 1.5;
        margin-top: var(--umo-content-node-bottom);
        &::before {
          counter-increment: section;
          display: inline;
        }
      }
      // v-deep(p):before {

      // }
      ol,
      ul {
        margin-top: 0.25em;
        padding-left: 1.6em;
      }
    }
  }
  ol {
    &[type='decimal'] li > p:before {
      content: counter(section) '.';
     

      width: 27px;
      position: absolute;
      left: 0;
      top: 0;
      height: 100%;
    }
    &[type='decimal-leading-zero'] li > p:before {
    

      width: 30px;
      position: absolute;
      left: 0;
      top: 0;
      height: 100%;
      content: counter(section, decimal-leading-zero) '.';
    }
    &[type='lower-roman'] li > p:before {
      

      width: 40px;
      position: absolute;
      left: 0;
      top: 0;
      height: 100%;
      content: counter(section, lower-roman) '. ';
      //content: counters(section, '.', lower-roman) '.';
    }
    &[type='upper-roman'] li > p:before {
      position: absolute;
      left: 0;
      top: 0;
      height: 100%;
      width: 40px;
      content: counter(section, upper-roman) '.';
    }
    &[type='lower-latin'] li > p:before {
      width: 25px;
      position: absolute;
      left: 0;
      top: 0;
      height: 100%;
      content: counter(section, lower-latin) '.';
    }
    &[type='upper-latin'] li > p:before {
      width: 25px;
      position: absolute;
      left: 0;
      top: 0;
      height: 100%;
      content: counter(section, upper-latin) '.';
    }
    &[type='trad-chinese-informal'] li > p:before {
      width: 25px;
      position: absolute;
      left: 0;
      top: 0;
      height: 100%;
      content: counter(section, trad-chinese-informal) '.';
    }
    &[type='simp-chinese-formal'] li > p:before {
      width: 25px;
      position: absolute;
      left: 0;
      top: 0;
      height: 100%;
      content: counter(section, simp-chinese-formal) '.';
    }
  }
  ul {
    &[type='disc'] li > p:before {
      content: '•';
      width: 7px;
      position: absolute;
      left: 0;
      top: 0;
      height: 100%;
    }
    &[type='circle'] li > p:before {
      content: '◦';
      width: 7px;
      position: absolute;
      left: 0;
      top: 0;
      height: 100%;
    }
    &[type='square'] li > p:before {
      content: '▪';
      width: 7px;
      position: absolute;
      left: 0;
      top: 0;
      height: 100%;
    }
  }
  // ol li p {
  //   margin-left: 0.25em;
  //   word-break: break-all;
  // }

  blockquote {
    border-left: 3px solid var(--umo-primary-color);
    padding: 0.5em 1em;
    color: #777;
    background-color: rgba(0, 0, 0, 0.03);
    p {
      margin-top: 0;
    }
  }

  table {
    border-collapse: collapse;
    table-layout: fixed;
    width: 100%;
    margin: 0;
    overflow: hidden;
    page-break-inside: auto;

    tr {
      page-break-inside: avoid;
      page-break-after: auto;
    }

    // thead {
    //   display: table-header-group;
    // }
    td,
    th {
      min-width: 1em;
      border: 1px solid var(--umo-content-table-border-color);
      padding: 3px 5px;
      vertical-align: middle;
      box-sizing: border-box;
      position: relative;

      > * {
        margin-bottom: 0;
      }

      &[data-align='left-top'] {
        vertical-align: top;
        text-align: left;
      }

      &[data-align='center-top'] {
        vertical-align: top;
        text-align: center;
      }

      &[data-align='right-top'] {
        vertical-align: top;
        text-align: right;
      }

      &[data-align='justify-top'] {
        vertical-align: top;
        text-align: justify;
      }

      &[data-align='left-middle'] {
        vertical-align: middle;
        text-align: left;
      }

      &[data-align='center-middle'] {
        vertical-align: middle;
        text-align: center;
      }

      &[data-align='right-middle'] {
        vertical-align: middle;
        text-align: right;
      }

      &[data-align='justify-middle'] {
        vertical-align: middle;
        text-align: justify;
      }

      &[data-align='left-bottom'] {
        vertical-align: bottom;
        text-align: left;
      }

      &[data-align='center-bottom'] {
        vertical-align: bottom;
        text-align: center;
      }

      &[data-align='right-bottom'] {
        vertical-align: bottom;
        text-align: right;
      }

      &[data-align='justify-bottom'] {
        vertical-align: bottom;
        text-align: justify;
      }
    }

    th {
      font-weight: bold;
      text-align: left;
      // background-color: var(--umo-content-table-thead-background);
    }

    .selectedCell:after {
      z-index: 2;
      position: absolute;
      content: '';
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      background: var(--umo-content-table-selected-background);
      pointer-events: none;
    }

    .column-resize-handle {
      position: absolute;
      right: -1px;
      top: 0;
      bottom: -1px;
      width: 3px;
      background-color: var(--umo-primary-color);
      pointer-events: none;
    }

    p {
      margin: 0;
    }
  }

  .indent-1 {
    text-indent: 2em;
  }

  .indent-2 {
    text-indent: 4em;
  }

  .indent-3 {
    text-indent: 6em;
  }

  .indent-4 {
    text-indent: 8em;
  }

  .indent-5 {
    text-indent: 10em;
  }

  .tableWrapper {
    max-width: 100%;
    overflow-x: auto;
    .umo-scrollbar();
  }

  .resize-cursor {
    cursor: col-resize;
  }

  .umo-task-list {
    list-style: none;
    padding: 0;

    li {
      display: flex;
      align-items: first baseline;

      &[data-checked='true'] {
        p {
          opacity: 0.5;
          text-decoration: line-through;

          &:has([style]) {
            text-decoration: inherit;
          }

          * {
            text-decoration: line-through;
          }
        }
      }

      > label {
        flex: 0 0 auto;
        margin-right: 10px;
        user-select: none;
        input {
          transform: translateY(0.2em);
        }
      }

      > div {
        flex: 1 1 auto;
      }
    }

    input[type='checkbox'] {
      cursor: pointer;
      font-family: unset;
      color: var(--umo-primary-color);
      border: 1px solid var(--umo-primary-color);
      border-radius: 0;
      margin: 0.25em;
      width: 16px;
      height: 16px;
      padding: 0.1em 0.4em;
      background: white;
      opacity: 0.5;
    }
  }

  .umo-search-result {
    background-color: var(--umo-content-search-result-background);

    &-current {
      background: var(--umo-content-search-result-current-background);
    }
  }

  .umo-page-divider {
    .umo-page-divider();
  }

  .umo-node-view {
    display: flex;
    position: relative;
    max-height: 100%;
    .umo-node-container {
      max-height: 100%;
    }
    .umo-hover-shadow:hover {
      box-shadow: var(--umo-shadow);
    }
  }

  // 插件样式
  .ProseMirror-gapcursor {
    height: 24px;
    display: flex;
    &:after {
      width: 1px;
      height: 16px;
      margin-top: 8px;
      background-color: var(--umo-color-black);
    }
  }

  .Tiptap-mathematics {
    &-editor {
      background: var(--umo-content-node-selected-background);
      font-family: monospace;
      padding: 0.2em 0.5em;
      // outline: solid 1px var(--umo-primary-color);
    }

    &-render {
      cursor: pointer;
      padding: 0 0.25em;
      transition: background 0.2s;

      &:hover {
        background: var(--umo-content-node-selected-background);
      }
    }

    &-editor,
    &-render {
      border-radius: 0.2em;
      display: inline-block;
    }
  }

  .umo-color-highlighter {
    white-space: nowrap;

    &::before {
      background-color: var(--color);
      border: 1px solid rgba(128, 128, 128, 0.3);
      border-radius: 2px;
      content: ' ';
      display: inline-block;
      height: 1em;
      margin-bottom: 0.15em;
      margin-right: 0.1em;
      vertical-align: middle;
      width: 1em;
    }
  }
}

.umo-editor-page-computed {
  width: 100%;
  height: 0;
  position: absolute;
  border: none;
}
.not-adopted-result {
  text-decoration: underline wavy red;
}
.adopted-result {
  text-decoration: underline wavy green;
}

.layout-columns {
  display: flex;
  width: 100%;

  &:hover .column-item {
    outline: 2px dashed #ccc;
    cursor: pointer;

    transition: border-width 0.3s ease; /* 平滑过渡效果 */
  }
  .column-item {
    .bellCss {
      margin: 0;
    }
    .umo-node-view {
      margin: 0;
    }
  }
}
