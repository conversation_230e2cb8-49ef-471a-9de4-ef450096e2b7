:root {
  // 主题色
  --umo-primary-color: #0966b4;
  --td-brand-color: #0966b4 !important;
  --umo-color-white: #fff;
  --umo-color-black: #000;
  // 警示颜色
  --umo-warning-color: #f6913e;
  --umo-error-color: #ef3f35;
  // 背景颜色
  --umo-container-background: #f4f5f7;
  --umo-button-hover-background: #f1f3f5;
  // 文本选中时的背景颜色
  --umo-text-selection-background: #94cfff;

  // 字体相关
  --umo-font-family: FZHTJW;
  --umo-font-size: 1.125rem;
  --umo-font-size-small: 12px;
  --umo-text-color: rgb(0, 0, 0);
  --umo-text-color-light: rgba(0, 0, 0, 0.4);
  --umo-text-color-disabled: #a7abac;

  // 边框
  --umo-shadow:
    0 3px 14px 2px rgba(0, 0, 0, 0.03), 0 8px 10px 1px rgba(0, 0, 0, 4%),
    0 5px 5px -3px rgba(0, 0, 0, 8%);
  --umo-border-color: rgba(0, 0, 0, 0.08);
  --umo-border-color-dark: #ddd;
  --umo-border-color-light: rgba(0, 0, 0, 0.05);

  // 圆角
  --umo-radius: 3px;
  --umo-radius-medium: 5px;

  // 弹出层
  --umo-popup-content-padding: 12px;
  --umo-popup-max-height: max(60vh, 180px);
  --umo-tooltip-content-padding: 6px 10px;
  --umo-mask-color: transparent;

  // 编辑器
  --umo-content-placeholder-color: #999;
  --umo-content-text-color: #000;
  --umo-content-node-border: #e7e7e7;
  --umo-content-node-radius: var(--umo-radius);
  --umo-content-node-bottom: 16px;
  --umo-content-node-selected-background: rgba(0, 0, 0, 0.06);
  --umo-content-table-border-color: #dee0e3;
  --umo-content-table-thead-background: #f1f3f5;
  --umo-content-table-selected-background: rgba(200, 200, 255, 0.4);
  --umo-content-line-number-color: #e7e8ea;
  --umo-content-search-result-background: #fefc7ef2;
  --umo-content-search-result-current-background: #0dff00c3;
  --umo-content-invisible-break-color: rgb(78, 139, 252);
  --umo-content-code-color: var(--umo-primary-color);
  --umo-content-code-background: #f1f3f5;
  --umo-content-code-family:
    Consolas, Monaco, Andale Mono, Ubuntu Mono, monospace;

  // 滚动条
  --umo-scrollbar-size: 4px;
  --umo-scrollbar-thumb-color: rgba(0, 0, 0, 0.2);
  --umo-scrollbar-thumb-hover-color: rgba(0, 0, 0, 0.35);

  // 菜单悬浮效果
  --umo-menu-item-hover-background: #f6f6f6;

  --umo-menu-item-selected-background: rgba(9, 102, 180, 0.1);
}

[theme-mode='dark'] {
  // 主题色
  --umo-color-white: #17171a;
  --umo-color-black: #fff;

  // 警示颜色
  --umo-warning-color: rgb(207, 110, 45);
  --umo-error-color: rgb(198, 71, 81);

  // 背景颜色
  --umo-container-background: #2a2b2d;
  --umo-button-hover-background: #2c2c2c;

  // 字体相关
  --umo-text-color: #c0c0c0;
  --umo-text-color-light: #bbb;
  --umo-text-color-disabled: #999;

  // 边框
  --umo-border-color: rgba(255, 255, 255, 0.15);
  --umo-border-color-dark: rgba(255, 255, 255, 0.2);
  --umo-border-color-light: rgba(255, 255, 255, 0.08);

  // 编辑器
  --umo-content-text-color: #000;

  // 滚动条
  --umo-scrollbar-thumb-color: rgba(255, 255, 255, 0.3);
  --umo-scrollbar-thumb-hover-color: rgba(255, 255, 255, 0.45);

  // 菜单悬浮效果
  --umo-menu-item-hover-background: #323232;
}
