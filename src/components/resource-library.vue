<template>
  <modal :visible="show" attach="body" :prevent-scroll-through="false" placement="center" draggable
    :header="t('insert.paperWarrapper.ResourceLibrary')" destroy-on-close width="1200px" @confirm="getSelctedFiles"
    @close="handleClose">
    <t-tabs v-model="curTab" @change="changeTab">
      <t-tab-panel value="myResource" :label="t('insert.library.myResource')"> </t-tab-panel>
      <t-tab-panel value="bookResource" :label="t('insert.library.textbookResources')"> </t-tab-panel>
    </t-tabs>
    <div>
      <div class="main">
        <div class="main-form">
          <div class="main-form-query">
            <t-form ref="formRef" :data="queryParams" label-width="80px" label-align="right" layout="inline">
              <t-form-item v-if="isLink" :label="t('insert.library.fileType') + '：'">
                <t-select v-model="queryParams.fileType" :placeholder="t('insert.library.fileTypePlaceholder')"
                  style="width: 200px" clearable @change="getList">
                  <t-option v-for="item in fileTypeList" :key="item.value" :label="item.label" :value="item.value" />
                </t-select>
              </t-form-item>
              <t-form-item :label="t('insert.library.fileName') + '：'">
                <t-input v-model="queryParams.folderName" :placeholder="t('insert.library.fileNamePlaceholder')"
                  style="width: 200px" />
              </t-form-item>
              <t-form-item>
                <t-button theme="primary" class="btn" @click="getList">
                  <template #icon>
                    <SearchIcon />
                  </template>
                  {{ t('insert.library.search') }}</t-button>
                <t-button v-if="curTab === 'myResource' && props.fileType" class="btn" @click="handleAddFile">
                  {{ t('insert.library.upload') }}
                </t-button>
              </t-form-item>
            </t-form>
          </div>
        </div>

        <div class="navigation-bar">
          <div class="nav-buttons">
            <t-button theme="default" variant="text" :disabled="historyIndex <= 0" @click="handleBack">
              <template #icon>
                <t-icon name="chevron-left" size="26" />
              </template>
            </t-button>

            <t-button theme="default" variant="text" :disabled="historyIndex >= folderHistory.length - 1"
              @click="handleForward">
              <template #icon>
                <t-icon name="chevron-right" size="26" />
              </template>
            </t-button>
          </div>
          <t-breadcrumb>
            <t-breadcrumb-item v-for="(folder, index) in folderPath" :key="index"
              :class="{ clickable: index < folderPath.length - 1 }" @click="handleBreadcrumbClick(folder.id)">
              {{ folder.name }}
            </t-breadcrumb-item>
          </t-breadcrumb>
        </div>
        <div v-if="isLink" class="main-input">
          <span>{{ t('insert.library.linkTitle') }}:</span>
          <t-input v-model="linkTitle" style="width: 300px" clearable :maxlength="40" show-limit-number />
        </div>

        <div class="main-list">
          <div v-for="item in resourceList" :key="item.type === 'folder'
            ? `folder_${item.resourceId}`
            : `file_${item.resourceId}`
            " class="main-item">
            <div v-if="item.type === 'file'" class="main-item-header">
              <div>
                <t-checkbox v-model:checked="item.isSelected"
                  @change="(checked) => handleSelectionChange(item, checked)" />
              </div>
            </div>
            <div class="main-item-img" @click="handleItemClick(item)">
              <template v-if="item.type === 'folder'">
                <img src="http://dutp-test.oss-cn-beijing.aliyuncs.com/%E6%96%87%E4%BB%B6%E5%A4%B9%20131x113%402x.png"
                  style="width: 50px; height: 50px; object-fit: contain" />
              </template>
              <template v-else>
                <!-- 根据文件类型显示不同的预览 -->
                <img v-if="item.fileType === '1'" :src="item.fileUrl" :alt="item.name"
                  style="width: 50px; height: 50px; object-fit: cover" />
                <img v-else-if="item.fileType === '2'"
                  src="http://dutp-test.oss-cn-beijing.aliyuncs.com/%E9%9F%B3%E9%A2%91.png" alt="音频"
                  style="width: 50px; height: 50px; object-fit: contain" />
                <img v-else-if="item.fileType === '3'"
                  src="http://dutp-test.oss-cn-beijing.aliyuncs.com/%E8%A7%86%E9%A2%91.png" alt="视频"
                  style="width: 50px; height: 50px; object-fit: contain" />
                <img v-else-if="item.fileType === '4'"
                  src="http://dutp-test.oss-cn-beijing.aliyuncs.com/%E6%96%87%E4%BB%B6.png" alt="虚拟仿真"
                  style="width: 50px; height: 50px; object-fit: contain" />
                <img v-else-if="item.fileType === '5'"
                  src="http://dutp-test.oss-cn-beijing.aliyuncs.com/%E6%96%87%E4%BB%B6.png" alt="AR/VR"
                  style="width: 50px; height: 50px; object-fit: contain" />
                <img v-else-if="item.fileType === '6'"
                  src="http://dutp-test.oss-cn-beijing.aliyuncs.com/%E6%96%87%E4%BB%B6.png" alt="3D模型"
                  style="width: 50px; height: 50px; object-fit: contain" />
                <img v-else-if="item.fileType === '7'"
                  src="http://dutp-test.oss-cn-beijing.aliyuncs.com/%E6%96%87%E4%BB%B6.png" alt="习题"
                  style="width: 50px; height: 50px; object-fit: contain" />
                <img v-else-if="item.fileType === '8'"
                  src="http://dutp-test.oss-cn-beijing.aliyuncs.com/%E6%96%87%E4%BB%B6.png" alt="课件"
                  style="width: 50px; height: 50px; object-fit: contain" />
                <img v-else src="http://dutp-test.oss-cn-beijing.aliyuncs.com/%E6%96%87%E4%BB%B6.png" alt="文件"
                  style="width: 50px; height: 50px; object-fit: contain" />
              </template>
            </div>
            <div class="main-item-name">
              <t-tooltip :content="item.name" :show-arrow="false">
                {{ item.name }}</t-tooltip>
            </div>
          </div>
        </div>
        <t-tag v-if="isLink" theme="warning" style="float:right;margin-right: 100px;margin-bottom: 10px">{{ t('insert.link.notice') }}</t-tag>
        <t-space v-if="resourceList.length === 0" style="
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 300px;
          " direction="vertical" align="center">
          <t-empty />
        </t-space>

        <div v-if="resourceList.length !== 0" class="page-item">
          <t-pagination v-model="queryParams.pageNum" v-model:page-size="queryParams.pageSize" :total="total"
            @change="getList" />
        </div>
      </div>
    </div>

    <!-- 添加图片对话框 -->
    <uploadImg v-model="imageDialogVisible" :parent-id="queryParams.parentId" @refresh="refresh" />

    <!-- 添加视频对话框 -->
    <uploadVideo v-model="videoDialogVisible" :parent-id="queryParams.parentId" @refresh="refresh" />

    <!-- 添加音频对话框 -->
    <uploadAudio v-model="audioDialogVisible" :parent-id="queryParams.parentId" @refresh="refresh" />

    <!-- 添加虚拟仿真/AR/VR/3D模型对话框 -->
    <uploadModel v-model="modelDialogVisible" :file-type="fileType" :parent-id="queryParams.parentId"
      @refresh="refresh" />

    <!-- 添加课件对话框 -->
    <uploadCourseware v-model="coursewareDialogVisible" :parent-id="queryParams.parentId" @refresh="refresh" />

    <!-- 图片预览对话框 -->
    <t-dialog v-model:visible="imagePreviewVisible" :header="t('insert.library.imagePreview')" width="800px"
      :destroy-on-close="true" placement="center">
      <div class="image-preview-container">
        <img :src="previewUrl" :alt="t('insert.library.imagePreview')" style="max-width: 100%; max-height: 70vh" />
      </div>
    </t-dialog>

    <!-- 音频预览对话框 -->
    <t-dialog v-model:visible="audioPreviewVisible" :header="t('insert.library.audioPreview')" width="500px"
      :destroy-on-close="true">
      <div class="audio-preview-container">
        <audio controls style="width: 100%">
          <source :src="previewUrl" type="audio/mpeg" />
          您的浏览器不支持音频播放
        </audio>
      </div>
    </t-dialog>

    <!-- 视频预览对话框 -->
    <t-dialog v-model:visible="videoPreviewVisible" :header="t('insert.library.videoPreview')" width="800px"
      :destroy-on-close="true">
      <div class="video-preview-container">
        <video controls style="width: 100%">
          <source :src="previewUrl" type="video/mp4" />
          您的浏览器不支持视频播放
        </video>
      </div>
    </t-dialog>

    <!-- 文件预览对话框 -->
    <t-dialog v-model:visible="filePreviewVisible" :header="t('insert.library.filePreview')" width="90%"
      :destroy-on-close="true" mode="full-screen">
      <div class="file-preview-container">
        <iframe :src="previewUrl" frameborder="0" style="width: 100%; height: 80vh"></iframe>
      </div>
    </t-dialog>
  </modal>
</template>

<script setup>
import { Base64 } from 'js-base64'
import { SearchIcon } from 'tdesign-icons-vue-next'
import { MessagePlugin } from 'tdesign-vue-next'

import { uploadResource } from '@/api/book/bookResource.js'
import { listBookResourcefolderResource } from '@/api/resource/bookResourceFolder'
import { listUserResourceFolderResource } from '@/api/resource/userResourceFolder'
import uploadAudio from '@/components/template/resourceLibrary/components/uploadResourceDialog/uploadAudio.vue'
import uploadCourseware from '@/components/template/resourceLibrary/components/uploadResourceDialog/uploadCourseware.vue'
import uploadImg from '@/components/template/resourceLibrary/components/uploadResourceDialog/uploadImg.vue'
import uploadModel from '@/components/template/resourceLibrary/components/uploadResourceDialog/uploadModel.vue'
import uploadVideo from '@/components/template/resourceLibrary/components/uploadResourceDialog/uploadVideo.vue'
import { fileTypeList } from '@/utils/optionUtil'
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  fileType: {
    type: String,
    default: '',
  },
  isLink: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: '',
  },
  multiple: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:visible', 'insertByResource'])
const { chapterId, bookId } = useStore()

const show = ref(false)

const linkTitle = ref('')

const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  folderName: null,
  parentId: 0, // 默认父级ID为0
  fileType: props.fileType,
})

const total = ref(0)
const curTab = ref('myResource')

// 导航历史
const folderHistory = ref([])
const historyIndex = ref(-1)
const folderPath = ref([{ id: 0, name: '我的资源' }])
const MAX_HISTORY = 5

// 列表数据
const resourceList = ref([])

// 文件上传相关变量
const imageDialogVisible = ref(false)
const videoDialogVisible = ref(false)
const audioDialogVisible = ref(false)
const modelDialogVisible = ref(false)
const coursewareDialogVisible = ref(false)

const imagePreviewVisible = ref(false)
const audioPreviewVisible = ref(false)
const videoPreviewVisible = ref(false)
const filePreviewVisible = ref(false)

const previewUrl = ref('')
const previewFileName = ref('')

const formRef = ref(null)

watch(
  () => props.visible,
  async (val) => {
    show.value = val
    queryParams.value.fileType = props.fileType
    if (val && props.isLink) {
      linkTitle.value = props.title
    }
    if (val) {
      reset()
      await getList()
      resourceList.value?.forEach((e) => {
        e.isSelected = false
      })
    }
  },
)

/** 初始化 */
// onMounted(() => {
//   reset()
//   getList()
// })

// 切换tab
const changeTab = () => {
  reset()
  getList()
}

// 重置
const reset = () => {
  // 初始化历史记录
  let firstFolder = {
    id: 0,
    name: t('insert.library.myResource'),
  }
  if (curTab.value === 'bookResource') {
    firstFolder = {
      id: 0,
      name: t('insert.library.textbookResources'),
    }
  }
  folderPath.value = [firstFolder]
  folderHistory.value = []
  folderHistory.value.push({
    parentId: 0,
    folderPath: [...folderPath.value],
  })
  historyIndex.value = 0

  // 初始化查询参数
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    folderName: null,
    parentId: 0,
    fileType: props.fileType,
  }

  resourceList.value = []
}

// 刷新数据
const refresh = () => {
  getList()
}

// 获取列表数据
const getList = async () => {
  try {
    let response = null
    if (curTab.value === 'myResource') {
      response = await listUserResourceFolderResource({
        ...queryParams.value,
        folderName: queryParams.value.folderName,
        fileType: queryParams.value.fileType,
        folderId: queryParams.value.parentId,
      })
    } else {
      response = await listBookResourcefolderResource({
        ...queryParams.value,
        folderName: queryParams.value.folderName,
        fileType: queryParams.value.fileType,
        folderId: queryParams.value.parentId,
        bookId: bookId.value,
      })
    }

    const allItems = response.rows || []
    resourceList.value = allItems.map((item) => {
      item.isSelected = false
      return item
    })

    // 设置总数
    total.value = response.total
  } catch (error) {
    MessagePlugin.error(`获取列表失败：${error.message}`)
  } finally {
  }
}

// 处理选择变更
function handleSelectionChange(item, checked) {
  if (item.type === 'folder') {
    return
  }
  // 更新项目的选中状态
  item.isSelected = checked
}

// 导航相关函数
function handleBack() {
  if (historyIndex.value > 0) {
    historyIndex.value--
    const prevState = folderHistory.value[historyIndex.value]
    queryParams.value.parentId = prevState.parentId
    folderPath.value = [...prevState.folderPath]
    getList()
  }
}

//
function handleForward() {
  if (historyIndex.value < folderHistory.value.length - 1) {
    historyIndex.value++
    const nextState = folderHistory.value[historyIndex.value]
    queryParams.value.parentId = nextState.parentId
    folderPath.value = [...nextState.folderPath]
    getList()
  }
}

function addToHistory() {
  folderHistory.value = folderHistory.value.slice(0, historyIndex.value + 1)
  folderHistory.value.push({
    parentId: queryParams.value.parentId,
    folderPath: [...folderPath.value],
  })
  if (folderHistory.value.length > MAX_HISTORY) {
    folderHistory.value.shift()
  } else {
    historyIndex.value++
  }
}

function handleBreadcrumbClick(folderId) {
  const index = folderPath.value.findIndex((item) => item.id === folderId)
  if (index !== -1) {
    queryParams.value.parentId = folderId
    folderPath.value = folderPath.value.slice(0, index + 1)
    addToHistory()
    getList()
  }
}

// 处理项目点击
function handleItemClick(item) {
  if (item.type === 'folder') {
    queryParams.value.parentId = item.resourceId
    folderPath.value.push({
      id: item.resourceId,
      name: item.name,
    })
    addToHistory()
    getList()
  } else {
    handleFilePreview(item)
  }
}

// 处理添加文件按钮
function handleAddFile() {
  console.log('上传事件触发：fileType是', props)

  switch (props.fileType) {
    case '1':
      imageDialogVisible.value = true
      break
    case '3':
      videoDialogVisible.value = true
      break
    case '2':
      audioDialogVisible.value = true
      break
    case '4':
    case '5':
    case '6':
      modelDialogVisible.value = true
      break
    case '8':
      coursewareDialogVisible.value = true
      break
  }
}

// 预览处理函数
function handleFilePreview(file) {
  if (!file.fileUrl) {
    MessagePlugin.error('文件地址无效')
    return
  }
  previewUrl.value = file.fileUrl
  previewFileName.value = file.name
  switch (file.fileType) {
    case '1': // 图片
      previewFile(file.fileUrl)
      break
    case '2': // 音频
      audioPreviewVisible.value = true
      break
    case '3': // 视频
      videoPreviewVisible.value = true
      break
    case '4': // 虚拟仿真
    case '5': // AR/VR
      window.open(file.fileUrl)
      break
    case '6': // 3D模型
      previewFile(file.fileUrl)
      break
    case '8': // 课件
    default:
      previewFile(file.fileUrl)
      break
  }
}

// 预览文件
function previewFile(url) {
  window.open(
    `${import.meta.env.VITE_ONLINE_PREVIEW}${encodeURIComponent(Base64.encode(url))}`,
  )
}

// 获取所有选中的文件
const getSelctedFiles = () => {
  let selectedFiles = resourceList.value.filter((item) => item.isSelected) || []
  if (selectedFiles.length === 0) {
    return MessagePlugin.error('请选择文件')
  }
  if (!props.multiple && selectedFiles.length > 1) {
    return MessagePlugin.error('只能选择一个文件')
  }
  if (props.isLink) {
    if (!linkTitle.value) {
      return MessagePlugin.error('请输入链接标题')
    }
    selectedFiles = selectedFiles.map((item) => {
      return {
        title: linkTitle.value,
        ...item,
      }
    })
  }

  if (props.multiple) {
    if (curTab.value === 'myResource') {
      selectedFiles.forEach((item) => {
        uploadResource({
          fileName: item.name,
          fileUrl: item.fileUrl,
          fileSize: item.fileSize,
          chapterId: chapterId.value,
          resourceId: item.resourceId,
        })
      })
    }
    emit('insertByResource', selectedFiles)
  } else {
    const item = selectedFiles[0]
    if (curTab.value === 'myResource') {
      uploadResource({
        fileName: item.name,
        fileUrl: item.fileUrl,
        fileSize: item.fileSize,
        chapterId: chapterId.value,
        resourceId: item.resourceId,
      })
    }

    emit('insertByResource', item)
  }

  handleClose()
}

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false)
}
</script>

<style lang="less" scoped>
.main {
  .main-form {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 20px 0;

    .main-form-query {
      .btn {
        margin-left: 20px;
      }
    }
  }

  .main-back {
    display: flex;
    align-items: center;
  }

  .main-input {
    display: flex;
    align-items: center;

    span {
      padding-right: 20px;
      color: #333;

      &::before {
        content: '*';
        color: red;
        padding-right: 4px;
      }
    }
  }

  .main-list {
    display: flex;
    flex-wrap: wrap;
    margin: 20px 0;

    .main-item {
      padding: 20px;
      text-align: center;
      cursor: pointer;

      &:hover {
        background-color: #f5f5f5;
      }

      .main-item-header {
        display: flex;
        justify-content: space-between;
      }

      .main-item-name {
        width: 130px;
        white-space: nowrap;
        /* 保证文本在一行内显示 */
        overflow: hidden;
        /* 超出容器部分隐藏 */
        text-overflow: ellipsis;
        /* 使用省略号表示文本溢出 */
      }
    }
  }
}

.navigation-bar {
  display: flex;
  align-items: center;
  margin: 20px 0;

  .nav-buttons {
    margin-right: 20px;
  }

  .clickable {
    cursor: pointer;
    color: var(--td-brand-color);

    &:hover {
      color: var(--td-brand-color-hover);
    }
  }
}

.preview-list {
  margin-top: 16px;

  .image-item {
    margin-bottom: 16px;

    .image-item-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 8px; // 添加底部间距

      .image-info {
        display: flex;
        align-items: center;

        .preview-image {
          width: 60px;
          height: 60px;
          object-fit: cover;
          margin-right: 12px;
        }

        .image-name {
          max-width: 200px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .remove-btn {
        margin-left: 12px;
      }
    }

    // 添加进度条样式
    .progress-wrapper {
      margin-top: 8px;
      padding: 0 12px;
    }
  }
}

.upload-tips {
  margin-top: 8px;
  color: var(--td-text-color-secondary);
}

.video-list {
  margin-top: 16px;

  .video-item {
    margin-bottom: 16px;

    .video-item-content {
      display: flex;
      flex-direction: column;
      gap: 12px;

      .video-info {
        display: flex;
        align-items: center;
        gap: 8px;

        .video-name {
          max-width: 200px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .video-controls {
        .video-player {
          width: 100%;
          max-height: 200px; // 添加最大高度限制
          object-fit: contain; // 保持视频比例
        }

        .remove-btn {
          margin-top: 8px;
        }
      }
    }
  }
}
</style>
