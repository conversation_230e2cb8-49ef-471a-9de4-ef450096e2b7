<template>
  <div class="rich-editor-con">
    <Toolbar style="border-bottom: 1px solid #ccc" :editor="editorWangRef" :default-config="toolbarConfigData"
      :mode="mode" />
    <EditorWang v-model="valueHtml" :style="{
      height: heightProps + 'px',
      'max-height': maxHeightProps + 'px',
    }" :default-config="editorConfig" :mode="mode" @click="initCurrentEditor" @on-created="handleCreated"
      @on-change="handleChange" @on-destroyed="handleDestroyed" @on-focus="handleFocus" />
    <slot />
    <formula-dialog :visibility="formulaDialogVisibility" :provider-name="formulaProviderName"
      :formula-type="formulaType" :need-t="props.needT" @confirm="confirmOnFormulaEditorDialog"
      @cancel="formulaDialogVisibility = false"></formula-dialog>
  </div>
</template>

<script setup>
import '@wangeditor/editor/dist/css/style.css' // 引入 css

import { Boot } from '@wangeditor/editor'
import { Editor as EditorWang, Toolbar } from '@wangeditor/editor-for-vue'
import { onBeforeUnmount, onMounted, ref, shallowRef, triggerRef } from 'vue'

import FormulaDialog from '@/components/menus/toolbar/modal/formulaDialog.vue'
import { OssService } from '@/utils/aliOss'

import module from './module'
import { audioMenuConf } from './module/audio/audioMenu.js'
import { formulaMenuConf } from './module/formula/formulaMenu.js'
import lineHeight from '@/extensions/line-height'
const props = defineProps({
  toolbarConfig: {
    type: Object,
    default: {},
  },
  textContent: {
    type: String,
    default: '',
  },
  heightProps: {
    type: Number,
    default: 100,
  },
  maxHeightProps: {
    type: Number,
    default: 500,
  },
  needT: {
    type: Boolean,
    default: true,
  },
  placeholder: {
    type: String,
    default: '请输入内容...',
  },
})

const emit = defineEmits([
  'updateTextContent',
  'onEditorCreated',
  'onEditorMounted',
])
const formulaDialogVisibility = ref(false)
const formulaProviderName = ref('')
const formulaType = ref('')
function addLatexFormula() {
  formulaProviderName.value = 'latex'
  formulaType.value = 'latex'
  formulaDialogVisibility.value = true
}
function addMathFormula() {
  formulaProviderName.value = 'wiris'
  formulaType.value = 'math'
  formulaDialogVisibility.value = true
}
function addChemistryFormula() {
  formulaType.value = 'chem'
  formulaDialogVisibility.value = true
  formulaProviderName.value = 'wiris'
}
function confirmOnFormulaEditorDialog({ url, width, height, name, langData }) {
  const htmlStr = `<img width=${width} height=${height} src='${url}' crossorigin="anonymous" name="${name}" isShowImageTitle="0" />`
  editorWangRef.value.focus()
  editorWangRef.value.dangerouslyInsertHtml(htmlStr)
  formulaDialogVisibility.value = false
}

// 配置 wangEditor
const mode = ref('')
const editorWangRef = shallowRef()
const valueHtml = ref('')

// 编辑器设置
let toolbarConfigData = {
  insertKeys: {
    // 插入的位置，基于当前的 toolbarKeys
    index: 24,
    keys: [
      'through', // 删除线
      'code', // 行内代码
      'sup', // 上标
      'sub', // 下标
      'clearStyle', // 清除格式
      audioMenuConf.key,
      formulaMenuConf.key
    ],
  },
  // 隐藏'全屏'菜单和省略号菜单
  excludeKeys: ['fullScreen', 'group-more-style', 'emotion'],
}

// 必须放外面，不然会报错
module()





onMounted(() => {
  if (props.toolbarConfig && typeof props.toolbarConfig === 'object') {
    toolbarConfigData = {
      insertKeys: {
        index: toolbarConfigData.insertKeys.index,
        keys: [
          ...toolbarConfigData.insertKeys.keys,
          ...(props.toolbarConfig.insertKeys?.keys ?? [])
        ]
      },
      excludeKeys: [
        ...(props.toolbarConfig.excludeKeys ?? []),
        ...toolbarConfigData.excludeKeys,
        'emotion'
      ],
    }
  }
})


const editorConfig = {
  placeholder: props.placeholder,
  height: '10px',
  fontSize: '18px', // 设置默认字号为18px
  defaultStyle: `
    font-family:  FZHTJW, serif;
  `,
  pasteFilterStyle: false, // 关闭默认的粘贴样式过滤，保留 &nbsp; 等空格
  formulaConfig: {
    imageUrlTemplate: 'https://r.latexeasy.com/image.svg?',
    editorMode: 'live',
    editorLiveServer: 'https://latexeasy.com',
  },
  MENU_CONF: {
    // 配置字号列表
    fontSize: {

      fontSizeList: [
        {
          name: '10px',
          value: '10px'
        },
        {
          name: '11px',
          value: '11px'
        },
        {
          name: '12px',
          value: '12px'
        },
        {
          name: '14px',
          value: '14px'
        },
        {
          name: '16px',
          value: '16px'
        },
        {
          name: '18px',
          value: '18px'
        },
        {
          name: '20px',
          value: '20px'
        },
        {
          name: '22px',
          value: '22px'
        },
        {
          name: '24px',
          value: '24px'
        },
        {
          name: '26px',
          value: '26px'
        },
        {
          name: '28px',
          value: '28px'
        },
        {
          name: '32px',
          value: '32px'
        },
        {
          name: '36px',
          value: '36px'
        },
        {
          name: '42px',
          value: '42px'
        },
        {
          name: '48px',
          value: '48px'
        },
        {
          name: '72px',
          value: '72px'
        },
        {
          name: '96px',
          value: '96px'
        }
      ]


      // '10px', '12px', '14px', '16px', '18px', '20px', '24px', '28px', '32px', '36px', '48px'
    },
    fontFamily: {
      defaultFontFamily: '黑体', // ⭐️ 设置默认值为宋体
      fontFamilyList: [
        { name: '黑体', value: 'FZHTJW' },
        { name: '宋体', value: 'FZSSJW' },
        { name: '楷体', value: 'FZKTJW' },
        { name: "仿宋", value: 'FZFangSong-Z02S' },
        { name: '隶书', value: 'FZHLK' },
        // {
        //   name: 'Times New Roman',
        //   value: 'Times New Roman'
        // }
      ]
    },


    // 配置上传
    uploadImage: {
      // 不需要配置服务器地址，因为我们自己处理上传
      server: '',
      fieldName: 'file',
      maxFileSize: 5 * 1024 * 1024, // 5MB
      async customUpload(file, insertFn) {
        try {
          OssService(file)
            .then((res) => {
              insertFn(res.url, '', '')
              // valueHtml.value += `<img syle="width: 20px; height: auto;" src="${res.url}"/>`
            })
            .catch((error) => {
              useMessage('error', error.message)
            })
        } catch (error) {
          console.error('上传失败:', error)
        }
      },
    },
    uploadVideo: {
      // 不需要配置服务器地址，因为我们自己处理上传
      server: '',
      fieldName: 'file',
      allowedFileTypes: ['video/*'],
      // 自定义上传
      async customUpload(file, insertFn) {
        try {
          OssService(file)
            .then((res) => {
              valueHtml.value += `<video
                                    width="500"
                                    preload="metadata"
                                    controls
                                  >
                                   <source src="${res.url}" type="video/mp4">
                                      您的浏览器不支持 Video 标签。
                                  </video>`
              // insertFn(res.url, '')
              // valueHtml.value += `<img syle="width: 20px; height: auto;" src="${res.url}"/>`
            })
            .catch((error) => {
              useMessage('error', error.message)
            })
        } catch (error) {
          console.error('上传失败:', error)
        }
      },
    },
    uploadAudio: {
      server: '',
      fieldName: 'file',
      allowedFileTypes: ['mp3'],
      async customUpload(file, insertFn) {
        try {
          OssService(file)
            .then((res) => {
              console.log("220", res)
              valueHtml.value += `<audio
                                    width="500"
                                    preload="metadata"
                                    controls
                                  >
                                   <source src="${res.url}" type="audio/mp3">
                                      您的浏览器不支持 Audio 标签。
                                  </audio>`
              // insertFn(res.url, '')
              // valueHtml.value += `<img syle="width: 20px; height: auto;" src="${res.url}"/>`
            })
            .catch((error) => {
              useMessage('error', error.message)
            })
        } catch (error) {
          console.error('上传失败:', error)
        }
      }
    }
  },
}

// 组件销毁时，也及时销毁编辑器
onBeforeUnmount(() => {
  const editor = editorWangRef.value
  if (editor == null) return
  editor.destroy()
})

// 注册菜单
const registerEditorWangMenu = () => {
  const keys = editorWangRef.value.getAllMenuKeys()

  // 公式菜单
  if (keys.indexOf(formulaMenuConf.key) === -1) {
    Boot.registerMenu(formulaMenuConf)
  }

  // 音频菜单
  if (keys.indexOf(audioMenuConf.key) === -1) {
    Boot.registerMenu(audioMenuConf)
  }
}

const initCurrentEditor = (editor) => {
  editorWangRef.value.focus()
}

const handleCreated = (editor) => {
  // 关联工具栏和编辑区，初始化editorWang菜单
  editorWangRef.value = editor
  editor.children.forEach((child) => {
    child.children.forEach(ele => {
      ele.fontSize = '18px'
      ele.fontFamily = 'FZHTJW'
    })
    child.lineHeight = '2'
  })
  emit('onEditorCreated', editor)
  emit('onEditorMounted', editor)
  triggerRef(editorWangRef)

  registerEditorWangMenu()
  registerEditorWangOpt()

  setEditorWangContent()
}

const setEditorWangContent = () => {
  // wangEditor加载慢
  valueHtml.value = props.textContent
}

// 绑定wang编辑器事件
const registerEditorWangOpt = () => {
  editorWangRef.value?.on('formulaMenuClick', (type) => {
    if (type === 'chem') {
      addChemistryFormula()
    } else if (type === 'math') {
      addMathFormula()
    } else if (type === 'latex') {
      addLatexFormula()
    }
  })

  // 监听DOM事件
  const editorContainer = editorWangRef.value.getEditableContainer()
  // if (editorContainer) {
  //   // 监听粘贴事件，处理空格转换
  //   editorContainer.addEventListener('paste', (event) => {
  //     // 阻止默认的粘贴行为
  //     event.preventDefault()
  //     event.stopPropagation()
  //     const clipboardData = event.clipboardData || window.clipboardData
  //     const text = clipboardData.getData('text/plain')
  //     if (!text) return

  //     const lines = text.split(/\r?\n/)

  //     lines.forEach((line, idx) => {
  //       for (const char of line) {
  //         if (char === ' ') {
  //           editorWangRef.value.insertText('\u00A0') // nbsp...
  //         } else {
  //           editorWangRef.value.insertText(char)
  //         }
  //       }
  //       if (idx !== lines.length - 1) {
  //         // 插入换行
  //         editorWangRef.value.dangerouslyInsertHtml('<p><br></p>')
  //       }
  //     })

  //     // 延迟处理，确保内容已经插入后再清理Word格式
  //     // setTimeout(() => {
  //     //   const html = editorWangRef.value.getHtml()

  //     //   console.log('复制后的html', html)

  //     //   // 清理Word格式，但保留首行缩进效果，让其可删除
  //     //   const cleanHtml = html
  //     //     // 将text-indent样式转换为可删除的空格
  //     //     .replace(/style="[^"]*text-indent:\s*([^";]+)[^"]*"/gi, (match, indentValue) => {
  //     //       // 移除text-indent样式，让&nbsp;来处理缩进
  //     //       return match.replace(/text-indent:\s*[^;]+;?/gi, '')
  //     //     })

  //     //   console.log('清理后的html', cleanHtml)
  //     //   if (cleanHtml !== html) {
  //     //     editorWangRef.value.setHtml(cleanHtml)
  //     //     valueHtml.value = cleanHtml
  //     //   }
  //     // }, 100)
  //   }, true); // 使用捕获阶段，确保在 wangEditor 处理之前拦截
  //   editorContainer.addEventListener('beforeinput', (e) => {
  //     if (
  //       e.inputType === 'insertText' &&
  //       e.data === ' ' &&
  //       !e.isComposing
  //     ) {
  //       e.preventDefault()
  //       // 插入真实的不换行空格字符（非HTML实体）
  //       editorWangRef.value.insertText('\u00A0')
  //     }
  //   })
  // }
}

// 更新内容
const handleChange = (editor) => {
  if (editor == null) return
  emit('updateTextContent', valueHtml.value)
}

// 销毁事件
const handleDestroyed = (editor) => {
  if (editor == null) return
  editor.destroy()
}

// 聚焦事件
const handleFocus = (editor) => {
  editorWangRef.value = editor
  triggerRef(editorWangRef)
}
</script>

<style lang="less" scoped>
@import '@/assets/styles/_common.less';

.rich-editor-con {
  margin-bottom: 10px;
  border: 1px solid #dcdfe6;
}
</style>

<style>
.w-e-text-container [data-slate-editor] {
  font-family: 'FZHTJW' !important;
}
</style>
