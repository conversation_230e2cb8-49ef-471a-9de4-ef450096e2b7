<template>


  <div :class="source == 'tool' ? 'fount-bold' : ''">


    <menus-button :text="text || t('base.color')" menu-type="popup" popup-handle="arrow" hide-text
      :popup-visible="popupVisible" @toggle-popup="togglePopup" @menu-click="colorChange(currentColor)">


      <!-- <icon name="color" :style="{
        borderColor: currentColor,
        borderWidth: '4px',
        borderBottomStyle: 'solid',

      }" /> -->
      <icon name="color" :style="{
        borderColor: editor?.getAttributes('textStyle')?.color || currentColor,
        borderWidth: '4px',
        borderBottomStyle: 'solid',
      }" />
      <template #content>
        <color-picker :default-color="defaultColor" @change="colorChange" />
      </template>
    </menus-button>

    <div class="fount-bold-text __ellipsis" v-if="source == 'tool'">{{ t('base.color') }}</div>
  </div>

</template>

<script setup lang="ts">
const props = defineProps({
  text: {
    type: String,
    default: '',
  },
  modeless: {
    type: Boolean,
    default: false,
  },
  defaultColor: {
    type: String,
    default: '#000',
  },
  source: String,
})
const emits = defineEmits(['change'])

const { popupVisible, togglePopup } = usePopup()
const { editor } = useStore()

let currentColor = $ref<any>()
const colorChange = (color: string) => {
  console.log("aaa", color)
  currentColor = color
  popupVisible.value = false

  if (props.modeless) {
    emits('change', currentColor)
    return
  }

  if (color === '') {
    editor.value?.chain().focus().unsetColor().run()
  } else {
    editor.value?.chain().focus().setColor(color).run()
  }
}
</script>
