<template>
  <div style="display: flex" @click="handleFileChange">
    <div>
      <menus-button ico="backgroundImage" style="padding: 0 0 0 15px" :text="t('base.backgroundImage')"
        hide-text></menus-button>
    </div>
    <div style="flex: 1" class="fount-bold-text __ellipsis">
      {{ t('base.backgroundImage') }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { chooseFile, defaultOptPreChekck } from '@/utils/file'
const { editor } = useStore()
const handleFileChange = () => {
  chooseFile((file) =>
    // editor?.value
    //   .chain()
    //   .focus()
    //   .setBackgroundUrl(file.fileUrl)
    //   .setBackgroundSize('contain')
    //   .run(),
    editor.value
      ?.chain()
      .focus()
      .setBackgroundImg({ src: file.fileUrl }).run(),
    {
      optPreChekck: defaultOptPreChekck
    }
  )
}
</script>

<style lang="less" scoped>
.umo-icon-background-color {
  border-radius: 2px;
}
</style>
