<template>
  <div class="fount-bold" @click="handleClick">
    <div>
      <menus-button ico="layoutColumn" :text="t('base.layoutColumn.text')" hide-text />
    </div>

    <div class="fount-bold-text __ellipsis">
      {{ t('base.layoutColumn.text') }}
    </div>
  </div>
  <modal :visible="visible" :header="t('base.layoutColumn.text')" width="600px" @close="visible = false"
    @confirm="handleConfirm">
    <div>
      <t-form :data="formData">
        <t-radio-group v-model="formData.layout" variant="primary-filled" default-value="1" @change="changeType">
          <t-radio-button value="1">{{
            t('base.layoutColumn.twoColumn')
          }}</t-radio-button>
          <t-radio-button value="2">{{
            t('base.layoutColumn.threeColumn')
          }}</t-radio-button>
        </t-radio-group>
        <div class="form-alert">
          <t-alert theme="info">
            <template #message>{{ t('base.layoutColumn.tips') }}</template>
          </t-alert>
        </div>

        <div class="form-item">
          <t-form-item :label="t('base.layoutColumn.left')" required-mark>
            <t-input-number v-model="formData.leftWidth" suffix="%" :min="1" :max="99" />
          </t-form-item>
          <t-form-item v-if="formData.layout === '2'" :label="t('base.layoutColumn.center')" required-mark>
            <t-input-number v-model="formData.centerWidth" suffix="%" :min="1" :max="99" />
          </t-form-item>
          <t-form-item :label="t('base.layoutColumn.right')" required-mark>
            <t-input-number v-model="rightWidthComputed" suffix="%" :min="1" :max="99" />
          </t-form-item>
        </div>

        <div class="form-item">
          <t-form-item :label="t('base.layoutColumn.borderOpen')">
            <t-switch v-model="formData.borderOpen" />
          </t-form-item>

          <t-form-item v-if="formData.borderOpen" :label="t('base.layoutColumn.borderColor')">
            <t-popup placement="right-bottom" attach="body" :destroy-on-close="false">
              <div class="umo-color-picker-more" style="display: flex; align-items: center; cursor: pointer">
                <div
                  :style="`border:1px solid #ddd;width:20px;height:20px;border-radius:5px;background:${formData.borderColor};margin-right:10px;`"
                  :class="`${formData.borderColor == 'transparent' || !formData.borderColor ? 'transparent' : ''}`">
                </div>
                <div class="umo-color-picker-more-menu" :style="`border-bottom: 3px solid ${formData.borderColor};}`">
                  <span v-text="t('base.layoutColumn.borderColor')"></span>
                </div>
              </div>
              <template #content>
                <div style="padding: 10px">
                  <color-picker :default-color="formData.borderColor" @change="backgroundColorChange" />
                </div>
              </template>
            </t-popup>
          </t-form-item>
          <t-form-item v-if="formData.borderOpen" :label="t('base.layoutColumn.borderWidth')">
            <t-input-number v-model="formData.borderWidth" suffix="px" :min="1" :max="99" />
          </t-form-item>
          <t-form-item v-if="formData.borderOpen" :label="t('base.layoutColumn.borderStyle')">
            <t-radio-group v-model="formData.borderStyle">
              <t-radio value="dotted">{{
                t('insert.block.dotted')
              }}</t-radio>
              <t-radio value="solid">{{
                t('insert.block.solid')
              }}</t-radio>
              <t-radio value="double">{{
                t('insert.block.double')
              }}</t-radio>
              <t-radio value="dashed">{{
                t('insert.block.dashed')
              }}</t-radio>
            </t-radio-group>
          </t-form-item>



          <div class="padding-main">
            <div class="padding-title">{{ t('base.layoutColumn.layoutColumnMargin') }}</div>
            <div class="linkage" @click="handLinkage">
              <LinkIcon :color="linkage ? '#0052d9' : '#333'" size="large" />
            </div>
            <div class="padding-left">
              <t-form-item :label="t('base.layoutColumn.topPadding')">
                <t-input-number v-model="formData.topPadding" suffix="px" @change="changePadding"></t-input-number>
              </t-form-item>
              <t-form-item :label="t('base.layoutColumn.bottomPadding')">
                <t-input-number v-model="formData.bottomPadding" suffix="px"></t-input-number>
              </t-form-item>
            </div>

            <div class="padding-left">
              <t-form-item :label="t('base.layoutColumn.leftPadding')">
                <t-input-number v-model="formData.leftPadding" suffix="px"></t-input-number>
              </t-form-item>
              <t-form-item :label="t('base.layoutColumn.rightPadding')">
                <t-input-number v-model="formData.rightPadding" suffix="px"></t-input-number>
              </t-form-item>
            </div>
          </div>


        </div>
      </t-form>
    </div>
  </modal>
</template>
<script setup>
import { LinkIcon } from 'tdesign-icons-vue-next'
import { MessagePlugin } from 'tdesign-vue-next'
defineProps({
  source: String,
})
const { editor } = useStore()
const visible = ref(false)
const linkage = ref(false)
const formData = ref({
  layout: '1',
  leftWidth: 1,
  rightWidth: 1,
  centerWidth: 1,
  borderOpen: false,
  topPadding: 0,
  bottomPadding: 0,
  rightPadding: 0,
  leftPadding: 0,
})
const handleClick = () => {
  formData.value = {
    layout: '1',
    leftWidth: 50,
    rightWidth: 50,
    centerWidth: 0,
    borderOpen: false,
    topPadding: 0,
    bottomPadding: 0,
    leftPadding: 0,
    rightPadding: 0,
    borderColor: '#333',
    borderStyle: 'solid',
    borderWidth: 1,
  }
  console.log('formData.value', formData.value)
  visible.value = true
}

const rightWidthComputed = computed(() => {
  if (formData.value.layout === '1') {
    formData.value.rightWidth = 100 - formData.value.leftWidth
  } else {
    formData.value.rightWidth = 100 - formData.value.leftWidth - formData.value.centerWidth
  }

  return formData.value.rightWidth
})

const handLinkage = () => {
  linkage.value = !linkage.value
  if (linkage.value) {
    return MessagePlugin.success(t('base.layoutColumn.openlinkage'))
  } else {
    return MessagePlugin.error(t('base.layoutColumn.closelinkage'))
  }
}

const changePadding = () => {
  if (linkage.value) {
    formData.value.rightPadding = formData.value.topPadding
    formData.value.leftPadding = formData.value.topPadding
    formData.value.bottomPadding = formData.value.topPadding
  }

}


const changeType = (e) => {
  if (e === '1') {
    formData.value = {
      layout: '1',
      leftWidth: 50,
      rightWidth: 50,
      centerWidth: 0,
      topPadding: 0,
      bottomPadding: 0,
      leftPadding: 0,
      rightPadding: 0,
    }
  } else {
    formData.value = {
      layout: '2',
      leftWidth: 33,
      rightWidth: 33,
      centerWidth: 34,
      topPadding: 0,
      bottomPadding: 0,
      leftPadding: 0,
      rightPadding: 0,
    }
  }
}

const handleConfirm = () => {
  const { layout, leftWidth, rightWidth, centerWidth, borderOpen, topPadding, bottomPadding, leftPadding, rightPadding, borderColor, borderStyle, borderWidth } = formData.value
  const columnNum = layout === '1' ? 2 : 3
  console.log(rightWidth, centerWidth)
  if (layout === '1') {
    if (Number(leftWidth) < 0) return MessagePlugin.error('左栏宽度不能小于0')
    if (leftWidth + rightWidth !== 100) {
      return MessagePlugin.error('两栏宽度之和必须为100%')
    } else {
      formData.value.centerWidth = 0
    }
  } else {
    if (Number(leftWidth) < 0) return MessagePlugin.error('左栏宽度不能小于0')
    if (Number(centerWidth) < 0) return MessagePlugin.error('中间栏宽度不能小于0')
    if (leftWidth + rightWidth + centerWidth !== 100) {
      return MessagePlugin.error('三栏宽度之和必须为100%')
    }
  }

  if (Number(borderWidth) < 1) return MessagePlugin.error('边框宽度不能小于0或等于0')

  editor.value
    ?.chain()
    .focus()
    .setLayoutColumn({
      left: leftWidth,
      right: rightWidth,
      center: centerWidth,
      columnNum,
      borderOpen,
      topPadding,
      bottomPadding,
      leftPadding,
      rightPadding,
      borderColor,
      borderStyle,
      borderWidth
    })
    .run()

  visible.value = false
}

const backgroundColorChange = (e) => {
  formData.value.borderColor = e
}
</script>

<style lang="less" scoped>
.form-item {
  margin: 10px 0;
}

.form-alert {
  margin-top: 10px;
}

.padding-main {
  display: flex;
  align-content: center;
  justify-content: center;
  padding: 10px;
  position: relative;
  border: 1px solid #dcdcdc;
  border-radius: 5px;

  .padding-title {
    position: absolute;
    top: -15px;
    left: 10px;
    background-color: #fff;
    font-size: 16px;
    font-weight: bold;
    padding: 0 5px;
    color: #333;
  }

  .linkage {
    position: absolute;
    top: 35%;
    left: 10px;
    width: 40px;
    height: 40px;
    z-index: 9999;
    transform: rotate(135deg);
    cursor: pointer;
  }
}

.padding-left {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px 0;

  .umo-form__item {
    margin-bottom: 10px;
  }

  .padding-icon {
    margin: 10px 0;
  }
}

.transparent {
  background:
    linear-gradient(to bottom right,
      transparent 49%,
      #ff0000 49%,
      #ff0000 51%,
      transparent 51%),
    #f0f0f0 !important;
}
</style>
