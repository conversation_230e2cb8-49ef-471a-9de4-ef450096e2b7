<template>
  <template v-if="source !== 'tool'">
    <menus-button :text="t('base.letterSpacing.text')" menu-type="dropdown" popup-handle="arrow" hide-text
      overlay-class-name="umo-letterSpacing-dropdown">
      <icon name="letterSpacing" class="icon-textSletterSpacing" />
      <template #dropmenu>
        <t-dropdown-menu>
          <t-dropdown-item class="umo-text-letterSpacing-menu" v-for="item in options" :key="item.value"
            :value="item.value" :divider="item.divider" @click="letterSpacingChange(item as LetterSpacingOption)">
            <span>{{ item.label }}</span>
          </t-dropdown-item>
        </t-dropdown-menu>
      </template>
    </menus-button>
  </template>

  <template v-else>
    <t-tooltip :content="t('base.letterSpacing.text')" theme="light" placement="top" :show-arrow="false"
      destroy-on-close>
      <div :class="'fount-bold'">
        <div>
          <menus-button :text="t('base.letterSpacing.text')" ico="letterSpacing" menu-type="dropdown" hide-text
            popup-handle="arrow" :select-options="letterSpacings" @click="letterSpacingChange" />

        </div>
        <div class="fount-bold-text __ellipsis" style="margin-top:3px;">
          {{ t('base.letterSpacing.text') }}
        </div>
      </div>
    </t-tooltip>
  </template>
</template>

<script setup lang="ts">
const { editor } = useStore()
defineProps({
  source: String,
})
interface LetterSpacingOption {
  label: string
  px?: string
}
const options = [
  { label: t('base.letterSpacing.0px'), px: 'normal' },
  { label: t('base.letterSpacing.1px'), px: '1px' },
  { label: t('base.letterSpacing.2px'), px: '2px' },
  { label: t('base.letterSpacing.3px'), px: '3px' },
  { label: t('base.letterSpacing.4px'), px: '4px' },
  { label: t('base.letterSpacing.5px'), px: '5px' },
]


const letterSpacingChange = ({ content, value }: { content: string, value: string }) => {
  console.log(content, value)
  if (!content) {
    return
  }
  editor.value?.chain().focus().setLetterSpacing(value).run()
}


const letterSpacings = computed(() => {
  return options.map((item) => {
    return {
      content: item.label,
      label: item.label,
      value: item.px,
      active: editor.value?.isActive({ letterSpacing: item.px }),
    }
  })
})
</script>

<style lang="less" scoped>
.icon-letterSpacing {
  border-radius: 2px;
}
</style>
<style lang="less">
.umo-text-letterSpacing-dropdown {
  .umo-popup__content {
    .umo-divider {
      margin-top: 8px;
      margin-bottom: 8px;
    }
  }
}

.icon-textSletterSpacing {
  font-size: 18px !important;
}

.umo-text-letterSpacing-menu {
  width: 140px;
  margin-bottom: 6px;
  border: solid 1px transparent;

  &.umo-clear-format-menu {
    margin-bottom: 0;
  }

  &:hover {
    border-color: var(--umo-primary-color);
    background-color: inherit;
  }

  .umo-dropdown__item-text {
    display: flex;
    align-items: center;
    padding: 2px;

    .umo-icon {
      font-size: 16px;
      margin-right: 5px;
    }
  }
}
</style>