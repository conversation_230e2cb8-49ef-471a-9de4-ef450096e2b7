<template>
  <div class="question-content-list">
    <div class="content-item">
      <!-- 01 单选，多选, 填空、判断、连线、排序等 -->
      <div class="item-type-con">
        <span class="type-label">{{ t('insert.questions.case') }}<span class="required">*</span></span>
        <t-radio-group class="type-radio-sy" :value="localUserQuestion.questionType + ''" :disabled="isUpdate"
          @change="questionTypeChangeHandler($event, localUserQuestion)">
          <t-radio v-for="(typeItem, index) in QuestionTypesDescriptive" :key="index + '-' + typeItem.value"
            :value="typeItem.value">
            {{ typeItem.label }}
          </t-radio>
        </t-radio-group>
      </div>
      <!-- 02 题干 -->

      <QuestionItemInputCell :label="t('insert.questions.remark')" :required="false"
        :item-content="localUserQuestion.questionRemark" :editor-textbar-config="{ height: 140 }" placeholder="请输入答题要求"
        @update-item-stem="doUpateRemark(localUserQuestion, $event)" />

      <QuestionItemInputCell :label="t('insert.questions.questionText')" :required="true"
        :item-content="localUserQuestion.questionContent"
        :editor-textbar-config="{ height: questionContentEditorHeight }"
        @update-item-stem="doUpdateStem(localUserQuestion, $event)" @on-editor-created="onStemEditorCreated">
        <div v-if="isFillinBlanksQuestion(localUserQuestion.questionType)">
          <div>
            <t-button theme="primary" variant="outline" @click="appendBlanks">
              <template #icon><add-icon /></template>
              {{ t('insert.questions.addFillItem') }}
            </t-button>
          </div>
          <div>
            <!-- 答案是否支持乱序 -->
            <t-checkbox :checked="localUserQuestion.disorder === 2"
              @change="onMultipleFillInBlanksChange($event, localUserQuestion)">
              {{ t('questions.answerSupportRandomOrder') }}
            </t-checkbox>
          </div>
        </div>
        <div v-if="localUserQuestion.questionContent == '<p><br></p>'" style="color: red">
          {{ t('insert.questions.pleasefillinthquestion') }}
        </div>
      </QuestionItemInputCell>
      <QuestionItemCell v-if="isProgrammingQuestion(localUserQuestion.questionType)"
        :label="t('insert.questions.programmingLanguage')" :required="true">
        <t-select v-model="localUserQuestion.codeContent.language" style="width: 200px">
          <t-option v-for="languageItem in programmingLanguageList" :key="languageItem.value"
            :label="languageItem.label" :value="languageItem.value" />
        </t-select>
      </QuestionItemCell>
      <QuestionItemCell v-if="isProgrammingQuestion(localUserQuestion.questionType)"
        :label="t('insert.questions.code')">
        <template-resourceLibrary-components-CodeEditor v-model="localUserQuestion.codeContent.code"
          :language="localUserQuestion.codeContent.language" :read-only="false" />
      </QuestionItemCell>
      <!-- 03 选项 A B C D -->
      <QuestionItemCell v-if="isOptionSelectQuestionType(localUserQuestion.questionType)"
        :label="t('insert.questions.options')" :required="true">
        <div class="option-nav">
          <div v-for="(optionItem, optionIndex) in localUserQuestion.options" class="option-nav-item">
            <!-- 根据类型加载不同的选项组件 -->
            <QuestionItemOptionCell :key="optionItem.optionId" :option-index="optionIndex"
              :text-content="optionItem.optionContent" :editor-toolbar-config="{
                excludeKeys: EDITOR_EXCLUDE_KEYS,
              }" @update-item-option="doUpateOption(optionItem, $event)">
              <template v-if="isSingleOptionSelectQuetionType(
                localUserQuestion.questionType,
              )
                " #markAsRightAnswer>
                <t-radio :value="optionItem.rightFlag" :checked="`${optionItem.rightFlag}` === '1'" @change="
                  rightAnswerChange(
                    $event,
                    optionItem,
                    'single',
                    localUserQuestion,
                  )
                  ">{{ t('insert.questions.markAsRightAnswer') }}</t-radio>
                <t-button theme="danger" variant="text" size="small" style="margin-left: 12px"
                  @click="delDataOption(localUserQuestion, optionIndex)">{{ t('insert.questions.delBtnText')
                  }}</t-button>
              </template>
              <template v-else="isMultipleOptionSelectQuetionType(
                localUserQuestion.questionType,
              )
                " #markAsRightAnswer>
                <t-checkbox :value="optionItem.rightFlag" :checked="`${optionItem.rightFlag}` === '1'"
                  @change="rightAnswerChange($event, optionItem, 'multiple')">{{ t('insert.questions.markAsRightAnswer')
                  }}</t-checkbox>
                <t-button theme="danger" variant="text" size="large" style="margin-left: 12px"
                  @click="delDataOption(localUserQuestion, optionIndex)">{{ t('insert.questions.delBtnText')
                  }}</t-button>
              </template>
            </QuestionItemOptionCell>

            <div v-if="
              optionIndex == 0 && optionItem.optionContent == '<p><br></p>'
            " style="color: red">
              {{ t('insert.questions.pleasefillintheoption') }}
            </div>
          </div>
        </div>
        <t-button size="large" @click="addDataOption(localUserQuestion)">
          <template #icon> <add-icon /></template>
          {{ t('insert.questions.addOptions') }}
        </t-button>
      </QuestionItemCell>
      <!-- 判断题选项编辑 - 改为和单选题一样的可编辑形式 -->
      <QuestionItemCell v-if="isTrueOrFalseQuestion(localUserQuestion.questionType)"
        :label="t('insert.questions.options')" :required="true">
        <div class="option-nav">
          <div v-for="(optionItem, optionIndex) in localUserQuestion.options" class="option-nav-item">
            <!-- 判断题选项编辑组件 -->
            <QuestionItemOptionCell :key="optionItem.optionId" :option-index="optionIndex"
              :text-content="optionItem.optionContent" :editor-toolbar-config="{
                excludeKeys: EDITOR_EXCLUDE_KEYS,
              }" @update-item-option="doUpateOption(optionItem, $event)">
              <template #markAsRightAnswer>
                <t-radio :value="optionItem.rightFlag" :checked="`${optionItem.rightFlag}` === '1'" @change="
                  rightAnswerChange(
                    $event,
                    optionItem,
                    'single',
                    localUserQuestion,
                  )
                  ">{{ t('insert.questions.markAsRightAnswer') }}</t-radio>
              </template>
            </QuestionItemOptionCell>

            <div v-if="
              optionIndex == 0 && optionItem.optionContent == '<p><br></p>'
            " style="color: red">
              {{ t('insert.questions.pleasefillintheoption') }}
            </div>
          </div>
        </div>
      </QuestionItemCell>
      <!-- 排序答案 -->
      <QuestionItemCell v-if="isSortingQuestion(localUserQuestion.questionType)"
        :label="t('insert.questions.sortAnswer')" :required="true">
        <div style="display: flex;justify-content: space-between;margin: 10px 0;">

          <t-alert theme="warning" message="排序项顺序为正确答案顺序，书籍打包时自动生成乱序排序项。单选项卡支持多张图片上传，视频与音频文件每类仅支持上传一个"
            style="width: 890px;justify-content: flex-start;" />
          <div style="display: flex;align-items: center;">
            选项高度设置： <t-input-number v-model="sortOptionHeight" :readonly="optionHeightReadonly" :min="1" :max="1000"
              theme="normal" :step="1" suffix="像素" style="margin-right: 10px" />
            <t-button @click="resetOptionHeight">{{ optionHeightReadonly ? '自定义高度' : '确定' }}</t-button>
          </div>
        </div>
        <div class="option-nav">
          <div v-for="(optionItem, optionIndex) in localUserQuestion.options" class="option-nav-item">
            <!-- 根据类型加载不同的选项组件 -->
            <QuestionItemOptionCell :key="optionItem.optionId" :option-index="optionIndex"
              :text-content="optionItem.optionContent" :editor-toolbar-config="{
                excludeKeys: EDITOR_EXCLUDE_KEYS,
              }" @update-item-option="doUpateSortingOption(optionItem, $event)">
              <template #markAsRightAnswer>
                <t-button theme="danger" variant="text" size="small" style="margin-left: 12px"
                  @click="delDataOption(localUserQuestion, optionIndex)">{{ t('insert.questions.delBtnText')
                  }}</t-button>
              </template>
            </QuestionItemOptionCell>
          </div>
        </div>
        <div style="
            display: flex;
            justify-content: space-between;
            align-content: center;
          ">
          <t-button size="large" @click="addSortingOption(localUserQuestion)">
            <template #icon> <add-icon /></template>
            {{ t('insert.questions.addSort') }}
          </t-button>

        </div>
      </QuestionItemCell>
      <!-- 简答部分formItem -->

      <QuestionItemInputCell v-if="isDescriptiveAnswerQuestion(localUserQuestion.questionType)"
        :label="t('insert.questions.referenceAnswer')" :required="true" :item-content="localUserQuestion.rightAnswer"
        :editor-textbar-config="{ height: 140 }" @update-item-stem="doUpateAnswerInShort(localUserQuestion, $event)" />
      <!-- 编程题答案 -->
      <QuestionItemInputCell v-if="isProgrammingQuestion(localUserQuestion.questionType)"
        :label="t('insert.questions.referenceAnswer')" :required="true" :item-content="localUserQuestion.rightAnswer"
        :editor-textbar-config="{ height: 140 }" @update-item-stem="doUpateAnswerInShort(localUserQuestion, $event)" />
      <!-- 连线题 部分 -->
      <QuestionItemCell v-if="isMatchingQuestions(localUserQuestion.questionType)"
        :label="t('insert.questions.connection')" class="question-item-matching">
        <template-questions-onedit-matchingQuestion :left-option="formatedMatchingQuestionLeftOptions"
          :right-option="formatedMatchingQuestionRightOptions"
          :matching-result="formatedMatchingQuestionConnectionOptions" :editor-toolbar-config="{
            excludeKeys: EDITOR_EXCLUDE_KEYS,
          }" @option-content-update="
            optionContentUpdateOnChange($event, localUserQuestion)
            " />

      </QuestionItemCell>
      <!-- 04 解析内容 -->
      <QuestionItemInputCell :label="t('insert.questions.analysis')" :required="false"
        :item-content="localUserQuestion.analysis" :editor-textbar-config="{ height: 140 }"
        @update-item-stem="doUpateAnalysis(localUserQuestion, $event)" />

      <!-- 知识点提示 -->
      <QuestionItemCell :label="t('forms.fields.knowledgeReference')">
        <t-button @click="showBookChapterPopup = true" v-text="t('base.add')"></t-button>
        <ul v-if="localUserQuestion.sectionReferTo?.length > 0" style="list-style: none">
          <li v-for="(sectionRefers, index) in localUserQuestion.sectionReferTo">
            <t-breadcrumb>
              <t-icon name="delete" style="margin-right: 10px" @click="deleteSectionRefer(index)" />
              <t-breadcrumb-item v-for="sectionInfo in sectionRefers">
                <template #icon><t-icon name="bookmark" /></template>{{ sectionInfo.name }}
              </t-breadcrumb-item>
            </t-breadcrumb>
          </li>
        </ul>
      </QuestionItemCell>

      <!-- 是否显示标题 -->
      <div style="width: 100%; display: flex">
        <div style="padding-right: 30px">{{ t('questions.showTitle') }}：</div>
        <t-radio-group :value="questionTypeShow" @change="showTitleChange">
          <t-radio value="1">{{ t('questions.showTitle') }}</t-radio>
          <t-radio value="0">{{ t('questions.hideTitle') }}</t-radio>
          <!-- <t-radio
          v-for="(typeItem, index) in QuestionTypesDescriptive"
          :key="index + '-' + typeItem.value"
          :value="typeItem.value"
        >
          {{ typeItem.label }}
        </t-radio> -->
        </t-radio-group>
      </div>
    </div>
  </div>
  <menus-toolbar-modal-chapterSelect :visibility="showBookChapterPopup" :chapter-id="props.chapterId"
    @close="showBookChapterPopup = false" @confirm="chapterSelectConfirmHandler" />
</template>

<script setup>
import { AddIcon } from 'tdesign-icons-vue-next'

import { listFolder as listBookQuestionFolder } from '@/api/book/bookQuestionFolder'
import { WangEditorToolbarConfigItemType } from '@/components/wangEditor/toolbarConfigItems.ts'
import {
  initTrueOrFalseQuestionOptions,
  isDescriptiveAnswerQuestion,
  isFillinBlanksQuestion,
  isMatchingQuestions,
  isMultipleOptionSelectQuetionType,
  isOptionSelectQuestionType,
  isProgrammingQuestion,
  isSingleOptionSelectQuetionType,
  isSortingQuestion,
  isTrueOrFalseQuestion,
} from '@/utils/questionTypeUtil.ts'
import {
  deepCopyObject,
  emptyCommonQuestionTemplate,
  emptyProgrammingSelectionTemplate,
  programmingLanguageList,
  QuestionTypesDescriptive,
  uuid,
} from '@/utils/quetions-utils.ts'

import QuestionItemCell from './questionItemCell.vue'
import QuestionItemInputCell from './questionItemInputCell.vue'
import QuestionItemOptionCell from './questionItemOptionCell.vue'
let questionTypeShow = $ref('')
const EVENT_UPDATE_PARAMS = 'updateParams'
let questionContentEditorHeight = $ref(140)
const optionHeightReadonly = ref(true)
const sortOptionHeight = ref(40)
const props = defineProps({
  isUpdate: {
    type: Boolean,
    default: false,
  },
  visibility: {
    type: Boolean,
    default: false,
  },
  bookId: {
    type: String,
    default: '',
  },
  chapterId: {
    type: String,
    default: '',
  },
  questionList: {
    type: Object,
    default: [],
  },
  continuousAdd: {
    type: Boolean,
    default: false,
  },
  previousQuestionType: {
    type: [String, Number],
    default: null,
  },
  questionCount: {
    type: Number,
    default: 0,
  },
})

//高度重置按钮
const resetOptionHeight = () => {
  optionHeightReadonly.value = !optionHeightReadonly.value
  // console.log(optionHeightReadonly.value)
  console.log(sortOptionHeight.value)
  if (optionHeightReadonly.value) {
    localStorage.setItem('sortOptionHeight', sortOptionHeight.value)
    console.log(sortOptionHeight.value)
    console.log('1')
  } else {
    console.log('2', sortOptionHeight.value)
  }
  // if (optionHeightReadonly.value) {
  //   const getOptionHeight = localStorage.getItem('sortOptionHeight')
  //   console.log(getOptionHeight)
  //   sortOptionHeight.value = getOptionHeight
  //   console.log('1')
  // } else {
  //   console.log('2')
  //   console.log(sortOptionHeight.value)
  //   localStorage.setItem('sortOptionHeight', sortOptionHeight.value)

  // }
}


function showTitleChange(value) {
  console.log(value)
  questionTypeShow = value
  if (localUserQuestion) {
    localUserQuestion.questionTypeShow = questionTypeShow
  }

  emitQuestionUpdate()
}

// 方法
function deleteSectionRefer(index) {
  localUserQuestion.sectionReferTo.splice(index, 1)
}
function onStemEditorCreated(editor) {
  stemEditor = editor
}
function questionTypeChangeHandler($event, questionItem) {
  questionItem.questionType = `${$event}`
  questionItem.rightAnswer = ''
  if (isTrueOrFalseQuestion($event)) {
    questionItem.options = initTrueOrFalseQuestionOptions(null)
  } else if (isOptionSelectQuestionType($event) || isSortingQuestion($event)) {
    questionItem.options = deepCopyObject(emptyCommonQuestionTemplate.options)
  } else if (isMatchingQuestions($event)) {
    initMatchingQuestionOptions(questionItem)
  }
  emitQuestionUpdate()
}
function formatToMatchingQuestionLeftOptions(questionItem) {
  if (props.isUpdate) {
    return questionItem.options.filter((opt) => `${opt.optionPosition}` === '1')
  } else {
    // 如果是创建模式，默认初始化两个选项
    return [
      {
        optionContent: '',
        optionId: uuid(),
        optionPosition: 1,
      },
      {
        optionContent: '',
        optionId: uuid(),
        optionPosition: 1,
      },
    ]

    // 测试数据
    // return [ { id: uuid(), optionContent: '林则徐', optionPosition: 1}, { id: uuid(), optionContent: '爱迪生', optionPosition: 1}, { id: uuid(), optionContent: '肯尼迪', optionPosition: 1}, { id: uuid(), optionContent: '黄飞鸿', optionPosition: 1}, { id: uuid(), optionContent: '小泉红一郎', optionPosition: 1} ]
  }
}

function formatToMatchingQuestionRightOptions(questionItem) {
  if (props.isUpdate) {
    return questionItem.options.filter((opt) => `${opt.optionPosition}` === '2')
  } else {
    // 如果是创建模式，默认初始化两个选项
    return [
      {
        optionContent: '',
        optionId: uuid(),
        optionPosition: 2,
      },
      {
        optionContent: '',
        optionId: uuid(),
        optionPosition: 2,
      },
    ]

    // 测试数据
    // return [{ id: uuid(), optionContent: '美国', optionPosition: 2}, { key: uuid(), optionContent: '中国', optionPosition: 2}, { id: uuid(), optionContent: '日本', optionPosition: 2}]
  }
}
function formatToMatchingQuestionConnectionOptions(questionItem) {
  let connectionOpts = []
  if (!props.isUpdate) {
    return connectionOpts
  }
  try {
    if (typeof questionItem.rightAnswer === 'string') {
      connectionOpts = JSON.parse(questionItem.rightAnswer)
    }
    return connectionOpts.map(({ source, target }) => {
      return { source, target }
    })
  } catch (err) { }
  return connectionOpts
}
function delDataOption(questionItem, optionIndex) {
  questionItem.options.splice(optionIndex, 1)
  emitQuestionUpdate()
}
function rightAnswerChange(checked, optItem, type, item) {
  if (type === 'single') {
    if (checked) {
      item.options.forEach((option) => {
        option.rightFlag = 0
      })
    }
    optItem.rightFlag = checked ? 1 : 0
  } else if (type === 'multiple') {
    optItem.rightFlag = optItem.rightFlag === 1 ? 0 : 1
  }
  emitQuestionUpdate()
}
function onMultipleFillInBlanksChange(checkedOrNot, item) {
  if (checkedOrNot) {
    item.disorder = 2
  } else {
    item.disorder = 1
  }
  emitQuestionUpdate()
}
function optionContentUpdateOnChange(matchingOpts, item) {
  // 左侧选项，右侧选项，连接信息一起获得
  item.options = [
    ...matchingOpts.leftOption.map((opt) => {
      return {
        ...opt,
        optionPosition: 1,
        optionHeight: 500,
      }
    }),
    ...matchingOpts.rightOption.map((opt) => {
      return {
        ...opt,
        optionPosition: 2,
        optionHeight: 500,
      }
    }),
  ]
  item.rightAnswer = JSON.stringify(
    matchingOpts.matchingResult.map((mr) => {
      return {
        source: mr.leftOptIndex,
        target: mr.rightOptIndex,
      }
    }),
  )
  emitQuestionUpdate()
}
function addSortingOption(item) {
  item.options.push({ optionContent: '', optionId: uuid() })
}
function doUpateSortingOption(optionItem, newValue) {
  optionItem.optionContent = newValue
  emitQuestionUpdate()
}
function emitQuestionUpdate() {
  console.log('题目更新前questionRemark值:', localUserQuestion.questionRemark)

  // 创建完整对象的深拷贝，确保所有属性都传递
  const questionDataCopy = JSON.parse(JSON.stringify([localUserQuestion]))
  const getOptionHeight = localStorage.getItem('optionHeight')
  if (localUserQuestion) {
    localUserQuestion.questionTypeShow = questionTypeShow
    localUserQuestion.optionHeight = getOptionHeight ? getOptionHeight : 500
  }

  console.log('题目更新后questionData',
    localUserQuestion)


  emit(EVENT_UPDATE_PARAMS, {
    questionData: [localUserQuestion],
    questionTypeShow,
    questionRemark: localUserQuestion.questionRemark,
    optionsHeight: getOptionHeight ? getOptionHeight : 500,
  })
}
function appendBlanks() {
  stemEditor.insertText('###请输入正确答案###')
}
function chapterSelectConfirmHandler(chapterTreeData) {
  showBookChapterPopup = false
  localUserQuestion.sectionReferTo = localUserQuestion.sectionReferTo || []
  localUserQuestion.sectionReferTo.push(chapterTreeData)
}

// 业务数据
const EDITOR_EXCLUDE_KEYS = [
  WangEditorToolbarConfigItemType.HEADER,
  WangEditorToolbarConfigItemType.UNDERLINE,
  WangEditorToolbarConfigItemType.SPLIT_LINE,
  WangEditorToolbarConfigItemType.EMOTION,
  WangEditorToolbarConfigItemType.COLOR,
  WangEditorToolbarConfigItemType.BG_COLOR,
  WangEditorToolbarConfigItemType.BLOCKQUOTE,
  WangEditorToolbarConfigItemType.BULLETED_LIST,
  WangEditorToolbarConfigItemType.NUMBERED_LIST,
  WangEditorToolbarConfigItemType.JUSTIFY_CENTER,
  WangEditorToolbarConfigItemType.JUSTIFY_LEFT,
  WangEditorToolbarConfigItemType.JUSTIFY_RIGHT,
  WangEditorToolbarConfigItemType.INSERT_LINK,
  WangEditorToolbarConfigItemType.TODO,
  WangEditorToolbarConfigItemType.GROUP_INDENT,
  WangEditorToolbarConfigItemType.GROUP_JUSTIFY,
  WangEditorToolbarConfigItemType.LINE_HEIGHT,
]

let showBookChapterPopup = $ref(false)
let localUserQuestion = $ref({})

let formatedMatchingQuestionLeftOptions = []
let formatedMatchingQuestionRightOptions = []
let formatedMatchingQuestionConnectionOptions = []
let stemEditor = $ref(null)

const doUpateOption = (optionItem, newValue) => {
  optionItem.optionContent = newValue
  emitQuestionUpdate()
}

const doUpdateStem = (item, newValue) => {
  item.questionContent = newValue
  // 最好可以知道内容可以占用多少空间，然后对editorwang的高度进行设置
  if (newValue.indexOf('<img') > -1) {
    questionContentEditorHeight = 400
  } else {
    questionContentEditorHeight = 140
  }
  emitQuestionUpdate()
}
const doUpateAnalysis = (item, newValue) => {
  item.analysis = newValue
  emitQuestionUpdate()
}
const doUpateAnswerInShort = (item, newValue) => {
  item.rightAnswer = newValue
  emitQuestionUpdate()
}
const doUpateRemark = (item, newValue) => {
  console.log('题目备注更新事件newValue原始值:', newValue)

  // 直接更新属性，不替换整个对象
  localUserQuestion.questionRemark = newValue

  console.log('题目备注更新后:', localUserQuestion.questionRemark)

  // 触发更新事件
  emitQuestionUpdate()
}

const emit = defineEmits([EVENT_UPDATE_PARAMS])
const addDataOption = (item) => {
  item.options.push({ optionContent: '', optionId: `c${uuid()}`, rightFlag: 0 })
}

// hook
onMounted(() => {
  if (!props.bookId && props.visibility) {
    MessagePlugin.error('bookId缺失')
  }
  if (props.visibility) {
    initQuestionList()
  }
  const getOptionHeight = localStorage.getItem('sortOptionHeight')
  if (getOptionHeight) {
    sortOptionHeight.value = getOptionHeight ? getOptionHeight : 40
  }
})

watch(
  () => props.visibility,
  (nval) => {
    if (nval) {
      if (!props.bookId) {
        MessagePlugin.error('bookId缺失')
      }
      initQuestionList()
      // 查询教材的默认文件夹题目更新事件emitQuestionUpdate
      listBookQuestionFolder({
        bookId: props.bookId,
      }).then((resp) => {
        let defaultFolderData
        defaultFolderData = resp.rows.find(
          (folder) => `${folder.defaultType}` === '1',
        )
      })
    }
  },
)

// 方法
function initQuestionList() {
  console.log('初始化题目信息，现在的props:', props)

  if (props.isUpdate && props.questionList?.[0]) {
    // 更新试题模式
    // 深度拷贝试题内容，防止污染父级的试题内容
    localUserQuestion = deepCopyObject(props.questionList[0])
  } else if (!props.isUpdate) {
    // 新增试题模式
    localUserQuestion = deepCopyObject(emptyCommonQuestionTemplate)

    // 如果是连续添加且已经添加过题目，延续上一题的设置
    if (props.continuousAdd && props.previousQuestionType && props.questionCount > 0) {
      localUserQuestion.questionType = props.previousQuestionType
      localUserQuestion.questionTypeShow = '0' // 第二题后默认隐藏标题

      // 根据题型初始化选项
      if (isTrueOrFalseQuestion(props.previousQuestionType)) {
        localUserQuestion.options = initTrueOrFalseQuestionOptions(null)
      } else if (isOptionSelectQuestionType(props.previousQuestionType) || isSortingQuestion(props.previousQuestionType)) {
        localUserQuestion.options = deepCopyObject(emptyCommonQuestionTemplate.options)
      } else if (isMatchingQuestions(props.previousQuestionType)) {
        // 连线题需要清空选项，让格式化函数重新初始化
        localUserQuestion.options = []
      }
    } else {
      localUserQuestion.questionTypeShow = '1' // 第一题默认显示标题
    }
  }

  console.log('localUserQuestion', localUserQuestion)
  console.log('props.questionList', props.questionList)
  // 修改回显逻辑
  questionTypeShow = localUserQuestion?.questionTypeShow || '1'

  // 兼容性写法，旧数据的disorder数值可能是true、false
  localUserQuestion.disorder = [1, 2].includes(localUserQuestion.disorder)
    ? localUserQuestion.disorder
    : localUserQuestion.disorder
      ? 2
      : 1
  localUserQuestion.codeContent =
    localUserQuestion.codeContent ||
    JSON.stringify(emptyProgrammingSelectionTemplate)
  try {
    localUserQuestion.codeContent = JSON.parse(localUserQuestion.codeContent)
  } catch (err) { }
  localUserQuestion.codeContent = Object.assign(
    emptyProgrammingSelectionTemplate,
    localUserQuestion.codeContent,
  )
  initMatchingQuestionOptions(localUserQuestion)
}
function initMatchingQuestionOptions(questionItem) {
  formatedMatchingQuestionLeftOptions =
    formatToMatchingQuestionLeftOptions(questionItem)
  formatedMatchingQuestionRightOptions =
    formatToMatchingQuestionRightOptions(questionItem)
  formatedMatchingQuestionConnectionOptions =
    formatToMatchingQuestionConnectionOptions(questionItem)
}
</script>
<style lang="less" scoped>
@import '@/assets/styles/_common.less';

.question-content-list {
  padding: 20px 30px 0px 30px;

  .content-item {
    padding: 10px;
    // box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.2);
    // border-radius: 5px;
    .base-flex-column;
    justify-content: flex-start;
    align-items: center;

    .item-header {
      width: 100%;
      background-color: rgb(239, 239, 239);
      border-radius: 5px;
      .base-flex-row;
      justify-content: space-between;
      align-items: center;

      .item-left {
        margin-left: 10px;
        color: #0d1117;
        font-size: large;
      }

      .item-right {
        .item-btn {
          margin-right: 20px;

          .arrow-icon-sty {
            margin-top: 3px;
            margin-right: 5px;
          }
        }

        .active {
          color: #0080ff;
        }
      }
    }

    .item-type-con {
      width: 100%;
      margin-top: 20px;
      margin-bottom: 20px;
      .base-flex-row;
      justify-content: flex-start;
      align-items: center;

      .type-label {
        width: 100px;
      }
    }

    .option-nav {
      width: 100%;

      .option-nav-item {
        width: 100%;
        margin-bottom: 55px;

        .choiceQuestion-main-header {
          width: calc(100% - 12px);
          border: 1px solid rgb(222, 222, 222);
          background-color: rgb(239, 239, 239);
          padding: 5px 5px;
          .base-flex-row;
          justify-content: space-between;
          align-items: center;
        }
      }
    }

    .truefalse-option-item {
      margin-right: 10px;

      &:last-child {
        margin-right: 0;
      }
    }
  }

  // .addContentToBlanks {
  //   display: inline-block;
  //   background: url('@/assets/images/note.svg') no-repeat;
  //   width: 36px;
  //   height: 36px;
  //   background-size: contain;
  // }
}
</style>
