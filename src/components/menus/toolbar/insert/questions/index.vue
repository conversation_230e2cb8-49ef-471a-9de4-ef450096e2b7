<template>
  <menus-button ico="questions" :text="t('insert.questions.text')" menu-type="popup" huge :popup-visible="popupVisible"
    @toggle-popup="togglePopup">
    <template #content>
      <!--  下拉菜单    -->
      <div class="umo-page-divider-dropdown">
        <div v-for="item in QuestionImportCategoryDropdownOptions" :key="item.value" class="umo-page-divider-item"
          :value="item.value" :title="item.label" @click="doShowPopup(item)">
          {{ item.label }}
        </div>
      </div>
    </template>
  </menus-button>
  <!-- 题目新建弹窗 -->
  <t-drawer v-model:visible="showQuestionPopup" attach="body" size="80%" mode="overlay" placement="right"
    :confirm-btn="t('insert.questions.add')" class="new-question-drawer" :header="t('editor.addQuestionItem')"
    :destroy-on-close="true" :close-on-overlay-click="false" :on-confirm="saveQuestions"
    :on-close-btn-click="onCancelAddingQuestion" @close="onCancelAddingQuestion">
    <template #footer>
      <div style="
          display: flex;
          justify-content: flex-start;
          align-items: center;
          width: 100%;
        ">
        <t-space>
          <t-checkbox v-model="continuousAdd">{{
            t('insert.questions.continuousAdd')
          }}</t-checkbox>
          <t-button theme="primary" @click="saveQuestions">{{
            t('insert.questions.add')
          }}</t-button>
          <t-button theme="default" @click="onCancelAddingQuestion">{{
            t('insert.questions.cancel')
          }}</t-button>
        </t-space>
      </div>
    </template>

    <CommonQuestionComp v-if="showQuestionPopup" :key="commonQuestionCompKey" :visibility="showQuestionPopup"
      :book-id="route.query.bookId" :chapter-id="route.query.chapterId" :continuous-add="continuousAdd"
      :previous-question-type="previousQuestionType" :question-count="questionCount"
      @update-params="handleParamsUpdate" />
  </t-drawer>
  <!-- 题库引入题目 弹窗 -->
  <menus-toolbar-modal-addQuestionFromResourceBank :close-on-overlay-click="false"
    :visibility="showQuestionResourceBankPopup" :book-id="route.query.bookId"
    @confirm="confirmHandler4ResourceBankPopup" @cancel="showQuestionResourceBankPopup = false" />
  <!-- excel引入题目 弹窗 -->
  <menus-toolbar-modal-importQuestionFromFile :close-on-overlay-click="false"
    :visibility="showImportQuestionByExternalFilePopup" :book-id="route.query.bookId"
    :chapter-id="route.query.chapterId" @confirm="confirmHandler4ImportQuestionPopup"
    @cancel="showImportQuestionByExternalFilePopup = false" />
</template>

<script setup>
import { nextTick } from 'vue'
import { useRoute } from 'vue-router'

import { addQuestion, addQuestionBatch } from '@/api/book/bookQuestion'
import {
  deriveBookQuestionObjectWithMinimumProperty,
  deriveUserQuestionObjectWithMinimumProperty,
} from '@/utils/questionTypeUtil'
import {
  deepCopyObject,
  emptyCommonQuestionTemplate,
  QuestionImportCategoryDropdownOptions,
  QuestionNewTypes,
} from '@/utils/quetions-utils'

import CommonQuestionComp from '../components/questions/commonQuestionComp.vue'
import { queryBookResourceDefaultFolder } from './question-folder-mixins'

const { popupVisible, togglePopup } = usePopup()
const { editor, batchInsert } = useStore()
const route = useRoute()
// 一级试题类型数据列表
let currentFirstLevelQuestionType = $ref('')

const showQuestionPopup = ref(false)
let showImportQuestionByExternalFilePopup = $ref(false)
let showQuestionResourceBankPopup = $ref(false)
let currentQuestionLabel = $ref('')

const continuousAdd = $ref(false)
let commonQuestionCompKey = $ref(0)
let previousQuestionType = $ref(null) // 记录上一题的题型
let questionCount = $ref(0) // 记录当前是第几题

const parentParams = $ref({
  firstLevelQuestionType: currentFirstLevelQuestionType,
  questionsDataList: [],
  questionTitle: '',
})

let tmpQuestionsDataList = {
  questionsDataList: [],
}

let defaultFolderData = $ref(null)

// hook
onMounted(() => {
  queryBookResourceDefaultFolder(route.query.bookId).then(
    (defaultFolderObj) => {
      defaultFolderData = defaultFolderObj
    },
  )
})

// 方法
async function confirmHandler4ResourceBankPopup(selectedQuestions) {
  closeAllPopups()
  try {
    if (!route.query.bookId) {
      MessagePlugin.error(t('insert.questions.bookIdEmpty'))
      return
    }
    if (!route.query.chapterId) {
      MessagePlugin.error(t('insert.questions.chapterIdEmpty'))
      return
    }
    if (!defaultFolderData) {
      MessagePlugin.error(t('insert.questions.textbookGeneralEmpty'))
      return
    }
    let allPendingSavedBookQuestions = []

    // 先把教材试题先存起来
    allPendingSavedBookQuestions = allPendingSavedBookQuestions.concat(
      selectedQuestions
        .filter((question) => question._bookQuestionResource)
        .map((questionItem) => {
          const bookQuestion =
            deriveBookQuestionObjectWithMinimumProperty(questionItem)
          bookQuestion.userQuestion =
            deriveUserQuestionObjectWithMinimumProperty(questionItem)
          return bookQuestion
        }),
    )
    console.log(allPendingSavedBookQuestions)
    const pendingSavedUserQuestions = selectedQuestions.filter(
      (question) => !question._bookQuestionResource,
    )
    console.log(pendingSavedUserQuestions)
    if (pendingSavedUserQuestions.length) {
      // 需要对题干解析部分富文本进行编码传输
      const mappedUserQuestions = pendingSavedUserQuestions.map(
        (questionItem) => buildBookQuestionSaveParam(questionItem),
      )
      // 创建book question，将user question和book question关联起来
      const savedBookQuestionsResp = await addQuestionBatch(mappedUserQuestions)
      console.log(savedBookQuestionsResp)
      // 最小化属性保存到编辑器中
      allPendingSavedBookQuestions = allPendingSavedBookQuestions.concat(
        savedBookQuestionsResp.data.map((savedBookQuestion) => {
          const bookQuestionObject =
            deriveBookQuestionObjectWithMinimumProperty(savedBookQuestion)
          bookQuestionObject.userQuestion =
            deriveUserQuestionObjectWithMinimumProperty(
              savedBookQuestion.userQuestion,
            )
          return bookQuestionObject
        }),
      )
      if (savedBookQuestionsResp.data) {
        MessagePlugin.success({
          content: t('insert.questions.successGenerated'),
        })
      }
    }
    console.log(allPendingSavedBookQuestions)
    batchInsert(allPendingSavedBookQuestions, 'questions', (file) => ({
      questionsList: [file],
    }))
    // allPendingSavedBookQuestions.forEach((question) => {
    //   console.log(question)
    //   const chain = editor.value.chain()
    //   chain
    //     .setParagraph()
    //     .questions({
    //       questionsList: [question],
    //     })
    //     .run()
    // })
  } catch (err) {
    console.error(err)
  }
}

function confirmHandler4ImportQuestionPopup(savedQuestionList) {
  closeAllPopups()

  const pendingSavedBookQuestionList = savedQuestionList.map((question) => {
    const bookQuestion = deriveBookQuestionObjectWithMinimumProperty(question)
    bookQuestion.userQuestion = deriveUserQuestionObjectWithMinimumProperty(
      question.userQuestion,
    )
    bookQuestion._bookQuestionResource = true
    return bookQuestion
  })

  pendingSavedBookQuestionList.forEach((question) => {
    console.log('question', question)
    editor.value
      .chain()
      .setParagraph()
      .questions({
        questionsList: [question],
      })
      .run()
  })
}
const handleParamsUpdate = (newParams) => {
  const { questionData } = newParams
  // 将试题的题干和解析等部分编码后保存，以防其中包含视频图片等情况
  tmpQuestionsDataList = questionData
}
const onCancelAddingQuestion = () => {
  console.log('取消添加题目')
  disableCtrlAInterceptor()
  showQuestionPopup.value = false
}


const handleKeyDown = (e) => {
  console.log('keydown', e)
  if (e.ctrlKey && (e.key === 'a' || e.key === 'A')) {
    console.log('我进来了', e)
    e.preventDefault(); // 阻止默认全选行为
    e.stopPropagation(); // 阻止事件冒泡
    console.log('Ctrl+A 被拦截');
  }
};


// 弹窗打开时添加监听  
const enableCtrlAInterceptor = () => {
  console.log('添加监听')
  document.addEventListener('keydown', handleKeyDown);
};

// 弹窗关闭时移除监听  
const disableCtrlAInterceptor = () => {
  console.log('移除监听')
  document.removeEventListener('keydown', handleKeyDown);
};

const doShowPopup = (item) => {
  currentFirstLevelQuestionType = item.value
  currentQuestionLabel = item.label

  if (QuestionNewTypes.CommonQuestion.value === item.value) {
    // 重置题目计数
    questionCount = 0
    previousQuestionType = null

    doResetCommonQuestionPoup()
    // 监听全局键盘事件
    enableCtrlAInterceptor()
    showQuestionPopup.value = true
  } else if (QuestionNewTypes.ImportFromQuestionItemRepo.value === item.value) {
    showQuestionResourceBankPopup = true
  } else if (QuestionNewTypes.ImportFromExternalProfile.value === item.value) {
    showImportQuestionByExternalFilePopup = true
  }
  popupVisible.value = false
}
function closeAllPopups() {
  showQuestionPopup.value = false
  showImportQuestionByExternalFilePopup = false
  showQuestionResourceBankPopup = false
}
function buildBookQuestionSaveParam(questionItem) {
  console.log('构建题目数据questionItem', questionItem)

  if (
    questionItem.codeContent &&
    typeof questionItem.codeContent === 'string'
  ) {
    questionItem.codeContent = JSON.parse(questionItem.codeContent)
  }

  return {
    questionType: questionItem.questionType,
    chapterId: route.query.chapterId,
    bookId: route.query.bookId,
    folderId: defaultFolderData.folderId,
    userQuestion: {
      questionTypeShow: questionItem.questionTypeShow,
      createSource: 1,
      codeContent: safeEncode(
        JSON.stringify({
          code: questionItem.codeContent?.code || '',
          language: questionItem.codeContent?.language || '',
        }),
      ),
      folderId: 0,
      questionType: questionItem.questionType,
      questionContent: encodeURIComponent(questionItem.questionContent),
      rightAnswer: questionItem.rightAnswer,
      analysis: encodeURIComponent(questionItem.analysis),
      disorder: questionItem.disorder,
      questionRemark: questionItem.questionRemark,
      options: questionItem.options.map((opt) => {
        return {
          optionContent: encodeURIComponent(opt.optionContent),
          rightFlag: opt.rightFlag,
          optionPosition: opt.optionPosition ?? '',
        }
      }),
    },
  }
}
async function saveQuestions() {
  console.log('保存题目函数执行')

  try {
    if (!route.query.bookId) {
      MessagePlugin.error(t('insert.questions.bookIdEmpty'))
      return false
    }
    if (!route.query.chapterId) {
      MessagePlugin.error(t('insert.questions.chapterIdEmpty'))
      return false
    }
    if (!defaultFolderData) {
      MessagePlugin.error(t('insert.questions.textbookGeneralEmpty'))
      return false
    }
    const toBeAddedQuestion = tmpQuestionsDataList[0]

    if (toBeAddedQuestion.questionType == 3) {
      const match = toBeAddedQuestion.questionContent?.match(/###(.*?)###/)
      if (match) {
        const contentBetweenHashes = match[1] // 获取 ### 和 ### 之间的内容

        // 判断内容是否为 HTML 标签
        const isHtmlTag =
          /^<([a-zA-Z][a-zA-Z0-9]*)\b[^>]*>.*<\/\1>$/.test(
            contentBetweenHashes,
          ) || /^<([a-zA-Z][a-zA-Z0-9]*)\b[^>]*\/?>$/.test(contentBetweenHashes) // 支持自闭合标签

        if (isHtmlTag) {
          MessagePlugin.error('更新失败，题干中答案格式不正确，请使用纯文本')
          return false
        }
      } else {
        MessagePlugin.error('更新失败，题干中缺少答案')
        return false
      }
    }

    if (!toBeAddedQuestion.questionTypeShow) {
      toBeAddedQuestion.questionTypeShow = '1'
    }
    console.log('~~~~~~~~~~~', toBeAddedQuestion)
    if (!toBeAddedQuestion) {
      MessagePlugin.error(t('insert.questions.completeQuestion'))
      return false
    }
    if (
      !toBeAddedQuestion.questionContent ||
      toBeAddedQuestion.questionContent === '<p><br></p>'
    ) {
      MessagePlugin.error(t('insert.questions.pleasefillinthquestion'))
      return false
    }
    if (
      (!toBeAddedQuestion.options[0].optionContent || toBeAddedQuestion.options[0].optionContent === '<p><br></p>') &&
      toBeAddedQuestion.questionType != 3 &&
      toBeAddedQuestion.questionType != 6 &&
      toBeAddedQuestion.questionType != 8
    ) {
      MessagePlugin.error(t('insert.questions.fillContent'))
      return false
    }

    if (
      (!toBeAddedQuestion.rightAnswer ||
        toBeAddedQuestion.rightAnswer === '<p><br></p>') &&
      toBeAddedQuestion.questionType == 6
    ) {
      MessagePlugin.error(t('insert.questions.fillContent'))
      return false
    }

    if (toBeAddedQuestion.questionType == 1) {
      if (
        toBeAddedQuestion.options.filter((item) => item.rightFlag).length == 0
      ) {
        MessagePlugin.error(t('insert.questions.correctAnswer'))
        return false
      }
    }

    if (toBeAddedQuestion.questionType == 8) {
      if (
        !toBeAddedQuestion.rightAnswer ||
        toBeAddedQuestion.rightAnswer.length <= 2
      ) {
        MessagePlugin.error('更新失败，请设置连线题答案')
        return false
      }
    }

    const savedQuestions = await addQuestion(
      buildBookQuestionSaveParam(toBeAddedQuestion),
    )

    if (savedQuestions.data) {
      // 添加成功后关闭弹窗
      const toBeSavedBookQuestion = deriveBookQuestionObjectWithMinimumProperty(
        savedQuestions.data,
      )
      toBeSavedBookQuestion.userQuestion =
        deriveUserQuestionObjectWithMinimumProperty(
          savedQuestions.data.userQuestion,
        )

      // 确保 remark 字段存在且不为 null，如果为 null 则设置为空字符串
      // 假设 toBeAddedQuestion 是从 tmpQuestionsDataList[0] 获取的，并且它有 remark 属性
      const getOptionHeight = localStorage.getItem('optionHeight')
      const getSortOptionHeight = localStorage.getItem('sortOptionHeight')
      toBeSavedBookQuestion.optionHeight = getOptionHeight || 500
      toBeSavedBookQuestion.sortOptionHeight = getSortOptionHeight || 90
      toBeSavedBookQuestion.userQuestion.remark = toBeAddedQuestion.remark || ''
      toBeSavedBookQuestion.questionTypeShow =
        toBeAddedQuestion.questionTypeShow
      toBeSavedBookQuestion.userQuestion.sectionReferTo =
        toBeAddedQuestion.sectionReferTo
      console.log('toBeSavedBookQuestion~~~~~~~~~~~~~~~~~~~', toBeSavedBookQuestion)
      editor.value.chain()
        .command(({ tr, dispatch, state }) => {
          if (dispatch) {
            const { schema } = state
            const wrapper = schema.node('questions', {
              questionsList: [toBeSavedBookQuestion],
            })
            tr.insert(editor.value.state.selection.anchor - 1, wrapper)
          }

          return true
        })
        .focus()
        .run()

      if (continuousAdd) {
        // 记录当前题型，用于下一题延续
        previousQuestionType = toBeAddedQuestion.questionType
        questionCount++

        doResetCommonQuestionPoup() // 重置表单数据
        await nextTick() // 等待 DOM 更新和响应式效果处理完毕
        commonQuestionCompKey++ // 更改 key 以强制重新渲染组件

        MessagePlugin.success(t('insert.questions.successAddedAndContinue')) // 提示添加成功并可继续
        // 弹窗将保持打开状态
      } else {
        MessagePlugin.success(t('insert.questions.successAdded')) // 提示添加成功
        closeAllPopups()
      }
    }
  } catch (err) {
    console.error('添加问题失败:', err)
    MessagePlugin.error(err)
  }
}

// 添加安全的编码和解码函数
function safeEncode(content) {
  if (!content) return ''
  try {
    // 检查内容是否已经被编码
    const isEncoded = (str) => {
      try {
        return decodeURIComponent(str) !== str
      } catch (e) {
        return false
      }
    }

    // 如果已经编码过，直接返回
    if (isEncoded(content)) {
      return content
    }
    return encodeURIComponent(content)
  } catch (e) {
    console.error('编码内容失败:', e)
    return content
  }
}

function doResetCommonQuestionPoup() {
  parentParams.questionTitle = ''
  parentParams.questionsDataList = [deepCopyObject(emptyCommonQuestionTemplate)]
}



</script>

<style lang="less" scoped>
@import '@/assets/styles/_mixins.less';
@import '@/assets/styles/_common.less';

.umo-page-divider-dropdown {
  width: 110px;

  .umo-page-divider-item {
    padding: 5px 5px;
    cursor: pointer;
    border-radius: var(--umo-radius);

    &:hover {
      background-color: var(--td-bg-color-container-hover);
    }
  }
}

.header-con {
  width: 100%;
  .base-flex-row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  .header-con-left {
    font-size: 20px;
    .base-flex-row;
    justify-content: flex-start;
    align-items: center;
    margin-left: 30px;

    .back-btn {}
  }

  .center-con {
    font-size: 20px;
  }

  .right-con {
    margin-right: 30px;
  }
}
</style>
<style lang="less">
.new-question-drawer.umo-drawer--open {
  .umo-drawer__mask {
    background-color: rgba(0, 0, 0, 0.6);
  }
}
</style>
