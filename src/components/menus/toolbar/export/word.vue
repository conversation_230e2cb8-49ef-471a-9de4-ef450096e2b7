<template>
  <menus-button text="Word" ico="word" huge @click="handleExport" />
</template>

<script setup>
import { exportBookChapter } from '@/api/book/chapter.js'
const { chapterId } = useStore()
// 导出
function handleExport() {
  if (!chapterId.value) {
    MessagePlugin.error('请选择要导出的章节')
    return
  }
  //  导出
  exportBookChapter({
    chapterIdList: [chapterId.value],
  }).then((res) => {
    MessagePlugin.success(`已发起导出任务，请到任务中心查看`)
  })
}
</script>

<style lang="less" scoped></style>
