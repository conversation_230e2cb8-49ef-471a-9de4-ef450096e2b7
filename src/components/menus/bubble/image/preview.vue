<template>
  <menus-button ico="image-preview" :text="t('bubbleMenu.image.preview')" @menu-click="openImageViewer" />
</template>

<script setup lang="ts">
import { getSelectionNode } from '@/extensions/selection'
const { editor, imageViewer } = useStore()

const openImageViewer = () => {
  const image = getSelectionNode(editor.value)
  imageViewer.value.current = image.attrs.id
  console.log(imageViewer.value.current)
  imageViewer.value.visible = true
}
</script>
