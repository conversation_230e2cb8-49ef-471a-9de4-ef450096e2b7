<template>
    <t-dialog :visible="visible" @close="close" @open="openView" attach="body" :header="t('version.text')" width="800"
        :footer="false">
        <div class="version-content">
            <t-timeline>
                <t-timeline-item label="2025-09-17">
                    <div class="timeline-title">版本号:1.3.14</div>
                    <div class="timeline-custom-content">【编辑器】颜色模块新增取色器功能。</div>
                    <div class="timeline-custom-content">【编辑器】新增环绕组件添加标题样式。</div>
                    <div class="timeline-custom-content">【编辑器】图标组件改动排列顺序，新加组件优先前面展示。</div>
                    <div class="timeline-custom-content">【编辑器】新增试卷和作业组件预览可以显示分数功能。</div>
                    <div class="timeline-custom-content">【编辑器】资源库上传资源需要增加上传资源总数量。</div>
                    <div class="timeline-custom-content">【编辑器】解决版式图片裁剪功能将图片自动压缩不清晰的情况，截图操作更加便捷。</div>
                    <div class="timeline-custom-content">【编辑器】资源库已上传的文件资源文件名太长，看不见具体内容，需要全部显示。</div>
                    <div class="timeline-custom-content">【编辑器】版式图片中图片名称由于字数过多可以延出图片自身宽度进行展示。</div>
                </t-timeline-item>
                <t-timeline-item label="2025-09-10">
                    <div class="timeline-title">版本号:1.3.13</div>
                    <div class="timeline-custom-content">【编辑器】颜色控件面板中 更多颜色色板颜色拖动，点击【确定使用】会在最近使用显示该颜色</div>
                    <div class="timeline-custom-content">【编辑器】标题字体可以变成细体， 旧教材数据不受到影响</div>
                    <div class="timeline-custom-content">【编辑器】行内图片 新增图片位置设置选项</div>
                    <div class="timeline-custom-content">【编辑器】行内图片、版式图片、环绕图片：新增输入宽高改变图片大小功能</div>
                    <div class="timeline-custom-content">【编辑器】新增默认值 字体大小18 、字体样式 黑体、行间距2倍</div>
                    <div class="timeline-custom-content">【编辑器】新增编辑器辅助线功能，在【页面设置】【网格线】中实现</div>
                    <div class="timeline-custom-content">【编辑器】更改了版式图片标题对齐方式显示</div>
                    <div class="timeline-custom-content">【编辑器】新增修改上下角标快捷方式。ctrl+[ 上角标、ctrl+]下角标</div>
                    <div class="timeline-custom-content">【编辑器】针对打印预览样式进行了调整，打印显示更和谐</div>
                    <div class="timeline-custom-content">【编辑器】版权内没有的字体统一设置为黑体了</div>
                </t-timeline-item>
                <t-timeline-item label="2025-09-03">
                    <div class="timeline-title">版本号:1.3.12</div>
                    <div class="timeline-custom-content">
                        修复连线题样式
                    </div>
                    <div class="timeline-custom-content">修复排序题样式</div>
                    <div class="timeline-custom-content">修复页面设置-页面大小保存问题</div>

                </t-timeline-item>
                <t-timeline-item label="2025-08-20">
                    <div class="timeline-title">版本号:1.3.11</div>
                    <div class="timeline-custom-content">
                        新增行内公式图片上中位置调整方式，可以通过自己输入合适的值，进行上下位置的调整。默认值0，两行10px左右其他情况根据自身调整。</div>
                    <div class="timeline-custom-content">修复模板管理章头节头字体颜色配置</div>
                    <div class="timeline-custom-content">修复模板管理模板配置页UI样式</div>
                    <div class="timeline-custom-content">修复模板管理设置中页码位置以及样式</div>
                    <div class="timeline-custom-content">修复模板管理字体颜色与快捷方式统一</div>
                    <div class="timeline-custom-content">修复表格中多行多列合并产生的问题</div>
                    <div class="timeline-custom-content">修复字间距数值设置后，再次点击数值间距数值回显功能。</div>
                    <div class="timeline-custom-content">修复折叠组件内各个组件之间的合理间距</div>
                    <div class="timeline-custom-content">修复选中的文字再使用图标组件，图标组件成为文字背景图（只能选择一段文字，不支持多段）</div>
                </t-timeline-item>

                <t-timeline-item label="2025-08-13">
                    <div class="timeline-title">版本号:1.3.10</div>
                    <div class="timeline-custom-content">新增主题样式</div>
                    <div class="timeline-custom-content">新增版本管理模块</div>
                    <div class="timeline-custom-content">新增版式图片、行内图片、视频组件修改时，可以从资源库上传</div>
                    <div class="timeline-custom-content">新增气泡组件，其内容可设置为图片形式</div>
                    <div class="timeline-custom-content">修复背景图片显示问题</div>
                    <div class="timeline-custom-content">修复块组件UI布局问题,背景图片全部显示</div>
                    <div class="timeline-custom-content">修复下拉菜单中字号选项的排序出现混乱问题</div>
                    <div class="timeline-custom-content">修复表格标题字号默认大小问题</div>
                    <div class="timeline-custom-content">删除背景移除、背景设置选项</div>
                </t-timeline-item>

                <t-timeline-item label="2025-08-09">
                    <div class="timeline-title">版本号:1.3.9</div>
                    <div class="timeline-custom-content">新增折叠组件图标颜色选择</div>
                    <div class="timeline-custom-content">修复画廊对话框上移问题</div>
                    <div class="timeline-custom-content">修复图片名称一行显示。图片名边距减小。</div>
                    <div class="timeline-custom-content">修复连线题样式</div>
                    <div class="timeline-custom-content">修复画廊增加设置大小功能.画廊下边空白距离太大。</div>
                    <div class="timeline-custom-content">修复右侧滚动条样式</div>
                    <div class="timeline-custom-content">修复表格标题字号默认大小问题</div>
                </t-timeline-item>



            </t-timeline>
        </div>
    </t-dialog>
</template>

<script setup>

import { ref } from 'vue'

const visible = ref(false)

const openView = () => {
    visible.value = true
}

const close = () => {
    visible.value = false
}

defineExpose({ openView })

</script>


<style lang="less" scoped>
.version-content {
    padding: 20px;
    height: 500px;
    overflow-y: auto;

    &::-webkit-scrollbar {
        width: 0px;
    }

    .timeline-title {
        font-weight: bold;
        margin-bottom: 10px;
        background-color: #366ef4;
        color: #fff;
        padding: 5px 0px 5px 10px;
        border-radius: 4px;
    }

    .timeline-custom-content {
        line-height: 30px;
        color: #999;

        &::before {
            content: '•';
            margin-right: 10px;
            color: #333;
        }
    }
}
</style>
