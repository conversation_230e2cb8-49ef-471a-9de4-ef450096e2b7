<template>
  <div class="icon-template">
    <div class="tabGroup">
      <div class="btn" :class="{ active: queryParams.type == '1' }">

        <t-dropdown :options="boxes" trigger="click" class="dropdown" @click="dropDownClick">
          <t-space class="text">
            {{ t('template.systemIcon') }}
            <t-icon name="chevron-down" size="16" />
          </t-space>
        </t-dropdown>
      </div>
      <div class="btn" :class="{ active: queryParams.type == '2' }" @click="setActive('2')">
        <t-space class="text">{{ t('template.localIcon') }}
        </t-space>
      </div>
    </div>


    <div class="component-main">
      <div v-if="queryParams.type == '1'">
        <t-loading :loading="loading" text="拼命加载中...">
          <div v-if="dataList.length === 0 && iconList.length === 0" class="nodata">
            <t-space direction="vertical" align="center">
              <t-empty />
            </t-space>
          </div>
          <div v-else style="position: relative">
            <t-collapse>
              <t-collapse-panel :header="t('template.backgroundBorder')">
                <template #default>
                  <div style="max-height: 300px;overflow-y: auto">
                    <div v-for="(item, index) in dataList" :key="index" class="template-bg"
                      @click="handleClick(1, item)">
                      <img :src="item.url" />
                    </div>
                  </div>
                </template>

              </t-collapse-panel>
              <t-collapse-panel :header="t('template.backgroundIcon')">
                <template #default>
                  <div class="icon-template-bg" style="max-height: 300px;overflow-y: auto">
                    <div v-for="(item, index) in iconList" :key="index">
                      <div class="item" @click="handleClick(2, item)">
                        <img :src="item.url" />
                      </div>
                    </div>
                  </div>
                </template>

              </t-collapse-panel>
            </t-collapse>
          </div>
        </t-loading>
      </div>
      <div v-if="queryParams.type == '2'">
        <t-loading :loading="loading" text="拼命加载中...">
          <t-collapse>
            <t-collapse-panel :header="t('template.backgroundBorder')">
              <template #headerRightContent>
                <t-space size="small">
                  <t-button size="small" @click="addTemplateImage(1)">{{ t('template.add') }}</t-button>
                </t-space>
              </template>
              <template #default>
                <div :style="`max-height: ${maxHeight}px;overflow-y: auto`">
                  <div v-for="(item, index) in dataList" :key="index" class="template-bg" @click="handleClick(1, item)"
                    @mouseover="handleMouseOver(1, index)" @mouseleave="handleMouseLeave(1, index)">
                    <img :src="item.url" />
                    <div v-if="item.check" class="template-bg-btn">
                      <t-button size="small" style="margin-right: 5px" theme="danger"
                        @click.stop="handleDelete(item)">{{
                          t('template.del') }}</t-button>
                    </div>
                  </div>
                </div>
              </template>

            </t-collapse-panel>
            <t-collapse-panel :header="t('template.backgroundIcon')">
              <template #headerRightContent>
                <t-space size="small">
                  <t-button size="small" @click="addTemplateImage(2)">{{ t('template.add') }}</t-button>
                </t-space>
              </template>

              <template #default>
                <div :style="`max-height: ${maxHeight}px;overflow-y: auto`">
                  <div class="icon-template-bg">
                    <div v-for="(item, index) in iconList" :key="index" @mouseover="handleMouseOver(2, index)"
                      @mouseleave="handleMouseLeave(2, index)">
                      <div class="item" @click="handleClick(2, item)">
                        <img :src="item.url" />

                        <div v-if="item.check" class="template-bg-btn">
                          <t-button size="small" style="margin-right: 5px" theme="danger"
                            @click.stop="handleDelete(item)">{{
                              t('template.del') }}</t-button>
                        </div>
                      </div>
                    </div>
                  </div>

                </div>
              </template>


            </t-collapse-panel>
          </t-collapse>
        </t-loading>
      </div>
    </div>

    <!-- -->


    <!-- -->

  </div>
</template>

<script setup name="iconTemplate">
import { onMounted, ref } from 'vue'
import { getSelectionText, getSelectionNode } from '@/extensions/selection'
import {
  addBookTemplateImage,
  delBookTemplateImage,
  listBookTemplateImage,
} from '@/api/book/bookTemplateImage'
import { chooseFile, defaultOptPreChekck } from '@/utils/file'
const { editor, chapterId } = useStore()

const boxes = [
  { content: t('template.myselfIcon'), value: 1 },
  { content: t('template.templateIcon'), value: 2 },
]
const dataList = ref([])
const loading = ref(false)
const queryParams = ref({
  type: 1,
  chapterId: chapterId.value,
  fromTo: 1 // 1模板内 2模板库
})
const iconList = ref([])
const setActive = (id) => {
  // 更新 active 状态
  queryParams.value.type = id
  if (id == 1) {
    queryParams.value.fromTo = 1
  } else {
    queryParams.value.fromTo = null
  }
  getTemplateImageList()
}
// 获取模板图标库
const getTemplateImageList = async () => {
  dataList.value = []
  iconList.value = []
  loading.value = true
  const res = await listBookTemplateImage(queryParams.value)
  loading.value = false
  dataList.value = res.data.filter((item) => item.belongTo === 1) || []
  iconList.value = res.data.filter((item) => item.belongTo === 2) || []
  dataList.value = [...dataList.value].reverse()
  iconList.value = [...iconList.value].reverse()
}



const maxHeight = ref(0)
const screenHeight = ref(window.innerHeight);


const updateHeight = () => {
  screenHeight.value = window.innerHeight;
  console.log(screenHeight.value);
  if (screenHeight.value > 1000) {
    maxHeight.value = 400
  } else {
    maxHeight.value = 300
  }
};


onBeforeUnmount(() => {
  window.removeEventListener('resize', updateHeight);
});

onMounted(() => {
  queryParams.value = {
    type: 1,
    chapterId: chapterId.value,
    fromTo: 1
  }
  getTemplateImageList()
  updateHeight()
  window.addEventListener('resize', updateHeight);
})

const handleClick = (belongTo, item) => {


  // const test = getSelectionText(editor?.value) || ''

  if (belongTo === 1) {
    editor.value
      ?.chain()
      .focus()
      .setBackgroundImg({
        src: item.url,

      }).run()

  } else {
    editor.value
      ?.chain()
      .focus()
      .setImageIcon({
        src: item.url,

      })
      .run()

  }
  // editor.value?.chain().focus().deleteSelection().run()
}

// 鼠标进入
const handleMouseOver = (belongTo, index) => {
  if (belongTo === 1) {
    dataList.value[index].check = true
  } else {
    iconList.value[index].check = true
  }
}

// 鼠标离开
const handleMouseLeave = (belongTo, index) => {
  if (belongTo === 1) {
    dataList.value[index].check = false
  } else {
    iconList.value[index].check = false
  }
}

// 上传模板背景图
const addTemplateImage = (belongTo) => {
  chooseFile((file) => {
    addBookTemplateImage({
      type: 2,
      belongTo,
      url: file.fileUrl,
    }).then((res) => {
      useMessage('success', '上传成功')
      getTemplateImageList()
    })
  }, {
    optPreChekck: defaultOptPreChekck
  })
}

// 删除图片
const handleDelete = (item) => {
  const dialogInstance = useConfirm({
    body: '是否确认删除该模板图片?',
    onConfirm: async () => {
      await delBookTemplateImage(item.imageId)
      getTemplateImageList()
      useMessage('success', '删除成功')
      dialogInstance.destroy()
    },
  })
}


// dropdown 下拉
const dropDownClick = (e, content) => {
  queryParams.value.type = 1
  if (e.value == 1) {
    queryParams.value.fromTo = 1
  }
  if (e.value == 2) {
    queryParams.value.fromTo = 2
  }
  getTemplateImageList()
}
</script>
<style lang="less" scoped>
.tabGroup {
  display: flex;
  width: 290px;
  gap: 20px;
  padding: 20px 0px 0px 10px;
  position: fixed;
  height: 40px;
  z-index: 999;
  background-color: var(--td-bg-color-container);

  .btn {
    height: 36px;
    line-height: 36px;
    text-align: center;
    background-color: #f1f2f4;
    border-radius: 8px;
    color: #333;
    cursor: pointer;

    .text {
      width: 100%;
      margin: 0 10px;

    }

    .arrow {
      background: url('@/assets/images/tabSelectItemIcon.png') no-repeat;
      display: inline-flex;
      width: 12px;
      height: 7px;
      background-size: cover;
    }
  }



  .active {
    background-color: #0966b4;
    color: #fff;
  }



}


.tabSelect {
  width: 120px;

  border-radius: 8px;
  background-color: #fff;
  border: 1px solid #dcdcdc;

  .tabSelectItem {
    padding: 5px 8px;
    color: #333;
    font-size: 14px;
    overflow: hidden;
    border-radius: 8px 8px 0 0;
    cursor: pointer;
  }

  .active {
    color: #0966B4;
    background-color: rgba(236, 243, 249, 0.7);
  }
}

.nodata {
  display: flex;
  justify-content: center;
  align-items: center;
}

::v-deep(.umo-collapse) {
  border: none;
}

::v-deep(.umo-collapse-panel__wrapper .umo-collapse-panel__header) {
  border: none;
}




::v-deep(.umo-collapse-panel__wrapper .umo-collapse-panel__body) {
  border-bottom: none;
  background: transparent;
  overflow: inherit;
}






.component-main {
  position: fixed;
  top: 280px;
  padding: 0 5px;
  width: 290px;

  .nodata {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .icon-template-bg {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-gap: 10px;
    cursor: pointer;

    .umo-tabs {
      overflow-y: auto;
      padding-bottom: 20px;
    }

    .item {
      cursor: pointer;
      position: relative;
      margin: 10px 0;

      img {
        width: 100%;
        height: 46px;
        object-fit: contain;
      }

      .template-bg-btn {
        position: absolute;
        bottom: 0;
        left: 0;
        top: 0;
        right: 0;
        width: 100%;
        display: flex;
        justify-content: flex-end;
        background: rgba(0, 0, 0, 0.5);
        padding: 5px 0;
      }
    }


  }

  .template-bg {
    cursor: pointer;
    position: relative;
    margin: 10px 0;

    img {
      width: 100%;
      min-height: 40px;
      object-fit: contain;
    }

    .template-bg-btn {
      position: absolute;
      bottom: 0;
      left: 0;
      top: 0;
      right: 0;
      width: 100%;
      display: flex;
      justify-content: flex-end;
      background: rgba(0, 0, 0, 0.5);
      padding: 5px 0;
    }
  }
}
</style>


<style lang="less">
.btn {
  .umo-space-item {
    width: auto;
    gap: 0 !important;
  }
}
</style>
