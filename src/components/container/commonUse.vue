<!--
 * @Author: 矫建武 <EMAIL>
 * @Date: 2024-11-13 08:38:31
 * @LastEditTime: 2024-11-27 16:09:03
 * @FilePath: \dutp-editor\src\components\container\commonUse.vue
 * @Description: 
 * 
 * @file: https://github.com/OBKoro1/koro1FileHeader/wiki/%E5%AE%89%E8%A3%85%E5%92%8C%E5%BF%AB%E9%80%9F%E4%B8%8A%E6%89%8B
-->

<template>
  <div class="commonUse-bg">
    <t-collapse :borderless="true" :expand-on-row-click="true" :default-expand-all="true" expand-icon-placement="right">
      <t-collapse-panel :header="t('containerTool.titleLevel')">
        <menus-toolbar-base-heading source="tool" />
      </t-collapse-panel>

      <t-collapse-panel :header="t('containerTool.typeface')">
        <div class="font-item">
          <menus-toolbar-base-font-family source="tool" />
        </div>

        <div class="font-item">
          <menus-toolbar-base-font-size source="tool" />
        </div>
        <div class="font-bg">
          <div class="font-bg-item">
            <menus-toolbar-base-bold source="tool" />
            <menus-toolbar-base-italic source="tool" />
            <menus-toolbar-base-underline source="tool" />
            <menus-toolbar-base-strike source="tool" />
          </div>
          <div class="font-bg-item">
            <menus-toolbar-base-subscript source="tool" />
            <menus-toolbar-base-superscript source="tool" />
            <menus-toolbar-base-color source="tool" />
            <menus-toolbar-base-background-color source="tool" />
          </div>
          <div class="font-bg-item">
            <!-- <menus-toolbar-base-highlight source="tool" /> -->
            <menus-toolbar-base-textEffect source="tool" />
            <menus-toolbar-base-letterSpacing source="tool" />
            <menus-toolbar-base-fontBorder source="tool" />
            <div class="font-bg-seizeASeat" />
          </div>
        </div>
      </t-collapse-panel>

      <t-collapse-panel :header="t('containerTool.cuttingBoard')">
        <div class="font-bg">
          <div class="font-bg-item">
            <menus-toolbar-base-undo source="tool" />
            <menus-toolbar-base-redo source="tool" />
            <menus-toolbar-base-format-painter source="tool" />
            <menus-toolbar-base-clear-format source="tool" />
          </div>
        </div>
      </t-collapse-panel>

      <t-collapse-panel :header="t('containerTool.paragraph')">
        <div class="font-bg">
          <div class="font-bg-item">
            <menus-toolbar-base-ordered-list source="tool" />
            <menus-toolbar-base-bullet-list source="tool" />
            <menus-toolbar-base-indent source="tool" /><menus-toolbar-base-outdent source="tool" />
          </div>
          <div class="font-bg-item">
            <menus-toolbar-base-line-height source="tool" />
            <menus-toolbar-base-margin source="tool" />
            <menus-toolbar-base-align-left source="tool" />
            <menus-toolbar-base-align-center source="tool" />
          </div>
          <div class="font-bg-item">
            <menus-toolbar-base-align-right source="tool" />
            <menus-toolbar-base-align-justify source="tool" />
            <menus-toolbar-base-align-distributed source="tool" /><menus-toolbar-base-quote source="tool" />
          </div>
          <div class="font-bg-item">
            <menus-toolbar-insert-hr />
            <menus-toolbar-base-select-all source="tool" />
            <menus-toolbar-base-layout-column source="tool" />
            <menus-toolbar-insert-hard-break />
          </div>
          <div class="font-bg-item">

            <menus-toolbar-base-aligned />
            <div class="font-bg-seizeASeat"></div>
            <div class="font-bg-seizeASeat"></div>
          </div>
        </div>
      </t-collapse-panel>
    </t-collapse>
  </div>
</template>
<script setup></script>
<style lang="less">
.umo-collapse-panel__content {
  padding-left: 20px !important;
}
</style>
<style lang="less" scoped>
.commonUse-bg {
  height: 1370px;
  overflow: hidden;

  &::-webkit-scrollbar {
    width: 0;
    height: 0;
  }

  .umo-collapse-panel__content {
    &:host {
      padding: 0px !important;
    }
  }

  .font-item {
    margin-bottom: 5px;
  }

  .font-bg {
    width: 100%;
    box-sizing: border-box;
    padding-right: 10px;

    .font-bg-item {
      display: flex;
      justify-content: space-between;
    }

    .font-bg-seizeASeat {
      width: 55px;
      height: 55px;
      margin: 5px;
    }
  }
}
</style>
