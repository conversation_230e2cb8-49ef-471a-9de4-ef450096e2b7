<template>
  <iframe ref="iframeRef" class="umo-print-iframe" :srcdoc="iframeCode" />
</template>

<script setup lang="ts">
const { container, options, editor, page, printing, exportPDF } = useStore()
import { getChapterInfo } from '@/api/book/chapter.js'
import { useRoute } from 'vue-router'
const iframeRef = $ref<HTMLIFrameElement | null>(null)
let iframeCode = $ref('')
const route = useRoute()
const getStylesHtml = () => {
  return Array.from(document.querySelectorAll('link, style'))
    .map((item) => item.outerHTML)
    .join('')
}

const getPlyrSprite = () => {
  return document.querySelector('#sprite-plyr')?.innerHTML ?? ''
}

const getContentHtml = () => {
  return Array.from(
    document.querySelectorAll(`${container} .umo-page-node-view`),
  )
    .map((page) => {
      // 先从原始页面收集所有图片的样式信息
      const imageStylesMap = new Map()
      const originalImageContainers = page.querySelectorAll('.umo-node-image')
      originalImageContainers.forEach(container => {
        const img = container.querySelector('img')
        if (img && !container.closest('table, .tableWrapper')) {
          const dragerElement = container.querySelector('.es-drager')
          const nodeView = container.closest('[data-node-view-wrapper]')
          const titleDiv = container.querySelector('.title, .gallery-title')

          let styleInfo = {
            width: 'auto',
            height: 'auto',
            transform: 'none',
            nodeViewStyles: {},
            titleHtml: titleDiv ? titleDiv.outerHTML : null
          }

          if (dragerElement) {
            // 从 drager 元素获取用户调整后的尺寸和变换
            const dragerStyles = getComputedStyle(dragerElement)
            styleInfo.width = dragerStyles.width || dragerElement.style.width || 'auto'
            styleInfo.height = dragerStyles.height || dragerElement.style.height || 'auto'
            styleInfo.transform = dragerStyles.transform || dragerElement.style.transform || 'none'
          } else {
            // 从容器或图片本身获取尺寸
            const containerStyles = getComputedStyle(container)
            const imgStyles = getComputedStyle(img)
            styleInfo.width = containerStyles.width !== 'auto' ? containerStyles.width : imgStyles.width
            styleInfo.height = containerStyles.height !== 'auto' ? containerStyles.height : imgStyles.height
            styleInfo.transform = imgStyles.transform || img.style.transform || 'none'
          }

          // 获取容器布局样式
          if (nodeView) {
            const nodeViewStyles = getComputedStyle(nodeView)
            styleInfo.nodeViewStyles = {
              display: nodeViewStyles.display,
              justifyContent: nodeViewStyles.justifyContent,
              alignItems: nodeViewStyles.alignItems,
              margin: nodeViewStyles.margin,
              padding: nodeViewStyles.padding,
              float: nodeViewStyles.float
            }
          }

          console.log('收集到的样式信息:', {
            src: img.src.substring(img.src.lastIndexOf('/') + 1),
            ...styleInfo
          })

          imageStylesMap.set(img.src, styleInfo)
        }
      })

      // 克隆节点以避免修改原始DOM
      const clonedPage = page.cloneNode(true) as HTMLElement

      // 处理所有图片组件 - 应用收集到的样式
      const imageContainers = clonedPage.querySelectorAll('.umo-node-image')
      imageContainers.forEach(container => {
        if (!container.closest('table, .tableWrapper')) {
          const img = container.querySelector('img')
          if (img && imageStylesMap.has(img.src)) {
            const styleInfo = imageStylesMap.get(img.src)

            // 创建打印图片容器
            const printContainer = document.createElement('div')
            printContainer.className = 'print-image-container'

            // 复制图片并应用样式
            const newImg = img.cloneNode(true) as HTMLImageElement
            newImg.style.cssText = `
              width: ${styleInfo.width};
              height: ${styleInfo.height};
              transform: ${styleInfo.transform};
              object-fit: contain;
              display: block;
              max-width: none;
              max-height: none;
            `

            printContainer.appendChild(newImg)

            // 如果有图号标题，也要保留
            if (styleInfo.titleHtml) {
              const titleContainer = document.createElement('div')
              titleContainer.innerHTML = styleInfo.titleHtml
              const titleElement = titleContainer.firstElementChild as HTMLElement
              if (titleElement) {
                titleElement.style.cssText = `
                  margin: 5px 0;
                  width: 100%;
                  text-align: center;
                  font-size: 14px;
                  color: #333;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  flex-wrap: nowrap;
                  gap: 5px;
                  white-space: nowrap;
                `
                printContainer.appendChild(titleElement)
              }
            }

            // 应用容器的布局样式
            const nodeViewStyles = styleInfo.nodeViewStyles
            printContainer.style.cssText += `
              display: ${nodeViewStyles.display || 'block'};
              justify-content: ${nodeViewStyles.justifyContent || 'center'};
              align-items: ${nodeViewStyles.alignItems || 'center'};
              margin: ${nodeViewStyles.margin || '10px 0'};
              padding: ${nodeViewStyles.padding || '0'};
              float: ${nodeViewStyles.float || 'none'};
              position: relative;
            `

            console.log('应用最终样式:', {
              src: img.src.substring(img.src.lastIndexOf('/') + 1),
              width: styleInfo.width,
              height: styleInfo.height,
              transform: styleInfo.transform
            })

            container.parentNode?.replaceChild(printContainer, container)
          }
        }
      })

      // 处理完成

      // 处理图片画廊组件
      const galleryContainers = clonedPage.querySelectorAll('.images-content')
      galleryContainers.forEach(container => {
        const parentNode = container.parentNode as HTMLElement
        // parentNode.style.cssText = parentNode.style.cssText + 'border: 1px solid #ccc;'
        const galleryTitle = parentNode?.querySelector('.gallery-title')

        if (galleryTitle) {
          // 防止标题被遮挡
          galleryTitle.style = galleryTitle.style + ';padding-top: 10px'
          // console.log('galleryTitle', galleryTitle)
          // 确保画廊标题在打印时正确显示
          const newTitle = galleryTitle.cloneNode(true) as HTMLElement
          newTitle.style.cssText = `
            margin: 5px 0;
            width: 100%;
            text-align: center;
            font-size: 14px;
            color: #333;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-wrap: nowrap;
            gap: 5px;
            white-space: nowrap;
          `
          // 会出现两个标题，一个在容器前面，一个在容器后面
          // 将标题移到容器后面
          // if (parentNode && parentNode.parentNode) {
          //   parentNode.parentNode.insertBefore(newTitle, parentNode.nextSibling)
          // }
        }
      })

      // 移除可能导致问题的交互元素（但保留 imageIcon 组件）
      const interactiveElements = clonedPage.querySelectorAll('.es-drager-dot, .top-node-mu')
      interactiveElements.forEach(el => el.remove())

      return clonedPage.outerHTML
    })
    .join('')
}

const defaultLineHeight = $computed(
  () =>
    options.value.dicts?.lineHeights.find(
      (item: { default: any }) => item.default,
    )?.value,
)

const getIframeCode = () => {
  const { orientation, size, background } = page.value
  /* eslint-disable */
  return `
    <!DOCTYPE html>
    <html lang="zh-CN" theme-mode="${options.value.theme}">
    <head>
      <title>${options.value.document?.title}</title>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      ${getStylesHtml()}
      <style>
      body{
        overflow: auto;
        height: auto;
      }
      @page {
        size: ${orientation === 'portrait' ? size?.width : size?.height}cm ${orientation === 'portrait' ? size?.height : size?.width}cm;
        margin:0;
        background: ${background};
      }

      /* 打印专用样式 */
      .print-image-container {
        page-break-inside: avoid;
        margin: 10px 0;
        display: flex !important;
        flex-direction: column !important;
        align-items: center !important;
      }

      .print-image-container img {
        max-width: 100% !important;
        height: auto !important;
        display: block !important;
      }

      /* 行内图片样式 */
      [data-type="imageIcon"] img,
      [data-type="imageInLine"] img,
      img[data-inline="true"] {
        max-height: 1.2em !important;
        width: auto !important;
        vertical-align: middle !important;
        display: inline !important;
        margin: 0 2px !important;
      }

      /* 确保所有内容正常显示 */
      * {
        box-sizing: border-box !important;
      }

      .print-image-container .title,
      .print-image-container .gallery-title,
      .gallery-title {
        margin: 5px 0 !important;
        width: 100% !important;
        text-align: center !important;
        font-size: 14px !important;
        color: #333 !important;
        display: flex !important;
        justify-content: center !important;
        align-items: center !important;
        flex-wrap: nowrap !important;
        gap: 5px !important;
        white-space: nowrap !important;
      }

      /* 确保图号和标题的所有子元素都在同一行 */
      .print-image-container .title > *,
      .print-image-container .gallery-title > *,
      .gallery-title > *,
      .title > *,
      .title text,
      .title div,
      .gallery-title text,
      .gallery-title div {
        display: inline-flex !important;
        white-space: nowrap !important;
        flex-shrink: 0 !important;
        margin: 0 2px !important;
      }

      /* 隐藏交互元素 */
      .es-drager-dot,
      .top-node-mu,
      .es-drager-rotate {
        display: none !important;
      }

      /* 确保图片容器正确显示 */
      .umo-node-image {
        display: block !important;
        position: relative !important;
      }

      /* 移除可能影响打印的样式 */
      .es-drager {
        position: static !important;
        transform: none !important;
      }

      /* 表格文字换行样式 */
      table {
        table-layout: auto !important;
        width: 100% !important;
      }

      table td,
      table th {
        word-wrap: break-word !important;
        word-break: break-all !important;
        white-space: normal !important;
        overflow-wrap: break-word !important;
        hyphens: auto !important;
        min-width: 50px !important;
        max-width: none !important;
      }

      table p {
        word-wrap: break-word !important;
        word-break: break-all !important;
        white-space: normal !important;
        margin: 0 !important;
      }
      </style>
    </head>
    <body class="is-print">
      <div id="sprite-plyr" style="display: none;">
      ${getPlyrSprite()}
      </div>
      <div class="umo-editor-container" style="line-height: ${defaultLineHeight};" aria-expanded="false">
        <div class="tiptap umo-editor" translate="no">
          ${getContentHtml()}
        </div>
      </div>
      <script>
        document.addEventListener("DOMContentLoaded", (event) => {
          const observer = new MutationObserver(mutations => {
            mutations.forEach(mutation => {
              if (mutation.removedNodes) {
                Array.from(mutation.removedNodes).forEach(node => {
                  if (node?.classList?.contains('umo-page-watermark')) {
                    location.reload();
                  }
                });
              }
            });
          });
        });
      <\/script>
    </body>
    </html>`
  /* eslint-enable */
}
const forceRepaint = (element: HTMLElement) => {
  void element.offsetHeight; // 强制触发重绘
}
// 预加载打印内容
const preloadPrintContent = async () => {
  editor.value?.commands.blur()
  iframeCode = getIframeCode()

  // 等待 iframe 内容加载
  return new Promise((resolve) => {
    if (!iframeRef) {
      resolve(false)
      return
    }

    let attempts = 0
    const maxAttempts = 100 // 最多等待5秒 (100 * 50ms)

    const checkIframeReady = () => {
      attempts++

      try {
        if (iframeRef.contentDocument && iframeRef.contentDocument.readyState === 'complete') {
          forceRepaint(iframeRef.contentDocument.body as HTMLElement)
          resolve(true)
        } else if (attempts >= maxAttempts) {
          console.warn('iframe 加载超时')
          resolve(false)
        } else {
          setTimeout(checkIframeReady, 50)
        }
      } catch (error) {
        if (attempts >= maxAttempts) {
          console.error('iframe 加载失败:', error)
          resolve(false)
        } else {
          console.warn('iframe not ready yet:', error)
          setTimeout(checkIframeReady, 50)
        }
      }
    }

    checkIframeReady()
  })
}

const bookName = ref('')
const chapterName = ref('')

const getInfo = async () => {
  const { chapterId } = route.query
  const { data } = await getChapterInfo(chapterId)
  bookName.value = data.bookName
  chapterName.value = data.chapterName



}


onMounted(() => {

})

// 等待图片加载完成
const waitForImages = () => {
  return new Promise((resolve) => {
    if (!iframeRef?.contentDocument) {
      resolve(true)
      return
    }

    const images = iframeRef.contentDocument.querySelectorAll('img') || []
    const totalImages = images.length

    if (totalImages === 0) {
      resolve(true)
      return
    }

    let loadedCount = 0
    const checkAllLoaded = () => {
      loadedCount++
      if (loadedCount >= totalImages) {
        resolve(true)
      }
    }

    images.forEach((img: HTMLImageElement) => {
      if (img.complete) {
        checkAllLoaded()
      } else {
        img.onload = checkAllLoaded
        img.onerror = checkAllLoaded
        // 设置超时，避免某些图片加载失败导致无限等待
        setTimeout(checkAllLoaded, 3000)
      }
    })
  })
}

const printPage = async () => {
  // 显示加载状态
  const loadingMessage = useMessage('loading', '正在准备打印内容...')

  try {
    // 预加载内容
    const contentReady = await preloadPrintContent()

    // 关闭加载提示
    loadingMessage.close?.()

    if (!contentReady) {
      useMessage('error', '打印内容加载失败，请重试')
      printing.value = false
      exportPDF.value = false
      return
    }

    const dialog = useConfirm({
      theme: 'info',
      header: printing.value ? t('print.title') : t('export.pdf.title'),
      body: printing.value ? t('print.message') : t('export.pdf.message'),
      confirmBtn: printing.value ? t('print.confirm') : t('export.pdf.confirm'),
      async onConfirm() {
        dialog.destroy()

        try {
          // 等待图片加载完成
          await waitForImages()
          const originalTitle = document.title;
          await getInfo();
          document.title = chapterName.value;
          // 立即触发打印，避免延迟导致的弹窗阻止
          if (iframeRef?.contentWindow) {
            iframeRef.contentWindow.print()
            document.title = originalTitle;
          } else {
            throw new Error('打印窗口不可用')
          }
        } catch (error) {
          console.error('打印失败:', error)
          useMessage('error', '打印失败，请重试')
          printing.value = false
          exportPDF.value = false
        }
      },
      onClosed() {
        printing.value = false
        exportPDF.value = false
      },
    })
  } catch (error) {
    // 确保关闭加载提示
    loadingMessage?.close?.()
    console.error('打印准备失败:', error)
    useMessage('error', '打印准备失败，请重试')
    printing.value = false
    exportPDF.value = false
  }
}

watch(
  () => [printing.value, exportPDF.value],
  (value: [boolean, boolean]) => {
    if (!value[0] && !value[1]) {
      return
    }
    // 添加防抖，避免重复触发
    setTimeout(() => {
      printPage()
    }, 100)
  },
)
</script>

<style lang="less" scoped>
.umo-print-iframe {
  position: absolute;
  width: 0;
  height: 0;
  border: none;
  overflow: auto;
}
</style>
