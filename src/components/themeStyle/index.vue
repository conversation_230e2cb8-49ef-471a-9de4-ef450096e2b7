<template>

    <!-- 主题抽屉 -->
    <t-drawer v-model:visible="visible" attach="body" lazy :footer="false" class="themeStyle-drawer" size="450px"
        preventScrollThrough :close-btn="true" mode="overlay" @close.self="close">

        <template #header>
            <div class="themeStyle-header">
                <div class="themeStyle-header-title">{{ t('themeStyle.text') }}<span class="themeStyle-header-tip">{{
                    t('themeStyle.headerTip')
                        }}</span>
                </div>
                <div class="themeStyle-header-btn">
                    <t-button theme="default" variant="text" size="small" @click="copyConfig">
                        复制配置
                    </t-button>
                    <t-button theme="default" variant="text" size="small" @click="readConfig">读取配置</t-button>
                </div>

            </div>

        </template>

        <!-- 标题 开始-->
        <div class="titleCss">
            <div class="titleCss-header">
                {{ t('themeStyle.title') }}
            </div>
            <div class="titleCss-titleLevel">
                <div class="titleCss-titleLevel-item" v-for="(item) in titleList" :key="item.level"
                    @click="changeTitleLevel(item.level)" :class="{ active: activeLevel === item.level }">{{ item.name
                    }}
                </div>
            </div>
            <div class="titleCss-fontStyle">
                <div class="titleCss-fontStyle-level2">
                    <!-- 字体 -->
                    <div class="titleCss-fontStyle-level2-item6">
                        <div class="titleCss-fontStyle-level2-item6-label">{{ t('themeStyle.family') }}</div>
                        <div class="titleCss-fontStyle-level2-item6-select">
                            <t-select v-model="form.title[activeLevel - 1].fontFamily"
                                :placeholder="t('insert.fold.familyPlaceholder')" style="width: 196px"
                                @change="handleHeadingFontFamilyChange">
                                <t-option-group v-for="(group, index) in allFonts" :key="index" :label="group.label"
                                    :divider="false">
                                    <t-option v-for="item in group.children" :key="item.value ?? ''"
                                        class="umo-font-family-item" :value="item.value ?? ''" :label="l(item.label)">
                                        <span :style="{ fontFamily: item.value ?? 'SimSun' }"
                                            v-text="l(item.label)"></span>
                                    </t-option>
                                </t-option-group>
                            </t-select>
                        </div>
                    </div>
                    <!-- 字号 -->
                    <div class="titleCss-fontStyle-level2-item4">
                        <div class="titleCss-fontStyle-level2-item4-label">{{ t('themeStyle.size') }}</div>
                        <div>
                            <t-select v-model="form.title[activeLevel - 1].fontSize" style="width: 126px;"
                                :defaultInputValue="'18px'" @change="handleHeadingFontSizeChange">
                                <t-option v-for="item in fontSizes" :key="item" :value="item.value"
                                    :label="item.label ?? '18px'">

                                </t-option>
                            </t-select>
                        </div>
                    </div>
                </div>
            </div>

            <div class="titleCss-fontStyle">
                <div class="titleCss-fontStyle-level2">
                    <div class="titleCss-fontStyle-level2-item6">
                        <!-- 字体颜色 -->
                        <div class="titleCss-fontColor">
                            <div class="titleCss-fontColor-label">{{ t('themeStyle.color') }}</div>
                            <div class="titleCss-fontColor-select">
                                <!-- <t-color-picker v-model="form.title[activeLevel - 1].color"
                                    :show-primary-color-preview="false" format="HEX"
                                    @change="handleHeadingFontColorChange" :color-modes="['monochrome']"
                                    style="width: 108px;" /> -->

                                <t-popup placement="right-bottom" attach="body" :destroy-on-close="false">
                                    <div class="umo-color-picker-more"
                                        style="display: flex; align-items: center; cursor: pointer;width: 108px;">
                                        <div :style="`border:1px solid #ddd;width:20px;height:20px;border-radius:5px;background:${form.title[activeLevel - 1].color};margin-right:10px;`"
                                            :class="`${form.title[activeLevel - 1].color == 'transparent' ? 'transparent' : ''}`">
                                        </div>
                                        <div class="umo-color-picker-more-menu"
                                            :style="`border-bottom: 3px solid ${form.title[activeLevel - 1].color};}`">
                                            <span v-text="t('themeStyle.titleColor')"></span>
                                        </div>
                                    </div>
                                    <template #content>
                                        <div style="padding: 10px">
                                            <color-picker v-model:value="form.title[activeLevel - 1].color"
                                                @change="handleHeadingFontColorChange" />
                                        </div>
                                    </template>
                                </t-popup>


                            </div>
                        </div>
                        <!-- 段前 -->
                        <div class="titleCss-MarginTop">
                            <div class="titleCss-MarginTop-label" style="text-align: right;">
                                <t-tooltip :content="t('themeStyle.beforeParagraph')">
                                    {{ t('themeStyle.bp') }}
                                </t-tooltip>
                            </div>
                            <div class="titleCss-MarginTop-select">
                                <t-input-number theme="column" v-model="form.title[activeLevel - 1].marginTop"
                                    style="width:90px;" @change="handleHeadingMarginTopChange" />
                            </div>
                        </div>
                        <!-- 段后 -->
                        <div class="titleCss-MarginTop">
                            <div class="titleCss-MarginTop-label"> <t-tooltip
                                    :content="t('themeStyle.AfterParagraph')">{{
                                        t('themeStyle.ap') }}</t-tooltip></div>
                            <div class="titleCss-MarginTop-select">
                                <t-input-number theme="column" v-model="form.title[activeLevel - 1].marginBottom"
                                    style="width:84px;" @change="handleHeadingMarginBottomChange" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="titleCss-fontStyle">
                <div class="titleCss-fontStyle-level2">
                    <div class="titleCss-fontStyle-level2-item6">
                        <!-- 对齐方式 -->
                        <div class="titleCss-fontColor">
                            <div class="titleCss-fontColor-label" style="width: 60px;flex-shrink: 0;text-align: right;">
                                {{
                                    t('themeStyle.alignment.text')
                                }}</div>
                            <div class="titleCss-fontColor-select">
                                <t-select style="width: 156px;margin-right:10px;"
                                    v-model="form.title[activeLevel - 1].alignment"
                                    @change="handleHeadingAlignmentChange">
                                    <t-option v-for="item in alignmentList" :key="item.value" :value="item.value"
                                        :label="item.name" />
                                </t-select>
                            </div>
                        </div>
                        <!-- 首行缩进 -->
                        <div class="titleCss-MarginTop">
                            <div class="titleCss-MarginTop-label" style="width: 60px;flex-shrink: 0;text-align: right;">
                                {{ t('themeStyle.indent.text') }}
                            </div>
                            <div class="titleCss-MarginTop-select">
                                <t-select v-model="form.title[activeLevel - 1].indent"
                                    style="width: 100px;text-align: right;" @change="handleHeadingIndentChange">
                                    <t-option v-for="item in indentList" :key="item.value" :value="item.value"
                                        :label="item.name" />
                                </t-select>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
            <div class="titleCss-fontStyle">
                <div class="titleCss-fontStyle-level2">
                    <div class="titleCss-fontStyle-level2-item6">
                        <!-- 行高 -->
                        <div class="titleCss-fontColor">
                            <div class="titleCss-fontColor-label">
                                <t-tooltip :content="t('themeStyle.lineHeight.text')">
                                    {{ t('themeStyle.lineHeight.stext') }}
                                </t-tooltip>
                            </div>
                            <div class="titleCss-fontColor-select">
                                <t-select style="width: 120px;margin-right:10px;"
                                    v-model="form.title[activeLevel - 1].lineHeight"
                                    @change="handleHeadingLineHeightChange">
                                    <t-option v-for="item in lineHeights" :key="item.value" :value="item.value"
                                        :label="item.content" />
                                </t-select>
                            </div>
                        </div>
                        <!-- 斜体 -->
                        <div class="titleCss-MarginTop">
                            <div class="titleCss-MarginTop-label" style="text-align: right;">
                                {{ t('themeStyle.italic.text') }}
                            </div>
                            <div class="titleCss-MarginTop-select">
                                <t-select v-model="form.title[activeLevel - 1].italic" style="width:67px;"
                                    @change="handleHeadingItalicChange">
                                    <t-option v-for="item in judgeList" :key="item.value" :value="item.value"
                                        :label="item.name" />
                                </t-select>
                            </div>
                        </div>
                        <!-- 下划线 -->
                        <div class="titleCss-MarginTop">
                            <div class="titleCss-MarginTop-label" style="width: 45px;text-align: right;">
                                <t-tooltip :content="t('themeStyle.underline.text')">
                                    {{ t('themeStyle.underline.stext') }}
                                </t-tooltip>
                            </div>
                            <div class="titleCss-MarginTop-select" style="">
                                <t-select v-model="form.title[activeLevel - 1].underline" style="width: 68px;"
                                    @change="handleHeadingUnderlineChange">
                                    <t-option v-for="item in judgeList" :key="item.value" :value="item.value"
                                        :label="item.name" />
                                </t-select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
        <!-- 标题 结束-->


        <!-- 章头 开始 -->
        <div class="titleCss">
            <div class="titleCss-header">
                {{ t('themeStyle.chapterHead') }}
            </div>

            <div class="titleCss-fontStyle">
                <div class="titleCss-fontStyle-level2">
                    <!-- 字体 -->
                    <div class="titleCss-fontStyle-level2-item6">
                        <div class="titleCss-fontStyle-level2-item6-label">{{ t('themeStyle.family') }}</div>
                        <div class="titleCss-fontStyle-level2-item6-select">
                            <t-select v-model="form.chapterHeader.fontFamily"
                                :placeholder="t('insert.fold.familyPlaceholder')"
                                style="width: 196px;margin-right:10px;" @change="handleChapterHeaderFontFamilyChange">
                                <t-option-group v-for="(group, index) in allFonts" :key="index" :label="group.label"
                                    :divider="false">
                                    <t-option v-for="item in group.children" :key="item.value ?? ''"
                                        class="umo-font-family-item" :value="item.value ?? ''" :label="l(item.label)">
                                        <span :style="{ fontFamily: item.value ?? 'SimSun' }"
                                            v-text="l(item.label)"></span>
                                    </t-option>
                                </t-option-group>
                            </t-select>
                        </div>
                    </div>
                    <!-- 字号 -->
                    <div class="titleCss-fontStyle-level2-item4">
                        <div class="titleCss-fontStyle-level2-item4-label">{{ t('themeStyle.size') }}</div>
                        <div>
                            <t-select v-model="form.chapterHeader.fontSize" style="width: 126px;"
                                :defaultInputValue="'18px'" @change="handleChapterHeaderFontSizeChange">
                                <t-option v-for="item in fontSizes" :key="item" :value="item.value"
                                    :label="item.label ?? '18px'">

                                </t-option>
                            </t-select>
                        </div>
                    </div>
                </div>
            </div>

            <div class="titleCss-fontStyle">
                <div class="titleCss-fontStyle-level2">
                    <div class="titleCss-fontStyle-level2-item6">
                        <!-- 字体颜色 -->
                        <div class="titleCss-fontColor">
                            <div class="titleCss-fontColor-label">{{ t('themeStyle.color') }}</div>
                            <div class="titleCss-fontColor-select">
                                <t-popup placement="right-bottom" attach="body" :destroy-on-close="false">
                                    <div class="umo-color-picker-more"
                                        style="display: flex; align-items: center; cursor: pointer;width: 108px; ;">
                                        <div :style="`border:1px solid #ddd;width:20px;height:20px;flex-shrink:0;border-radius:5px;background:${form.chapterHeader.color};margin-right:10px;`"
                                            :class="`${form.chapterHeader.color == 'transparent' ? 'transparent' : ''}`">
                                        </div>
                                        <div class="umo-color-picker-more-menu"
                                            :style="`border-bottom: 3px solid ${form.chapterHeader.color};}`">
                                            <span v-text="t('themeStyle.chapterHeaderTitleColor')"></span>
                                        </div>
                                    </div>
                                    <template #content>
                                        <div style="padding: 10px">
                                            <color-picker v-model:value="form.chapterHeader.color"
                                                @change="handleChapterHeaderFontColorChange" />
                                        </div>
                                    </template>
                                </t-popup>
                                <!-- <t-color-picker v-model="form.chapterHeader.color" :show-primary-color-preview="false"
                                    format="HEX" :color-modes="['monochrome']" style="width: 108px;"
                                    @change="handleChapterHeaderFontColorChange" /> -->
                            </div>
                        </div>
                        <!-- 字体颜色 -->
                        <!-- 段前 -->
                        <div class="titleCss-MarginTop">
                            <div class="titleCss-MarginTop-label" style="text-align: right; ">
                                <t-tooltip :content="t('themeStyle.beforeParagraph')">
                                    {{ t('themeStyle.bp') }}
                                </t-tooltip>
                            </div>
                            <div class="titleCss-MarginTop-select">
                                <t-input-number theme="column" v-model="form.chapterHeader.marginTop"
                                    style="width:90px;" @change="handleChapterHeaderMarginTopChange" />
                            </div>
                        </div>
                        <div class="titleCss-MarginTop">
                            <div class="titleCss-MarginTop-label"> <t-tooltip
                                    :content="t('themeStyle.AfterParagraph')">{{
                                        t('themeStyle.ap') }}</t-tooltip></div>
                            <div class="titleCss-MarginTop-select">
                                <t-input-number theme="column" v-model="form.chapterHeader.marginBottom"
                                    style="width:84px;" @change="handleChapterHeaderMarginBottomChange" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="titleCss-fontStyle">
                <div class="titleCss-fontStyle-level2">
                    <div class="titleCss-fontStyle-level2-item6">
                        <div class="titleCss-fontColor">
                            <div class="titleCss-fontColor-label" style="width: 60px;flex-shrink: 0;text-align: right;">
                                {{
                                    t('themeStyle.alignment.text')
                                }}</div>
                            <div class="titleCss-fontColor-select">
                                <t-select style="width: 156px;margin-right:10px;" v-model="form.chapterHeader.alignment"
                                    @change="handleChapterHeaderAlignmentChange">
                                    <t-option v-for="item in alignmentList" :key="item.value" :value="item.value"
                                        :label="item.name" />
                                </t-select>
                            </div>
                        </div>
                        <div class="titleCss-MarginTop">
                            <div class="titleCss-MarginTop-label" style="width: 60px;flex-shrink: 0;text-align: right;">
                                {{ t('themeStyle.indent.text') }}
                            </div>
                            <div class="titleCss-MarginTop-select">
                                <t-select v-model="form.chapterHeader.indent" style="width: 100px;text-align: right;"
                                    @change="handleChapterHeaderIndentChange">
                                    <t-option v-for="item in indentList" :key="item.value" :value="item.value"
                                        :label="item.name" />
                                </t-select>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
            <div class="titleCss-fontStyle">
                <div class="titleCss-fontStyle-level2">
                    <div class="titleCss-fontStyle-level2-item6">
                        <!-- 行高 -->
                        <div class="titleCss-fontColor">
                            <div class="titleCss-fontColor-label">
                                <t-tooltip :content="t('themeStyle.lineHeight.text')">
                                    {{ t('themeStyle.lineHeight.stext') }}
                                </t-tooltip>
                            </div>
                            <div class="titleCss-fontColor-select">
                                <t-select style="width: 120px;margin-right:10px;"
                                    v-model="form.chapterHeader.lineHeight"
                                    @change="handleChapterHeaderLineHeightChange">
                                    <t-option v-for="item in lineHeights" :key="item.value" :value="item.value"
                                        :label="item.content" />
                                </t-select>
                            </div>
                        </div>
                        <!-- 斜体 -->
                        <div class="titleCss-MarginTop">
                            <div class="titleCss-MarginTop-label" style="text-align: right;">
                                {{ t('themeStyle.italic.text') }}
                            </div>
                            <div class="titleCss-MarginTop-select">
                                <t-select v-model="form.chapterHeader.italic" style="width: 67px;"
                                    @change="handleChapterHeaderItalicChange">
                                    <t-option v-for="item in judgeList" :key="item.value" :value="item.value"
                                        :label="item.name" />
                                </t-select>
                            </div>
                        </div>
                        <!-- 下划线 -->
                        <div class="titleCss-MarginTop">
                            <div class="titleCss-MarginTop-label" style="width: 45px;text-align: right;">
                                <t-tooltip :content="t('themeStyle.underline.text')">
                                    {{ t('themeStyle.underline.stext') }}
                                </t-tooltip>
                            </div>
                            <div class="titleCss-MarginTop-select" style="">
                                <t-select v-model="form.chapterHeader.underline" style="width: 68px;"
                                    @change="handleChapterHeaderUnderlineChange">
                                    <t-option v-for="item in judgeList" :key="item.value" :value="item.value"
                                        :label="item.name" />
                                </t-select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="titleCss-fontStyle">
                <div class="titleCss-fontStyle-level2">
                    <div class="titleCss-fontStyle-level2-item6">
                        <div class="titleCss-fontColor">
                            <div class="titleCss-fontColor-label" style="width: 60px;flex-shrink: 0;text-align: right;">
                                {{ t('themeStyle.fontWeight') }}
                            </div>
                            <div class="titleCss-fontColor-select">
                                <t-select style="width: 80px;margin-right:10px;" v-model="form.chapterHeader.fontWeight"
                                    @change="handleChapterHeaderfontWeightChange">
                                    <t-option v-for="item in indentList" :key="item.value" :value="item.value"
                                        :label="item.name" />
                                </t-select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
        <!-- 章头 结束 -->


        <!-- 节头 开始 -->
        <div class="titleCss">
            <div class="titleCss-header">
                {{ t('themeStyle.sectionHeader') }}
            </div>

            <div class="titleCss-fontStyle">
                <div class="titleCss-fontStyle-level2">
                    <!-- 字体 -->
                    <div class="titleCss-fontStyle-level2-item6">
                        <div class="titleCss-fontStyle-level2-item6-label">{{ t('themeStyle.family') }}</div>
                        <div class="titleCss-fontStyle-level2-item6-select">
                            <t-select v-model="form.jointHeader.fontFamily"
                                :placeholder="t('insert.fold.familyPlaceholder')"
                                style="width: 196px;margin-right:10px;" @change="handleJointHeaderFontFamilyChange">
                                <t-option-group v-for="(group, index) in allFonts" :key="index" :label="group.label"
                                    :divider="false">
                                    <t-option v-for="item in group.children" :key="item.value ?? ''"
                                        class="umo-font-family-item" :value="item.value ?? ''" :label="l(item.label)">
                                        <span :style="{ fontFamily: item.value ?? 'SimSun' }"
                                            v-text="l(item.label)"></span>
                                    </t-option>
                                </t-option-group>
                            </t-select>
                        </div>
                    </div>
                    <!-- 字号 -->
                    <div class="titleCss-fontStyle-level2-item4">
                        <div class="titleCss-fontStyle-level2-item4-label">{{ t('themeStyle.size') }}</div>
                        <div>
                            <t-select v-model="form.jointHeader.fontSize" style="width: 126px;"
                                :defaultInputValue="'18px'" @change="handleJointHeaderFontSizeChange">
                                <t-option v-for="item in fontSizes" :key="item" :value="item.value"
                                    :label="item.label ?? '18px'">

                                </t-option>
                            </t-select>
                        </div>
                    </div>
                </div>
            </div>

            <div class="titleCss-fontStyle">
                <div class="titleCss-fontStyle-level2">
                    <div class="titleCss-fontStyle-level2-item6">
                        <!-- 字体颜色 -->
                        <div class="titleCss-fontColor">
                            <div class="titleCss-fontColor-label">{{ t('themeStyle.color') }}</div>
                            <div class="titleCss-fontColor-select">
                                <t-popup placement="right-bottom" attach="body" :destroy-on-close="false">
                                    <div class="umo-color-picker-more"
                                        style="display: flex; align-items: center; cursor: pointer;width: 108px; ;">
                                        <div :style="`border:1px solid #ddd;width:20px;height:20px;flex-shrink:0;border-radius:5px;background:${form.jointHeader.color};margin-right:10px;`"
                                            :class="`${form.jointHeader.color == 'transparent' ? 'transparent' : ''}`">
                                        </div>
                                        <div class="umo-color-picker-more-menu"
                                            :style="`border-bottom: 3px solid ${form.jointHeader.color};}`">
                                            <span v-text="t('themeStyle.jointHeaderFontColor')"></span>
                                        </div>
                                    </div>
                                    <template #content>
                                        <div style="padding: 10px">
                                            <color-picker v-model:value="form.jointHeader.color"
                                                @change="handleJointHeaderFontColorChange" />
                                        </div>
                                    </template>
                                </t-popup>
                                <!-- <t-color-picker v-model="form.jointHeader.color" :show-primary-color-preview="false"
                                    format="HEX" :color-modes="['monochrome']" style="width: 108px;"
                                    @change="handleJointHeaderFontColorChange" /> -->
                            </div>
                        </div>
                        <!-- 段前 -->
                        <div class="titleCss-MarginTop">
                            <div class="titleCss-MarginTop-label" style="text-align: right;">
                                <t-tooltip :content="t('themeStyle.beforeParagraph')">
                                    {{ t('themeStyle.bp') }}
                                </t-tooltip>
                            </div>
                            <div class="titleCss-MarginTop-select">
                                <t-input-number theme="column" v-model="form.jointHeader.marginTop" style="width:86px;"
                                    @change="handleJointHeaderMarginTopChange" />
                            </div>
                        </div>
                        <!-- 段后 -->
                        <div class="titleCss-MarginTop">
                            <div class="titleCss-MarginTop-label"> <t-tooltip
                                    :content="t('themeStyle.AfterParagraph')">{{
                                        t('themeStyle.ap') }}</t-tooltip></div>
                            <div class="titleCss-MarginTop-select">
                                <t-input-number theme="column" v-model="form.jointHeader.marginBottom"
                                    style="width:90px;" @change="handleJointHeaderMarginBottomChange" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="titleCss-fontStyle">
                <div class="titleCss-fontStyle-level2">
                    <div class="titleCss-fontStyle-level2-item6">
                        <!-- 对齐方式 -->
                        <div class="titleCss-fontColor">
                            <div class="titleCss-fontColor-label" style="width: 60px;flex-shrink: 0;text-align: right;">
                                {{
                                    t('themeStyle.alignment.text')
                                }}</div>
                            <div class="titleCss-fontColor-select">
                                <t-select style="width: 156px;margin-right:10px;" v-model="form.jointHeader.alignment"
                                    @change="handleJointHeaderAlignmentChange">
                                    <t-option v-for="item in alignmentList" :key="item.value" :value="item.value"
                                        :label="item.name" />
                                </t-select>
                            </div>
                        </div>
                        <!-- 缩进 -->
                        <div class="titleCss-MarginTop">
                            <div class="titleCss-MarginTop-label" style="width: 60px;flex-shrink: 0;text-align: right;">
                                {{ t('themeStyle.indent.text') }}
                            </div>
                            <div class="titleCss-MarginTop-select">
                                <t-select v-model="form.jointHeader.indent" style="width: 102px;text-align: right;"
                                    @change="handleJointHeaderIndentChange">
                                    <t-option v-for="item in indentList" :key="item.value" :value="item.value"
                                        :label="item.name" />
                                </t-select>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
            <div class="titleCss-fontStyle">
                <div class="titleCss-fontStyle-level2">
                    <div class="titleCss-fontStyle-level2-item6">
                        <!-- 行高 -->
                        <div class="titleCss-fontColor">
                            <div class="titleCss-fontColor-label">
                                <t-tooltip :content="t('themeStyle.lineHeight.text')">
                                    {{ t('themeStyle.lineHeight.stext') }}
                                </t-tooltip>
                            </div>
                            <div class="titleCss-fontColor-select">
                                <t-select style="width: 120px;margin-right:10px;" v-model="form.jointHeader.lineHeight"
                                    @change="handleJointHeaderLineHeightChange">
                                    <t-option v-for="item in lineHeights" :key="item.value" :value="item.value"
                                        :label="item.content" />
                                </t-select>
                            </div>
                        </div>
                        <!-- 斜体 -->
                        <div class="titleCss-MarginTop">
                            <div class="titleCss-MarginTop-label" style="text-align: right;">
                                {{ t('themeStyle.italic.text') }}
                            </div>
                            <div class="titleCss-MarginTop-select">
                                <t-select v-model="form.jointHeader.italic" style="width: 67px;"
                                    @change="handleJointHeaderItalicChange">
                                    <t-option v-for="item in judgeList" :key="item.value" :value="item.value"
                                        :label="item.name" />
                                </t-select>
                            </div>
                        </div>
                        <!-- 下划线 -->
                        <div class="titleCss-MarginTop">
                            <div class="titleCss-MarginTop-label" style="width: 45px;text-align: right;">
                                <t-tooltip :content="t('themeStyle.underline.text')">
                                    {{ t('themeStyle.underline.stext') }}
                                </t-tooltip>
                            </div>
                            <div class="titleCss-MarginTop-select" style="">
                                <t-select v-model="form.jointHeader.underline" style="width: 68px;"
                                    @change="handleJointHeaderUnderlineChange">
                                    <t-option v-for="item in judgeList" :key="item.value" :value="item.value"
                                        :label="item.name" />
                                </t-select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="titleCss-fontStyle">
                <div class="titleCss-fontStyle-level2">
                    <div class="titleCss-fontStyle-level2-item6">
                        <div class="titleCss-fontColor">
                            <div class="titleCss-fontColor-label" style="width: 60px;flex-shrink: 0;text-align: right;">
                                {{ t('themeStyle.fontWeight') }}
                            </div>
                            <div class="titleCss-fontColor-select">
                                <t-select style="width: 80px;margin-right:10px;" v-model="form.jointHeader.fontWeight"
                                    @change="handleJointHeaderfontWeightChange">
                                    <t-option v-for="item in indentList" :key="item.value" :value="item.value"
                                        :label="item.name" />
                                </t-select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
        <!-- 节头 结束 -->

        <!-- 正文 开始-->
        <div class="titleCss">
            <div class="titleCss-header">
                {{ t('themeStyle.mainBody') }}
            </div>
            <div class="titleCss-fontStyle">
                <div class="titleCss-fontStyle-level2">
                    <!-- 字体 -->
                    <div class="titleCss-fontStyle-level2-item6">
                        <div class="titleCss-fontStyle-level2-item6-label">{{ t('themeStyle.family') }}</div>
                        <div class="titleCss-fontStyle-level2-item6-select">
                            <t-select v-model="form.paragraph.fontFamily"
                                :placeholder="t('insert.fold.familyPlaceholder')"
                                style="width: 196px;margin-right:10px;" @change="handleParagraphFontFamilyChange">
                                <t-option-group v-for="(group, index) in allFonts" :key="index" :label="group.label"
                                    :divider="false">
                                    <t-option v-for="item in group.children" :key="item.value ?? ''"
                                        class="umo-font-family-item" :value="item.value ?? ''" :label="l(item.label)">
                                        <span :style="{ fontFamily: item.value ?? 'FZHTJW' }"
                                            v-text="l(item.label)"></span>
                                    </t-option>
                                </t-option-group>
                            </t-select>
                        </div>
                    </div>
                    <!-- 字号 -->
                    <div class="titleCss-fontStyle-level2-item4">
                        <div class="titleCss-fontStyle-level2-item4-label">{{ t('themeStyle.size') }}</div>
                        <div>
                            <t-select v-model="form.paragraph.fontSize" style="width: 126px;"
                                :defaultInputValue="'18px'" @change="handleParagraphFontSizeChange">
                                <t-option v-for="item in fontSizes" :key="item" :value="item.value"
                                    :label="item.label ?? '18px'">

                                </t-option>
                            </t-select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="titleCss-fontStyle">
                <div class="titleCss-fontStyle-level2">
                    <div class="titleCss-fontStyle-level2-item6">
                        <!-- 字体颜色 -->
                        <div class="titleCss-fontColor">
                            <div class="titleCss-fontColor-label">{{ t('themeStyle.color') }}</div>
                            <div class="titleCss-fontColor-select">
                                <!-- <t-color-picker v-model="form.paragraph.color" :show-primary-color-preview="false"
                                    format="HEX" :color-modes="['monochrome']" style="width: 108px;"
                                    @change="handleParagraphFontColorChange" /> -->

                                <t-popup placement="right-bottom" attach="body" :destroy-on-close="false">
                                    <div class="umo-color-picker-more"
                                        style="display: flex; align-items: center; cursor: pointer;width: 108px; ;">
                                        <div :style="`border:1px solid #ddd;width:20px;height:20px;flex-shrink:0;border-radius:5px;background:${form.paragraph.color};margin-right:10px;`"
                                            :class="`${form.paragraph.color == 'transparent' ? 'transparent' : ''}`">
                                        </div>
                                        <div class="umo-color-picker-more-menu"
                                            :style="`border-bottom: 3px solid ${form.paragraph.color};}`">
                                            <span v-text="t('themeStyle.paragraphFontColor')"></span>
                                        </div>
                                    </div>
                                    <template #content>
                                        <div style="padding: 10px">
                                            <color-picker v-model:value="form.paragraph.color"
                                                @change="handleParagraphFontColorChange" />
                                        </div>
                                    </template>
                                </t-popup>
                            </div>
                        </div>
                        <!-- 段前 -->
                        <div class="titleCss-MarginTop">
                            <div class="titleCss-MarginTop-label" style="text-align: right;">
                                <t-tooltip :content="t('themeStyle.beforeParagraph')">
                                    {{ t('themeStyle.bp') }}
                                </t-tooltip>
                            </div>
                            <div class="titleCss-MarginTop-select">
                                <t-input-number theme="column" v-model="form.paragraph.marginTop" style="width:88px;"
                                    @change="handleParagraphMarginTopChange" />
                            </div>
                        </div>
                        <!-- 段后 -->
                        <div class="titleCss-MarginTop">
                            <div class="titleCss-MarginTop-label"> <t-tooltip
                                    :content="t('themeStyle.AfterParagraph')">{{
                                        t('themeStyle.ap') }}</t-tooltip></div>
                            <div class="titleCss-MarginTop-select">
                                <t-input-number theme="column" v-model="form.paragraph.marginBottom" style="width:88px;"
                                    @change="handleParagraphMarginBottomChange" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="titleCss-fontStyle">
                <div class="titleCss-fontStyle-level2">
                    <div class="titleCss-fontStyle-level2-item6">
                        <!-- 对齐方式 -->
                        <div class="titleCss-fontColor">
                            <div class="titleCss-fontColor-label" style="width: 60px;flex-shrink: 0;text-align: right;">
                                {{
                                    t('themeStyle.alignment.text')
                                }}</div>
                            <div class="titleCss-fontColor-select">
                                <t-select style="width: 156px;margin-right:10px;" v-model="form.paragraph.alignment"
                                    @change="handleParagraphAlignmentChange">
                                    <t-option v-for="item in alignmentList" :key="item.value" :value="item.value"
                                        :label="item.name" />
                                </t-select>
                            </div>
                        </div>
                        <!-- 首行缩进 -->
                        <div class="titleCss-MarginTop">
                            <div class="titleCss-MarginTop-label" style="width: 60px;flex-shrink: 0;text-align: right;">
                                {{ t('themeStyle.indent.text') }}
                            </div>
                            <div class="titleCss-MarginTop-select">
                                <t-select v-model="form.paragraph.indent" style="width: 100px;text-align: right;"
                                    @change="handleParagraphIndentChange">
                                    <t-option v-for="item in indentList" :key="item.value" :value="item.value"
                                        :label="item.name" />
                                </t-select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="titleCss-fontStyle">
                <div class="titleCss-fontStyle-level2">
                    <div class="titleCss-fontStyle-level2-item6">
                        <!-- 行高 -->
                        <div class="titleCss-fontColor">
                            <div class="titleCss-fontColor-label">
                                <t-tooltip :content="t('themeStyle.lineHeight.text')">
                                    {{ t('themeStyle.lineHeight.stext') }}
                                </t-tooltip>
                            </div>
                            <div class="titleCss-fontColor-select">
                                <t-select style="width: 120px;margin-right:10px;" v-model="form.paragraph.lineHeight"
                                    @change="handleParagraphLineHeightChange">
                                    <t-option v-for="item in lineHeights" :key="item.value" :value="item.value"
                                        :label="item.content" />
                                </t-select>
                            </div>
                        </div>
                        <!-- 字符距 -->
                        <div class="titleCss-MarginTop">
                            <div class="titleCss-MarginTop-label" style="width: 45px;text-align: right;">
                                {{ t('themeStyle.spacing') }}
                            </div>
                            <div class="titleCss-MarginTop-select">
                                <t-select v-model="form.paragraph.spacing" style="width: 100px;"
                                    @change="handleParagraphSpacingChange">
                                    <t-option v-for="item in spacingList" :key="item.value" :value="item.value"
                                        :label="item.name" />
                                </t-select>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
        <!-- 正文 结束-->
    </t-drawer>

</template>

<script setup lang="ts">
import {
    checkParagraph,
    checkChapterHeader,
    checkJointHeader,
    checkParentIsParagraph,
    checkParentIsChapterHeader,
    checkLastParentIsChapterHeader,
    checkLastParentIsJointHeader,
    checkParentIsJointHeader,
} from '@/extensions/batchUpdateMarkAndAttrs'
import { getMarkType } from '@tiptap/core'
import { useDebounceFn, useEventListener } from '@vueuse/core'
import { MessagePlugin } from 'tdesign-vue-next';

const { options, editor } = useStore()

const visible = ref(false)
const open = () => {
    visible.value = true
}

const close = () => {
    visible.value = false
}

const realActiveLevel = ref(1)
const activeLevel = ref(1)
const form = ref({
    title: [{
        fontFamily: 'FZHTJW', fontSize: '18px', color: '#000', marginTop: '16', marginBottom: '0',
        alignment: 'left', indent: '2', lineHeight: '1.5', italic: '2', underline: '2'
    }, {
        fontFamily: 'FZHTJW', fontSize: '18px', color: '#000', marginTop: '16', marginBottom: '0',
        alignment: 'left', indent: '2', lineHeight: '1.5', italic: '2', underline: '2'
    }, {
        fontFamily: 'FZHTJW', fontSize: '18px', color: '#000', marginTop: '16', marginBottom: '0',
        alignment: 'left', indent: '2', lineHeight: '1.5', italic: '2', underline: '2'
    }, {
        fontFamily: 'FZHTJW', fontSize: '18px', color: '#000', marginTop: '16', marginBottom: '0',
        alignment: 'left', indent: '2', lineHeight: '1.5', italic: '2', underline: '2'
    }, {
        fontFamily: 'FZHTJW', fontSize: '18px', color: '#000', marginTop: '16', marginBottom: '0',
        alignment: 'left', indent: '2', lineHeight: '1.5', italic: '2', underline: '2'
    }, {
        fontFamily: 'FZHTJW', fontSize: '18px', color: '#000', marginTop: '16', marginBottom: '0',
        alignment: 'left', indent: '2', lineHeight: '1.5', italic: '2', underline: '2'
    }],
    paragraph: {
        fontFamily: 'FZHTJW', fontSize: '18px', color: '#000', marginTop: '16', marginBottom: '0',
        alignment: 'left', indent: '2', lineHeight: '1.5', spacing: '0'
    },
    chapterHeader: {
        fontFamily: 'FZHTJW', fontSize: '22px', color: '#000', marginTop: '16', marginBottom: '0',
        alignment: 'left', indent: '2', lineHeight: '1.5', italic: '2', underline: '2', fontWeight: '2'
    },
    jointHeader: {
        fontFamily: 'FZHTJW', fontSize: '20px', color: '#000', marginTop: '16', marginBottom: '0',
        alignment: 'left', indent: '2', lineHeight: '1.5', italic: '2', underline: '2', fontWeight: '2'
    },
})


const usedFonts = $ref<string[]>([])
const $recent = useState('recent')
const allFonts = computed(() => {
    const all = [
        {
            label: t('base.fontFamily.all'),
            children: options.value.dicts?.fonts ?? [],
        },
    ]
    // 通过字体值获取字体列表
    const getFontsByValues = (values: string[]) => {
        return values.map(
            (item) =>
                options.value.dicts?.fonts.find(
                    ({ value }: { value: string }) => value === item,
                ) ?? {
                    label: item,
                    item,
                },
        )
    }
    if ($recent.value.fonts.length > 0) {
        all.unshift({
            label: t('base.fontFamily.recent'),
            children: getFontsByValues($recent.value.fonts) as any,
        })
    }
    if (usedFonts.length > 0) {
        all.unshift({
            label: t('base.fontFamily.used'),
            children: getFontsByValues(usedFonts) as any,
        })
    }
    return all
})


// pt=px 乘以 3/4
const fontSizes = [
    { label: t('base.fontSize.42pt'), value: '42pt', order: 20 }, //56
    { label: t('base.fontSize.36pt'), value: '36pt', order: 19 }, //48
    { label: t('base.fontSize.26pt'), value: '26pt', order: 16 }, //35
    { label: t('base.fontSize.24pt'), value: '24pt', order: 15 }, //32
    { label: t('base.fontSize.22pt'), value: '22pt', order: 14 }, //29
    { label: t('base.fontSize.18pt'), value: '18pt', order: 11 }, //24
    { label: t('base.fontSize.16pt'), value: '16pt', order: 10 }, //22
    { label: t('base.fontSize.15pt'), value: '15pt', order: 9 }, //21
    { label: t('base.fontSize.14pt'), value: '14pt', order: 7 }, //19
    { label: t('base.fontSize.12pt'), value: '12pt', order: 4 }, //16
    { label: t('base.fontSize.10_5pt'), value: '10.5pt', order: 1 }, //14
    { label: t('base.fontSize.9pt'), value: '9pt', order: 3 }, //12
    { label: t('base.fontSize.7_5pt'), value: '7.5pt', order: 1 }, //10
    { label: '10', value: '10px', order: 1 },
    { label: '11', value: '11px', order: 2 },
    { label: '12', value: '12px', order: 3 },
    { label: '14', value: '14px', order: 4 },
    { label: '16', value: '16px', order: 5 },
    { label: t('base.fontSize.13_5px'), value: '18px', order: 6 },
    { label: '20', value: '20px', order: 8 },
    { label: '22', value: '22px', order: 10 },
    { label: '24', value: '24px', order: 11 },
    { label: '26', value: '26px', order: 12 },
    { label: '28', value: '28px', order: 13 },
    { label: '32', value: '32px', order: 15 },
    { label: '36', value: '36px', order: 17 },
    { label: '42', value: '42px', order: 18 },
    { label: '48', value: '48px', order: 19 },
    { label: '72', value: '72px', order: 21 },
    { label: '96', value: '96px', order: 22 },
]



const alignmentList = [
    { name: t('themeStyle.alignment.left'), value: 'left' },
    { name: t('themeStyle.alignment.center'), value: 'center' },
    { name: t('themeStyle.alignment.right'), value: 'right' },
    { name: t('themeStyle.alignment.justify'), value: 'justify' },
]

const indentList = [
    { name: t('themeStyle.indent.yes'), value: '1' },
    { name: t('themeStyle.indent.no'), value: '2' },
]





// 行高下拉数组
const lineHeights = computed(() => {
    return options.value.dicts?.lineHeights.map((item: any) => {
        return {
            content: item.default
                ? l(item.label) + t('base.lineHeight.default')
                : l(item.label),
            value: item.value,
            active: editor.value?.isActive({ lineHeight: item.value }),
        }
    })
})


// 是否选择下拉数组
const judgeList = [
    {
        name: t('themeStyle.italic.yes'), value: '1'
    },
    {
        name: t('themeStyle.italic.no'), value: '2'
    }
]

// 字间距
const spacingList = [
    {
        name: '0', value: '0'
    },
    {
        name: '1', value: '1px'
    },
    {
        name: '2', value: '2px'
    },
    {
        name: '3', value: '3px'
    },
    {
        name: '4', value: '4px'
    },
    {
        name: '5', value: '5px'
    },
]


// 标题数组
const titleList = [
    { name: t('themeStyle.titleLevel.one'), level: 1 },
    { name: t('themeStyle.titleLevel.two'), level: 2 },
    { name: t('themeStyle.titleLevel.three'), level: 3 },
    { name: t('themeStyle.titleLevel.four'), level: 4 },
    { name: t('themeStyle.titleLevel.five'), level: 5 },
    { name: t('themeStyle.titleLevel.six'), level: 6 },
]

const changeTitleLevel = (level: number) => {
    activeLevel.value = level
    realActiveLevel.value = level
}

// 配置校验函数
const validateConfig = (config) => {
    if (!isObject(config)) {
        const msg = '配置文件错误：参数类型错误';
        console.error(msg);
        MessagePlugin.error(msg);
        throw new Error(msg);
    }
    // 具体字段校验
    const requiredSections = ['title', 'paragraph', 'chapterHeader', 'jointHeader'];
    for (const section of requiredSections) {
        if (!(section in config)) {
            const msg = `配置文件错误：缺少必需字段 ${section}`;
            console.error(msg);
            MessagePlugin.error(msg);
            throw new Error(msg);
        }
    }

    // 校验title数组
    if (!Array.isArray(config.title)) {
        const msg = `配置文件错误：参数类型错误`;
        console.error(msg);
        MessagePlugin.error(msg);
        throw new Error(msg);
    }

    // 校验每个title项
    config.title.forEach((item, index) => {
        validateStyleObject(item, `title`);
    });

    // 校验其他样式对象
    validateStyleObject(config.paragraph, 'paragraph');
    validateStyleObject(config.chapterHeader, 'chapterHeader');
    validateStyleObject(config.jointHeader, 'jointHeader');
    return true;
}

// 校验对象的公共方法
const validateStyleObject = (obj, path) => {
    if (!isObject(obj)) {
        const msg = `配置文件错误：参数类型错误`;
        console.error(msg);
        MessagePlugin.error(msg);
        throw new Error(msg);
    }

    const props1 = ['fontFamily', 'fontSize', 'color', 'marginTop', 'marginBottom', 'alignment', 'indent', 'lineHeight', 'italic', 'underline'];
    const props2 = ['fontFamily', 'fontSize', 'color', 'marginTop', 'marginBottom', 'alignment', 'indent', 'lineHeight', 'spacing'];
    let checkProps = []
    if (path === 'title' || path === 'chapterHeader' || path === 'jointHeader') {
        checkProps = props1
    } else if (path === 'paragraph') {
        checkProps = props2
    }

    // 检查必需属性
    for (const prop of checkProps) {
        if (!(prop in obj)) {
            const msg = `配置文件错误：参数类型错误`;
            console.error(msg);
            MessagePlugin.error(msg);
            throw new Error(msg);
        }
    }

    // // 检查属性值类型
    // const numberProps = ['marginTop', 'marginBottom', 'indent', 'spacing'];
    // const stringProps = ['fontFamily', 'fontSize', 'color', 'alignment'];
    // const mixedProps = ['lineHeight', 'italic', 'underline'];

    // for (const prop in obj) {
    //     const value = obj[prop];

    //     if (numberProps.includes(prop) && isNaN(Number(value))) {
    //         throw new Error(`${path}.${prop} 必须是数字`);
    //     }

    //     if (stringProps.includes(prop) && typeof value !== 'string') {
    //         throw new Error(`${path}.${prop} 必须是字符串`);
    //     }

    //     if (mixedProps.includes(prop)) {
    //         const numValue = Number(value);
    //         if (isNaN(numValue) && typeof value !== 'string') {
    //             throw new Error(`${path}.${prop} 必须是数字或字符串`);
    //         }
    //     }
    // }

    // // 检查特定值的有效性
    // if (!['left', 'center', 'right', 'justify'].includes(obj.alignment)) {
    //     throw new Error(`${path}.alignment 必须是 left/center/right/justify`);
    // }

    // if (obj.fontSize && !/^\d+(px|pt|em|rem)$/.test(obj.fontSize)) {
    //     throw new Error(`${path}.fontSize 格式不正确，示例: 18px`);
    // }

    // if (obj.color && !/^#([0-9a-f]{3}){1,2}$/i.test(obj.color)) {
    //     throw new Error(`${path}.color 必须是有效的十六进制颜色值`);
    // }
}

// 读取配置
const readConfig = async () => {
    try {
        const configStr = await navigator.clipboard.readText();
        const config = JSON.parse(configStr)
        console.log('读取配置', config)

        // 校验数据
        validateConfig(config);

        form.value = config
        // 刷新数据
        refresh()
    } catch (error) {
        console.log('读取配置失败', error)
        MessagePlugin.error("读取配置失败, 原因可能是没有权限或者配置格式错误");
    }
}

// 保存配置
const copyConfig = async () => {
    try {
        const configStr = JSON.stringify(form.value)
        console.log('保存配置', configStr)
        // 写入粘贴板
        await navigator.clipboard.writeText(configStr);
        MessagePlugin.success("配置文件已经写入粘贴板，使用请点击读取配置");
    } catch (error) {
        console.log('保存配置失败', error)
        MessagePlugin.error("保存配置失败, 原因可能是没有权限");
    }
}

// 刷新数据
const refresh = () => {
    // 标题
    for (let i = 0; i < form.value.title.length; i++) {
        const item = form.value.title[i];
        realActiveLevel.value = i + 1;
        // setTimeout(() => {
        handleHeadingFontFamilyChange(item.fontFamily)
        handleHeadingFontSizeChange(item.fontSize)
        handleHeadingFontColorChange(item.color)
        handleHeadingItalicChange(item.italic)
        handleHeadingUnderlineChange(item.underline)
        handleHeadingMarginTopChange(item.marginTop)
        handleHeadingMarginBottomChange(item.marginBottom)
        handleHeadingAlignmentChange(item.alignment)
        handleHeadingIndentChange(item.indent)
        handleHeadingLineHeightChange(item.lineHeight)
        // }, 0)
    }

    // 正文
    const paragraph = form.value.paragraph
    handleParagraphFontFamilyChange(paragraph.fontFamily)
    handleParagraphFontSizeChange(paragraph.fontSize)
    handleParagraphFontColorChange(paragraph.color)
    handleParagraphMarginTopChange(paragraph.marginTop)
    handleParagraphMarginBottomChange(paragraph.marginBottom)
    handleParagraphAlignmentChange(paragraph.alignment)
    handleParagraphIndentChange(paragraph.indent)
    handleParagraphLineHeightChange(paragraph.lineHeight)
    handleParagraphSpacingChange(paragraph.spacing)

    // 章头
    const chapterHeader = form.value.chapterHeader
    handleChapterHeaderFontFamilyChange(chapterHeader.fontFamily)
    handleChapterHeaderFontSizeChange(chapterHeader.fontSize)
    handleChapterHeaderFontColorChange(chapterHeader.color)
    handleChapterHeaderItalicChange(chapterHeader.italic)
    handleChapterHeaderUnderlineChange(chapterHeader.underline)
    handleChapterHeaderMarginTopChange(chapterHeader.marginTop)
    handleChapterHeaderMarginBottomChange(chapterHeader.marginBottom)
    handleChapterHeaderAlignmentChange(chapterHeader.alignment)
    handleChapterHeaderIndentChange(chapterHeader.indent)
    handleChapterHeaderLineHeightChange(chapterHeader.lineHeight)
    handleChapterHeaderfontWeightChange(chapterHeader.fontWeight)

    // 节头
    const jointHeader = form.value.jointHeader
    handleJointHeaderFontFamilyChange(jointHeader.fontFamily)
    handleJointHeaderFontSizeChange(jointHeader.fontSize)
    handleJointHeaderFontColorChange(jointHeader.color)
    handleJointHeaderItalicChange(jointHeader.italic)
    handleJointHeaderUnderlineChange(jointHeader.underline)
    handleJointHeaderMarginTopChange(jointHeader.marginTop)
    handleJointHeaderMarginBottomChange(jointHeader.marginBottom)
    handleJointHeaderAlignmentChange(jointHeader.alignment)
    handleJointHeaderIndentChange(jointHeader.indent)
    handleJointHeaderLineHeightChange(jointHeader.lineHeight)
    handleJointHeaderfontWeightChange(jointHeader.fontWeight)
}

//#region 标题
// 字体
const handleHeadingFontFamilyChange = (value: string) => {
    updateMark({
        attrs: { fontFamily: value }, type: 'textStyle'
    })
}

// 字号
const handleHeadingFontSizeChange = (value: string) => {
    updateMark({
        attrs: { fontSize: value }, type: 'textStyle'
    })
}

// 颜色
const handleHeadingFontColorChange = (value: string) => {
    form.value.title[activeLevel.value - 1].color = value
    updateMark({
        attrs: { color: value }, type: 'textStyle'
    })
}

// 斜体
const handleHeadingItalicChange = (value: string) => {
    if (value == 1) {
        updateMark({
            type: 'italic',
        })
    } else {
        updateMark({
            type: 'italic',
            isDel: true
        })
    }

}

// 下划线
const handleHeadingUnderlineChange = (value: string) => {
    if (value == 1) {
        updateMark({
            type: 'underline',
        })
    } else {
        updateMark({
            type: 'underline',
            isDel: true
        })
    }
}

// 段前
const handleHeadingMarginTopChange = useDebounceFn((value) => {
    updateNode({
        type: 'heading',
        attrs: {
            margin: {
                top: value,
            }
        },
        isMerge: true
    })
}, 500)

// 段后
const handleHeadingMarginBottomChange = useDebounceFn((value) => {
    updateNode({
        type: 'heading',
        attrs: {
            margin: {
                bottom: value,
            }
        },
        isMerge: true
    })
})

// 对齐方式
const handleHeadingAlignmentChange = (value: string) => {
    updateNode({
        type: 'heading',
        attrs: {
            textAlign: value,
        },
    })
}

// 首行缩进
const handleHeadingIndentChange = (value: string) => {
    if (value == 1) {
        updateNode({
            type: 'heading',
            attrs: {
                indent: value,
            }
        })
    } else {
        updateNode({
            type: 'heading',
            attrs: {
                indent: null,
            }
        })
    }

}

// 行高
const handleHeadingLineHeightChange = (value: string) => {
    updateNode({
        type: 'heading',
        attrs: {
            lineHeight: value,
        }
    })
}
//#endregion


//#region 正文
// 字体
const handleParagraphFontFamilyChange = (value: string) => {
    updateMark({
        attrs: { fontFamily: value }, type: 'textStyle',
        checkNode: checkParentIsParagraph
    })
}

// 字号
const handleParagraphFontSizeChange = (value: string) => {
    updateMark({
        attrs: { fontSize: value }, type: 'textStyle',
        checkNode: checkParentIsParagraph
    })
}

// 颜色
const handleParagraphFontColorChange = (value: string) => {

    form.value.paragraph.color = value
    updateMark({
        attrs: { color: value }, type: 'textStyle',
        checkNode: checkParentIsParagraph
    })
}

// 斜体
// const handleParagraphItalicChange = (value: string) => {
//     if (value == 1) {
//         updateMark({
//             type: 'italic',
//         })
//     } else {
//         updateMark({
//             type: 'italic',
//             isDel: true
//         })
//     }

// }

// 下划线
// const handleParagraphUnderlineChange = (value: string) => {
//     if (value == 1) {
//         updateMark({
//             type: 'underline',
//         })
//     } else {
//         updateMark({
//             type: 'underline',
//             isDel: true
//         })
//     }
// }

// 段前
const handleParagraphMarginTopChange = useDebounceFn((value) => {
    updateNode({
        type: 'paragraph',
        attrs: {
            margin: {
                top: value,
            }
        },
        isMerge: true,
        checkNode: checkParagraph
    })
}, 500)

// 段后
const handleParagraphMarginBottomChange = useDebounceFn((value) => {
    updateNode({
        type: 'paragraph',
        attrs: {
            margin: {
                bottom: value,
            }
        },
        isMerge: true,
        checkNode: checkParagraph
    })
})

// 对齐方式
const handleParagraphAlignmentChange = (value: string) => {
    updateNode({
        type: 'paragraph',
        attrs: {
            textAlign: value,
        },
        checkNode: checkParagraph
    })
}

// 首行缩进
const handleParagraphIndentChange = (value: string) => {
    if (value == 1) {
        updateNode({
            type: 'paragraph',
            attrs: {
                indent: value,
            },
            checkNode: checkParagraph
        })
    } else {
        updateNode({
            type: 'paragraph',
            attrs: {
                indent: null,
            },
            checkNode: checkParagraph
        })
    }

}

// 行高
const handleParagraphLineHeightChange = (value: string) => {
    console.log(value)
    updateNode({
        type: 'paragraph',
        attrs: {
            lineHeight: value,
        },
        checkNode: checkParagraph
    })
}

// 字符距
const handleParagraphSpacingChange = (value: string) => {
    console.log(value)
    updateMark({
        attrs: { letterSpacing: value }, type: 'textStyle',
        checkNode: checkParentIsParagraph
    })
}
//#endregion


//#region 章头
// 字体
const handleChapterHeaderFontFamilyChange = (value: string) => {
    updateMark({
        attrs: { fontFamily: value }, type: 'textStyle',
        checkNode: checkLastParentIsChapterHeader
    })
}

// 字号
const handleChapterHeaderFontSizeChange = (value: string) => {
    updateMark({
        attrs: { fontSize: value }, type: 'textStyle',
        checkNode: checkLastParentIsChapterHeader
    })
}

// 颜色
const handleChapterHeaderFontColorChange = (value: string) => {
    console.log(value)
    form.value.chapterHeader.color = value;
    updateMark({
        attrs: { color: value }, type: 'textStyle',
        checkNode: checkLastParentIsChapterHeader
    })
}

// 斜体
const handleChapterHeaderItalicChange = (value: string) => {
    if (value == 1) {
        updateMark({
            type: 'italic',
            checkNode: checkLastParentIsChapterHeader
        })
    } else {
        updateMark({
            type: 'italic',
            isDel: true,
            checkNode: checkLastParentIsChapterHeader
        })
    }

}

// 下划线
const handleChapterHeaderUnderlineChange = (value: string) => {
    if (value == 1) {
        updateMark({
            type: 'underline',
            checkNode: checkLastParentIsChapterHeader
        })
    } else {
        updateMark({
            type: 'underline',
            isDel: true,
            checkNode: checkLastParentIsChapterHeader
        })
    }
}

// 段前
const handleChapterHeaderMarginTopChange = useDebounceFn((value) => {
    updateNode({
        type: 'paragraph',
        attrs: {
            margin: {
                top: value,
            }
        },
        isMerge: true,
        checkNode: checkParentIsChapterHeader
    })
}, 500)

// 段后
const handleChapterHeaderMarginBottomChange = useDebounceFn((value) => {
    updateNode({
        type: 'paragraph',
        attrs: {
            margin: {
                bottom: value,
            }
        },
        isMerge: true,
        checkNode: checkParentIsChapterHeader
    })
})

// 对齐方式
const handleChapterHeaderAlignmentChange = (value: string) => {
    updateNode({
        type: 'paragraph',
        attrs: {
            textAlign: value,
        },
        checkNode: checkParentIsChapterHeader
    })
}

// 首行缩进
const handleChapterHeaderIndentChange = (value: string) => {
    if (value == 1) {
        updateNode({
            type: 'paragraph',
            attrs: {
                indent: value,
            },
            checkNode: checkParentIsChapterHeader
        })
    } else {
        updateNode({
            type: 'paragraph',
            attrs: {
                indent: null,
            },
            checkNode: checkParentIsChapterHeader
        })
    }

}

// 行高
const handleChapterHeaderLineHeightChange = (value: string) => {
    updateNode({
        type: 'paragraph',
        attrs: {
            lineHeight: value,
        },
        checkNode: checkParentIsChapterHeader
    })
}

//字体加粗
const handleChapterHeaderfontWeightChange = (value: string) => {
    console.log(value)
    if (value == 1) {
        updateMark({
            type: 'bold',
            checkNode: checkParentIsChapterHeader
        })
    } else {
        updateMark({
            type: 'bold',
            isDel: true,
            checkNode: checkParentIsChapterHeader
        })
    }

}
//#endregion


//#region 节头
// 字体
const handleJointHeaderFontFamilyChange = (value: string) => {
    updateMark({
        attrs: { fontFamily: value }, type: 'textStyle',
        checkNode: checkLastParentIsJointHeader
    })
}

// 字号
const handleJointHeaderFontSizeChange = (value: string) => {
    updateMark({
        attrs: { fontSize: value }, type: 'textStyle',
        checkNode: checkLastParentIsJointHeader
    })
}

// 颜色
const handleJointHeaderFontColorChange = (value: string) => {
    form.value.jointHeader.color = value;
    updateMark({
        attrs: { color: value }, type: 'textStyle',
        checkNode: checkLastParentIsJointHeader
    })
}

// 斜体
const handleJointHeaderItalicChange = (value: string) => {
    if (value == 1) {
        updateMark({
            type: 'italic',
            checkNode: checkLastParentIsJointHeader
        })
    } else {
        updateMark({
            type: 'italic',
            isDel: true,
            checkNode: checkLastParentIsJointHeader
        })
    }

}

// 下划线
const handleJointHeaderUnderlineChange = (value: string) => {
    if (value == 1) {
        updateMark({
            type: 'underline',
            checkNode: checkLastParentIsJointHeader
        })
    } else {
        updateMark({
            type: 'underline',
            isDel: true,
            checkNode: checkLastParentIsJointHeader
        })
    }
}

// 段前
const handleJointHeaderMarginTopChange = useDebounceFn((value) => {
    updateNode({
        type: 'paragraph',
        attrs: {
            margin: {
                top: value,
            }
        },
        isMerge: true,
        checkNode: checkParentIsJointHeader
    })
}, 500)

// 段后
const handleJointHeaderMarginBottomChange = useDebounceFn((value) => {
    updateNode({
        type: 'paragraph',
        attrs: {
            margin: {
                bottom: value,
            }
        },
        isMerge: true,
        checkNode: checkParentIsJointHeader
    })
})

// 对齐方式
const handleJointHeaderAlignmentChange = (value: string) => {
    updateNode({
        type: 'paragraph',
        attrs: {
            textAlign: value,
        },
        checkNode: checkParentIsJointHeader
    })
}

// 首行缩进
const handleJointHeaderIndentChange = (value: string) => {
    if (value == 1) {
        updateNode({
            type: 'paragraph',
            attrs: {
                indent: value,
            },
            checkNode: checkParentIsJointHeader
        })
    } else {
        updateNode({
            type: 'paragraph',
            attrs: {
                indent: null,
            },
            checkNode: checkParentIsJointHeader
        })
    }

}

// 行高
const handleJointHeaderLineHeightChange = (value: string) => {
    updateNode({
        type: 'paragraph',
        attrs: {
            lineHeight: value,
        },
        checkNode: checkParentIsJointHeader
    })
}


//字体加粗
const handleJointHeaderfontWeightChange = (value: string) => {
    if (value == 1) {
        updateMark({
            type: 'bold',
            checkNode: checkParentIsJointHeader
        })
    } else {
        updateMark({
            type: 'bold',
            isDel: true,
            checkNode: checkParentIsJointHeader
        })
    }
}
//#endregion




// 更新mark
const updateMark = ({
    attrs, type,
    checkNode = checkParentIsHeading,
    isDel = false
}) => {
    if (!type) {
        console.warn('type is required')
        return;
    }
    editor?.value?.commands.batchUpdateMarkAndAttrs({
        checkNode: checkNode,
        handleNode: ({ node, pos, parent, index }, { tr, state }) => {
            if (isDel) {
                tr.removeMark(pos, pos + node.nodeSize, getMarkType(type, state.schema))
            } else {
                tr.addMark(pos, pos + node.nodeSize, findMark(state, node.marks, type, attrs))
            }

        }
    })
}

// 更新节点
const updateNode = ({
    attrs, type,
    checkNode = checkIsHeading,
    isMerge = false
}) => {
    if (!type) {
        console.warn('type is required')
        return;
    }
    editor?.value?.commands.batchUpdateMarkAndAttrs({
        checkNode: checkNode,
        handleNode: ({ node, pos, parent, index }, { tr, state }) => {
            if (isMerge) {
                tr.setNodeMarkup(pos, undefined, deepMerge(node.attrs, attrs))
            } else {
                tr.setNodeMarkup(pos, undefined, {
                    ...node.attrs,
                    ...attrs,
                })
            }
        }
    })
}

// 获取mark
const findMark = (state, marks, type, attrs) => {
    const markType = getMarkType(type, state.schema)
    const mark = marks.find(markItem => markItem.type.name === markType.name)
    if (!mark) {
        return markType.create(attrs)
    }
    // 这里的mark好像只是只读的，不能修改
    return markType.create({
        ...(mark.attrs),
        ...attrs,
    });
}

// 合并对象
const deepMerge = (target, source) => {
    const output = { ...target };
    if (isObject(target) && isObject(source)) {
        Object.keys(source).forEach(key => {
            if (isObject(source[key])) {
                output[key] = deepMerge(target[key] || {}, source[key]);
            } else {
                output[key] = source[key];
            }
        });
    }
    return output;
}

const isObject = (obj) => obj !== null && typeof obj === 'object';


// 检测标题
const checkHeading = (node, pos, parent, index) => {
    const { type, attrs: { level } } = node
    if (type.name === 'heading' && level === realActiveLevel.value) {
        return true
    }
    return false
}

// 检测父级是否是标题
const checkParentIsHeading = (node, pos, parent, index) => {
    const { type, attrs: { level } } = parent
    // console.log(node.isText , type.name  ,parent, level, realActiveLevel.value)
    if (node.isText && type.name === 'heading' && level === realActiveLevel.value) {
        return true
    }
    return false
}

// 检测标题节点
const checkIsHeading = (node, pos, parent, index) => {
    const { type, attrs: { level } } = node
    if (type.name === 'heading' && level === realActiveLevel.value) {
        return true
    }
    return false
}

defineExpose({
    open
})

</script>
<style lang="less" scoped>
.themeStyle-drawer {
    border: 1px solid #eee;

    :global(.umo-drawer__header) {
        border: none;
    }

    :global(.umo-color-picker__trigger) {
        width: 108px;
    }

    :global(.umo-input--auto-width) {
        width: fit-content;
        min-width: 60px;
        width: 108px;
    }

    .themeStyle-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;

        .themeStyle-header-title {
            width: 180px;
            flex-shrink: 0;
        }

        .themeStyle-header-btn {
            display: flex;
            justify-content: flex-end;
            margin-right: 30px;
        }

    }

    .titleCss {
        border-bottom: 1px solid #eee;
        margin-bottom: 20px;

        &:last-child {
            border-bottom: none;
        }

        .titleCss-header {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 16px;
        }

        .titleCss-titleLevel {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
        }

        .titleCss-titleLevel-item {
            text-align: center;
            padding: 5px 0;
            cursor: pointer;
        }

        .active {
            color: var(--td-brand-color-8);
            border: 1px solid var(--td-brand-color-9);
            border-radius: 4px;
            background-color: var(--td-brand-color-1);

        }


        // 字体样式
        .titleCss-fontStyle {
            margin: 20px 0;

            .titleCss-fontStyle-level2 {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 10px;

                .titleCss-fontStyle-level2-item6 {
                    display: flex;
                    align-items: center;

                    .titleCss-fontStyle-level2-item6-label {
                        margin-right: 10px;
                    }

                    .titleCss-fontStyle-level2-item6-select {}

                    // 字体颜色
                    .titleCss-fontColor {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;

                        .titleCss-fontColor-label {
                            width: 28px;
                            flex-shrink: 0;
                            margin-right: 10px;
                        }

                        .titleCss-fontColor-select {}
                    }

                    // 段前
                    .titleCss-MarginTop {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        margin-left: 10px;

                        .titleCss-MarginTop-label {
                            width: 28px;
                            flex-shrink: 0;
                            margin-right: 10px;
                        }
                    }

                }

                .titleCss-fontStyle-level2-item4 {
                    display: flex;
                    align-items: center;

                    .titleCss-fontStyle-level2-item4-label {
                        margin-right: 10px;
                    }

                    .titleCss-fontStyle-level2-item4-select {}

                }
            }
        }
    }


}

.themeStyle-header-tip {
    color: #999;
    font-size: 12px;
    margin-left: 10px;
    font-weight: normal;
}
</style>
