<template>
  <div class="umo-ribbon-menu">
    <div v-if="menus.length > 1" class="umo-ribbon-tabs">
      <div v-for="item in menus" :key="item.value" class="umo-ribbon-tabs-item"
        :class="{ active: currentMenu === item.value }" @click="changeMenu(item.value)">
        {{ item.label }}
      </div>

    </div>
    <toolbar-scrollable ref="scrollableRef" class="umo-scrollable-container">
      <div class="umo-ribbon-container">
        <!-- <template v-if="currentMenu === 'base'">
          <div class="umo-virtual-group">
            <div class="umo-virtual-group-row">
              <menus-toolbar-base-undo />
              <menus-toolbar-base-redo />
            </div>
            <div class="umo-virtual-group-row">
              <menus-toolbar-base-format-painter />
              <menus-toolbar-base-clear-format />
            </div>
          </div>
          <div class="umo-virtual-group">
            <div class="umo-virtual-group-row">
              <menus-toolbar-base-font-family />
              <menus-toolbar-base-font-size />
            </div>
            <div class="umo-virtual-group-row">
              <menus-toolbar-base-bold />
              <menus-toolbar-base-italic />
              <menus-toolbar-base-underline />
              <menus-toolbar-base-strike />
              <menus-toolbar-base-subscript />
              <menus-toolbar-base-superscript />
              <menus-toolbar-base-color />
              <menus-toolbar-base-background-color />
              <menus-toolbar-base-highlight />
            </div>
          </div>
          <div class="umo-virtual-group">
            <div class="umo-virtual-group-row">
              <menus-toolbar-base-ordered-list />
              <menus-toolbar-base-bullet-list />
              <menus-toolbar-base-task-list />
              <menus-toolbar-base-indent />
              <menus-toolbar-base-outdent />
              <menus-toolbar-base-line-height />
              <menus-toolbar-base-margin />
            </div>
            <div class="umo-virtual-group-row">
              <menus-toolbar-base-align-left />
              <menus-toolbar-base-align-center />
              <menus-toolbar-base-align-right />
              <menus-toolbar-base-align-justify />
              <menus-toolbar-base-align-distributed />
              <menus-toolbar-base-quote />
              <menus-toolbar-base-code v-if="!disableItem('code')" />
              <menus-toolbar-base-select-all />
            </div>
          </div>
          <div class="umo-virtual-group">
            <menus-toolbar-base-heading />
          </div>
          <div class="umo-virtual-group">
            <menus-toolbar-base-import-word />
            <menus-toolbar-base-markdown />
            <menus-toolbar-base-search-replace />
          </div>
          <div class="umo-virtual-group">
            <menus-toolbar-base-print v-if="!disableItem('print')" />
          </div>
          <div class="virtual-group is-slot">
            <slot name="toolbar_base" toolbar-mode="ribbon" />
          </div>
        </template> -->
        <template v-if="currentMenu === 'insert'">
          <div class="umo-virtual-group">
            <menus-toolbar-insert-link :is-huge="true" />
            <menus-toolbar-insert-paperWrapping />
            <menus-toolbar-insert-chapterHead />
            <menus-toolbar-insert-jointHeader />
            <menus-toolbar-insert-blockView />
            <menus-toolbar-insert-backgroundImg v-if="!disableItem('image')" />
            <menus-toolbar-insert-fold />
            <menus-toolbar-insert-surround />
            <menus-toolbar-insert-image v-if="!disableItem('image')" :is-huge="true" />
            <menus-toolbar-insert-video v-if="!disableItem('video')" />
            <menus-toolbar-insert-audio v-if="!disableItem('audio')" />
            <menus-toolbar-insert-file v-if="!disableItem('file')" />
            <menus-toolbar-insert-code-block v-if="!disableItem('code-block')" />
            <menus-toolbar-insert-symbol />
            <!-- <menus-toolbar-insert-emoji /> -->
            <menus-toolbar-insert-math v-if="!disableItem('math')" />
          </div>
          <!-- <div class="umo-virtual-group"> -->
          <!-- <menus-toolbar-insert-hard-break />
            <menus-toolbar-insert-hr /> -->
          <!-- <menus-toolbar-insert-toc /> -->
          <!-- <menus-toolbar-insert-text-box /> -->
          <!-- <menus-toolbar-insert-test /> -->
          <!-- </div> -->
          <div class="umo-virtual-group">
            <!-- <menus-toolbar-insert-template /> -->
            <!-- <menus-toolbar-insert-web-page /> -->
            <menus-toolbar-insert-questions />
            <menus-toolbar-insert-training />
            <menus-toolbar-insert-testPaper />
            <menus-toolbar-insert-psychology-health />
            <menus-toolbar-insert-schoolAssignment />
            <menus-toolbar-insert-bubble />
            <menus-toolbar-insert-games />
            <menus-toolbar-insert-courseware />
            <menus-toolbar-insert-simulation />
            <menus-toolbar-insert-arandvr />
            <menus-toolbar-insert-threeDimensional />
            <menus-toolbar-insert-expandReading v-if="!disableItem('expandReading')" />
            <menus-toolbar-insert-remark />
            <!-- <menus-toolbar-insert-interaction /> -->
          </div>
          <div class="virtual-group is-slot">
            <slot name="toolbar_insert" toolbar-mode="ribbon" />
          </div>
        </template>
        <template v-if="currentMenu === 'table'">
          <div class="umo-virtual-group">
            <menus-toolbar-table-insert />
            <menus-toolbar-table-template-table />
            <menus-toolbar-table-fix />
          </div>
          <div class="umo-virtual-group">
            <menus-toolbar-table-cells-align />
            <menus-toolbar-table-cells-background />
            <menus-toolbar-table-border-color />
            <menus-toolbar-table-border-style />
          </div>
          <div class="umo-virtual-group">
            <div class="umo-virtual-group-row">
              <menus-toolbar-table-add-row-before />
              <menus-toolbar-table-add-row-after />
              <menus-toolbar-table-delete-row />
            </div>
            <div class="umo-virtual-group-row">
              <menus-toolbar-table-add-column-before />
              <menus-toolbar-table-add-column-after />
              <menus-toolbar-table-delete-column />
            </div>
          </div>
          <div class="umo-virtual-group">
            <div class="umo-virtual-group-row">
              <menus-toolbar-table-merge-cells />
            </div>
            <div class="umo-virtual-group-row">
              <menus-toolbar-table-split-cell />
            </div>
          </div>
          <div class="umo-virtual-group">
            <div class="umo-virtual-group-row">
              <menus-toolbar-table-toggle-header-row />
              <menus-toolbar-table-toggle-header-column />
            </div>
            <div class="umo-virtual-group-row">
              <menus-toolbar-table-toggle-header-cell />
            </div>
          </div>
          <div class="umo-virtual-group">
            <div class="umo-virtual-group-row">
              <menus-toolbar-table-next-cell />
            </div>
            <div class="umo-virtual-group-row">
              <menus-toolbar-table-previous-cell />
            </div>
          </div>
          <div class="umo-virtual-group">
            <menus-toolbar-table-delete />
          </div>
          <div class="virtual-group is-slot">
            <slot name="toolbar_table" toolbar-mode="ribbon" />
          </div>
        </template>
        <template v-if="currentMenu === 'tools'">
          <div class="umo-virtual-group">
            <menus-toolbar-tools-qrcode />
            <menus-toolbar-tools-barcode />
          </div>
          <div class="umo-virtual-group">
            <!-- <menus-toolbar-tools-signature v-if="!disableItem('signature')" /> -->
            <menus-toolbar-tools-seal v-if="!disableItem('seal')" />
            <menus-toolbar-base-search-replace />
          </div>
          <div class="umo-virtual-group">
            <menus-toolbar-tools-diagrams v-if="!disableItem('diagrams')" />
            <!-- <menus-toolbar-tools-mind-map v-if="!disableItem('mind-map')" /> -->
            <menus-toolbar-tools-mermaid v-if="!disableItem('mermaid')" />
          </div>
          <div class="umo-virtual-group">
            <menus-toolbar-tools-chinese-case v-if="!disableItem('chineseCase')" />
          </div>
          <div class="umo-virtual-group">
            <slot name="toolbar_tools" toolbar-mode="ribbon" />
          </div>
        </template>
        <template v-if="currentMenu === 'page'">
          <div class="umo-virtual-group">
            <menus-toolbar-page-toggle-toc />
          </div>
          <div class="umo-virtual-group">
            <div class="umo-virtual-group-row">
              <!-- <menus-toolbar-page-margin /> -->
              <div>
                <div class="umo-virtual-group-row">
                  <menus-toolbar-page-size />
                </div>
                <div class="umo-virtual-group-row">
                  <menus-toolbar-page-orientation />
                </div>


              </div>
            </div>
          </div>
          <div v-if="!hidePageHeader || !hidePageFooter" class="umo-virtual-group">
            <menus-toolbar-page-header v-if="!hidePageHeader" />
            <menus-toolbar-page-footer v-if="!hidePageFooter" />
          </div>
          <div class="umo-virtual-group">
            <menus-toolbar-page-break />
            <menus-toolbar-page-deletePage />
            <menus-toolbar-page-gridPattern />
            <!-- <menus-toolbar-page-pageLine /> -->
            <!-- <menus-toolbar-page-line-number /> -->
            <!-- <menus-toolbar-page-watermark /> -->
            <!-- <menus-toolbar-page-background /> -->
          </div>
          <!-- 页面演示 -->
          <!-- <div class="umo-virtual-group">
            <menus-toolbar-page-preview />
          </div> -->
          <div class="virtual-group is-slot">
            <slot name="toolbar_page" toolbar-mode="ribbon" />
          </div>
        </template>
        <template v-if="currentMenu === 'export'">
          <div class="umo-virtual-group">
            <!-- <menus-toolbar-export-image /> -->
            <menus-toolbar-export-pdf />
            <menus-toolbar-export-word />
            <!-- <menus-toolbar-export-text /> -->
            <!--menus-toolbar-template-table / -->
          </div>
          <!-- <div class="umo-virtual-group">
            <menus-toolbar-export-share v-if="!disableItem('share')" />
            <menus-toolbar-export-embed v-if="!disableItem('embed')" />
          </div> -->
          <div class="virtual-group is-slot">
            <slot name="toolbar_export" toolbar-mode="ribbon" />
          </div>
        </template>
      </div>
    </toolbar-scrollable>
    <!-- 主题样式 -->
    <div class="themeStyle-button">
      <t-button @click="openDrawer" class="buttonCss">
        <template #icon>
          <span class="styleIcon"></span>
        </template>
        {{ t('themeStyle.text') }}
      </t-button>
    </div>
    <themeStyle ref="themeStyleRef" />
  </div>
</template>

<script setup lang="ts">
import themeStyle from '@/components/themeStyle/index.vue'
const visible = ref(false)
const props = defineProps<{
  menus: {
    value: string
    label: string
  }[]
  currentMenu: string
}>()
const emits = defineEmits(['menu-change'])

const { options, hidePageHeader, hidePageFooter, editor } = useStore()
const disableItem = (name: string): boolean | undefined => {
  return options.value.toolbar?.disableMenuItems.includes(name)
}
const themeStyleRef = ref(null);
const scrollableRef = $ref<{ update: () => void }>()
const changeMenu = async (menu: string) => {
  emits('menu-change', menu)
  await nextTick()
  scrollableRef?.update()
}

const openDrawer = () => {
  themeStyleRef.value.open()
}

</script>

<style lang="less" scoped>
.umo-ribbon-menu {
  width: 100%;
  position: relative;
}

:global(.umo-popup__arrow) {
  width: 0;
}

.themeStyle-header-tip {
  color: #999;
  font-size: 12px;
  margin-left: 10px;
  font-weight: normal;
}

.themeStyle-button {
  position: absolute;
  right: 10px;
  top: 50px;


  .buttonCss {
    // background-color: linear-gradient(to right, #EAFEAB, #A3DFFC) !important;
    background: linear-gradient(to right, #EAFEAB, #A3DFFC);
    color: #333;
    border: none;
  }

  .styleIcon {
    width: 20px;
    height: 20px;
    display: inline-flex;
    background: url('@/assets/icons/templateIcon.svg') no-repeat;
    background-size: cover;
    margin-right: 5px;
  }
}



.umo-ribbon-tabs {
  padding: 10px 10px 0;
  display: flex;

  &-item {
    font-size: var(--umo-font-size-small);
    margin-right: 25px;
    cursor: pointer;
    display: flex;
    align-items: center;
    flex-direction: column;
    font-size: 14px;

    &:hover {
      font-weight: 600;

      &::after {
        display: block;
        content: '';
        height: 3px;
        width: 100%;
        margin-top: 5px;
        background-color: var(--umo-border-color);
      }
    }

    &.active {
      color: var(--umo-primary-color);
      font-weight: 600;

      &::after {
        display: block;
        content: '';
        height: 3px;
        width: 100%;
        margin-top: 5px;
        background-color: var(--umo-primary-color);
        transition: width 0.3s;
      }

      &:hover::after {
        width: 120%;
      }
    }

    @media screen and (max-width: 640px) {
      margin-right: 10px;
    }
  }
}

.umo-scrollable-container {
  width: 100%;
  padding: 10px;
  box-sizing: border-box;
}

.umo-ribbon-container {
  display: flex;
  height: 56px;
  flex-shrink: 0;

  .umo-virtual-group {
    padding: 0 20px;
    border-left: solid 1px var(--umo-border-color-light);
    flex-shrink: 0;

    &:empty {
      display: none;
    }

    &:first-child {
      padding-left: 0;
    }

    &:first-child,
    &.is-slot:empty {
      border-left: none;
    }

    &-row {
      display: flex;
      align-items: center;

      :deep(> *:not(:last-child)) {
        margin-right: 5px;
      }

      &:not(:last-child) {
        margin-bottom: 5px;
      }
    }
  }
}
</style>
