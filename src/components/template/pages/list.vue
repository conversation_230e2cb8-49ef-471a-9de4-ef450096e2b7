<template>
  <div ref="container">
    <div>
      <t-radio-group v-model="selectType" variant="primary-filled" :default-value="1" size="large"
        @change="tapSelectChange">
        <t-radio-button :value="1">公开教材</t-radio-button>
        <t-radio-button :value="2">校本教材</t-radio-button>
      </t-radio-group>
      <div class="search-form">
        <t-form ref="queryFormRef" :data="queryParams" label-width="100px" layout="inline" @reset="resetQuery">
          <t-form-item label="教材名称" name="bookName">
            <t-input v-model="queryParams.bookName" placeholder="请输入教材名称" clearable maxlength="30"
              style="width: 200px"></t-input>
          </t-form-item>
          <t-form-item label="教材编号" name="bookNo">
            <t-input v-model="queryParams.bookNo" placeholder="请输入教材编号" clearable maxlength="20"
              style="width: 200px"></t-input>
          </t-form-item>
          <t-form-item v-if="selectType == 1" label="ISBN" name="isbn">
            <t-input v-model="queryParams.isbn" placeholder="请输入ISBN " clearable maxlength="40"
              style="width: 200px"></t-input>
          </t-form-item>
          <t-form-item v-if="selectType == 1" label="ISSN：" name="issn">
            <t-input v-model="queryParams.issn" placeholder="请输入ISSN " clearable maxlength="40"
              style="width: 200px"></t-input>
          </t-form-item>
          <t-form-item label="出版单位" name="houseId">
            <t-select v-model="queryParams.houseId" placeholder="全部" clearable filterable style="width: 200px">
              <t-option v-for="item in publishHouselList" :key="item.houseId" :label="item.houseName"
                :value="item.houseId" />
            </t-select>
          </t-form-item>
          <t-form-item label="主/副教材" name="masterFlag">
            <t-select v-model="queryParams.masterFlag" clearable filterable placeholder="全部" style="width: 200px">
              <t-option v-for="item in bookNatureTypeList" :key="item.value" :label="item.label" :value="item.value" />
            </t-select>
          </t-form-item>

          <t-form-item label="学校" name="schoolId">
            <t-select v-model="queryParams.schoolId" clearable filterable placeholder="全部" style="width: 200px">
              <t-option v-for="item in schoolList" :key="item.schoolId" :label="item.schoolName"
                :value="item.schoolId" />
            </t-select>
          </t-form-item>

          <t-form-item label="教材状态" name="publishStatus">
            <t-select v-model="queryParams.publishStatus" clearable filterable placeholder="全部" style="width: 200px">
              <t-option v-for="item in bookPublishStatusList" :key="item.value" :label="item.label"
                :value="item.value" />
            </t-select>
          </t-form-item>
          <t-form-item label="节点" name="stepId">
            <t-select v-model="queryParams.stepId" clearable placeholder="全部" filterable style="width: 200px">
              <t-option v-for="item in bookPublishStepList" :key="item.stepId" :label="item.stepName"
                :value="item.stepId" />
            </t-select>
          </t-form-item>
          <t-form-item label="销售状态" name="shelfState">
            <t-select v-model="queryParams.shelfState" clearable placeholder="全部" filterable style="width: 200px">
              <t-option v-for="item in bookShelfStatusOptions" :key="item.value" :label="item.label"
                :value="item.value" />
            </t-select>
          </t-form-item>
          <t-button theme="primary" type="submit" style="margin-right: 20px" @click="handleQuery">查询</t-button>
          <t-button theme="default" type="reset">重置</t-button>
        </t-form>
      </div>
      <div style="display: flex; align-items: center">
        <t-button theme="primary" @click="handleExport">导出 </t-button>
        <t-tooltip content="主编和书稿联系人只有在教材从未三审三校流程通过时才可以进行导出操作">
          <view style="
              margin-left: 10px;
              cursor: pointer;
              border: 1px solid #176eb8;
              border-radius: 5px;
              height: 13px;
              width: 13px;
              padding: 9px;
              display: flex;
              justify-content: center;
              align-items: center;
            ">
            <img src="@/assets/images/helpsIcon.svg" />
          </view>
        </t-tooltip>
      </div>
      <div>
        <t-enhanced-table row-key="bookId" :columns="columnList" :data="dataList" :loading="tableLoading" :tree="{
          childrenKey: 'children',
          treeNodeColumnIndex: 1,
          indent: 25,
          expandTreeNodeOnClick: true,
        }" :selected-row-keys="selectedRowKeys" :pagination="queryParams" @select-change="handleSelectionChange"
          @page-change="onPageChange">
          <template #tree-expand-and-fold-icon="{ type, row }">
            <ChevronRightIcon v-if="type === 'expand'" />
            <ChevronDownIcon v-else />
          </template>
          <template #cover="{ row }">
            <image-view :img="row.cover ||
              'http://dutp-test.oss-cn-beijing.aliyuncs.com/1741232140222.png'
              " />
          </template>
          <template #priceSale="{ row }">
            {{ row.priceSale || (row.masterFlag == 3 ? '0' : '') }}
          </template>
          <template #masterFlag="{ row }">
            <t-tag theme="warning">{{
              getOptionDesc(bookNatureTypeList, row.masterFlag)
            }}</t-tag>
          </template>

          <template #shelfState="scope">
            <!-- <t-tag theme="primary">{{
              getOptionDesc(bookShelfStatusOptions, scope.row.shelfState)
            }}</t-tag> -->
            <div v-if="scope.row.currentVersionId != scope.row.lastVersionId">
              <t-tag theme="primary" type="warning"> 未上架 </t-tag>
            </div>
            <t-tag v-else theme="primary" type="warning">{{
              getOptionDesc(bookShelfStatusOptions, scope.row.shelfState)
            }}</t-tag>
          </template>

          <template #stepName="scope">
            <div v-if="scope.row.isAmend == true">
              <t-tag type="success">已发版</t-tag>
            </div>
            <t-tag v-else-if="
              scope.row.auditState == null || scope.row.auditState == 3
            " type="success">制作中</t-tag>
            <t-tag v-else type="success">{{ scope.row.stepName }}</t-tag>
          </template>

          <template #publishStatus="scope">
            <div v-if="scope.row.currentVersionId != scope.row.lastVersionId">
              <t-tag> 未出版 </t-tag>
            </div>
            <t-tag v-else>{{
              getOptionDesc(bookPublishStatusList, scope.row.publishStatus)
            }}</t-tag>
          </template>
          <template #completeRate="scope">
            {{ scope.row.completeRate + '%' }}
          </template>

          <template #bookNo="scope">
            <span v-if="scope.row.currentVersionId == scope.row.lastVersionId">{{ scope.row.bookNo }}</span>
            <div v-if="scope.row.currentVersionId != scope.row.lastVersionId">
              <t-tag> 修正版本 </t-tag>{{ scope.row.bookNo }}
            </div>
          </template>

          <template #opt="scope">
            <t-button v-if="scope.row.currentVersionId == scope.row.lastVersionId" style="margin-right: 20px"
              @click.stop="handleView(scope.row.bookId)">
              <template #icon>
                <SearchIcon />
              </template>
              查看
            </t-button>
            <t-button v-if="
              scope.row.isShowAmendBtn &&
              scope.row.publishStatus == 2 &&
              scope.row.shelfState != 3 &&
              scope.row.currentVersionId == scope.row.lastVersionId &&
              scope.row.isAmend == false
            " @click.stop="handleAmend(scope.row)">
              <template #icon>
                <ChartRing1Icon />
              </template>
              修正
            </t-button>
          </template>
        </t-enhanced-table>
      </div>
    </div>
    <!-- 修正 -->
    <t-dialog v-model:visible="amendOpen" title="修正" placement="center" width="650px" :show-in-attached-element="true"
      :confirm-btn="null" :cancel-btn="null">
      <t-form ref="amendFormRef" :data="form" :rules="{
        versionNo: [
          {
            required: true,
            message: '版本号不能为空',
            type: 'error',
            trigger: 'blur',
          },
          {
            pattern: /^\d+\.\d+\.\d+$/,
            message: '格式不正确，请输入数字.数字.数字的格式',
            type: 'error',
            trigger: 'blur',
          },
        ],
        reason: [
          {
            required: true,
            message: '修正原因不能为空',
            type: 'error',
            trigger: 'blur',
          },
        ],
      }" label-width="100px" @submit="confirmAmend">
        <div style="color: red; padding: 20px">
          注意：进行修正后，当前版本教材会回退到发布环节，重新进入三审三校流程。
        </div>

        <t-form-item label="版本号" name="versionNo" required>
          <t-input v-model="form.versionNo" placeholder="请输入版本号" :maxlength="20">
          </t-input>
        </t-form-item>
        <t-form-item label="修正原因" name="reason" required>
          <t-textarea v-model="form.reason" placeholder="请输入内容" :maxlength="200" show-word-limit
            :autosize="{ minRows: 2, maxRows: 4 }">
          </t-textarea>
        </t-form-item>
        <t-form-item label="退回至">
          <t-tag>发布环节</t-tag>
        </t-form-item>
        <div class="dialog-footer">
          <t-button theme="default" style="margin-right: 20px" @click="amendOpen = false">取消</t-button>
          <t-button theme="primary" type="submit">保 存</t-button>
        </div>
      </t-form>
    </t-dialog>
    <feedbackModal ref="feedbackModalRef" :info="info" />
  </div>
</template>
<script setup>
import {
  ChartRing1Icon,
  ChevronDownIcon,
  ChevronRightIcon,
  SearchIcon,
} from 'tdesign-icons-vue-next'
import { useRoute, useRouter } from 'vue-router'

import { ListbookAreaNotPage } from '@/api/basic/bookArea.js'
import { listNoPage as listPublishHouse } from '@/api/basic/house.js'
import { listSchoolNoPage } from '@/api/basic/school.js'
import { listSubjectNotPage } from '@/api/basic/subject'
import { amendBook, exportBookList, listBook } from '@/api/book/book.js'
import { listStepNotPage } from '@/api/book/bookPublishStep.js'
import { listLanguage } from '@/api/book/language.js'
import { listType } from '@/api/book/type'
import { checkHasFeedback } from '@/api/message/feedback.js'
import ImageView from '@/components/ImageView.vue'
import {
  bookNatureTypeList,
  bookPublishStatusOptions,
  bookSchoolStatusOptions,
  bookShelfStatusOptions,
  getOptionDesc,
} from '@/utils/optionUtil.js'

import feedbackModal from '../components/feedbackModal.vue'
const { proxy } = getCurrentInstance()
const selectType = ref(1)
const container = ref(null)
const route = useRoute()
const router = useRouter()
const amendOpen = ref(false)
const dataList = ref([])
const publishHouselList = ref([])
const publishSteplList = ref([])
const schoolList = ref([])
const typeList = ref([])
const bookAreaList = ref([])
const languageList = ref([])
const form = ref({})
const eduSubjectList = ref([])
const amendFormRef = ref(null)
// 校本教材
const schoolBookColumns = [
  {
    colkey: 'bookId',
    type: 'multiple',
    width: 50,
    title: '选择',
    align: 'center',
    fixed: 'left',
  },
  {
    colKey: 'bookNo',
    title: '教材编号',
    width: 150,
    ellipsis: true,
    fixed: 'left',
  },
  {
    colKey: 'bookName',
    title: '教材名称',
    width: 150,
    ellipsis: true,
    fixed: 'left',
  },
  {
    colKey: 'cover',
    title: '封面',
    width: 120,
  },
  { colKey: 'publishStatus', width: 110, ellipsis: true, title: '教材状态' },
  { colKey: 'stepName', width: 100, ellipsis: true, title: '节点' },
  { colKey: 'masterFlag', width: 110, ellipsis: true, title: '主/副教材' },
  { colKey: 'deputyBookName', ellipsis: true, width: 120, title: '关联教材' },
  { colKey: 'houseName', width: 110, ellipsis: true, title: '出版单位' },
  { colKey: 'schoolName', width: 100, ellipsis: true, title: '学校' },
  { colKey: 'publishDate', width: 130, ellipsis: true, title: '出版时间' },
  { colKey: 'versionNo', width: 100, ellipsis: true, title: '版本号' },
  { colKey: 'topicNo', width: 100, ellipsis: true, title: '选题号' },
  { colKey: 'edition', width: 100, ellipsis: true, title: '版次' },
  { colKey: 'createTime', width: 200, ellipsis: true, title: '创建时间' },
  {
    colKey: 'completeRate',
    width: 120,
    ellipsis: true,
    title: '全书完成度',
  },
  {
    title: '操作',
    colKey: 'opt',
    fixed: 'right',
    foot: '-',
    width: 240,
  },
]

// 公开教材
const openBookColumns = [
  {
    colkey: 'bookId',
    type: 'multiple',
    width: 50,
    title: '选择',
    align: 'center',
    fixed: 'left',
  },
  {
    colKey: 'bookNo',
    title: '教材编号',
    width: 220,
    ellipsis: true,
    fixed: 'left',
  },
  {
    colKey: 'bookName',
    title: '教材名称',
    width: 150,
    ellipsis: true,
    fixed: 'left',
  },
  {
    colKey: 'cover',
    title: '封面',
    width: 120,
  },
  { colKey: 'isbn', width: 200, ellipsis: true, title: 'ISBN' },
  { colKey: 'issn', width: 200, ellipsis: true, title: 'ISSN' },
  { colKey: 'publishStatus', width: 110, ellipsis: true, title: '教材状态' },
  { colKey: 'shelfState', width: 110, ellipsis: true, title: '销售状态' },
  { colKey: 'stepName', width: 100, ellipsis: true, title: '节点' },
  { colKey: 'masterFlag', width: 110, ellipsis: true, title: '主/副教材' },
  { colKey: 'deputyBookName', ellipsis: true, width: 120, title: '关联教材' },
  { colKey: 'priceCounter', width: 100, ellipsis: true, title: '定价(元)' },
  { colKey: 'priceSale', width: 100, ellipsis: true, title: '售价(元)' },
  { colKey: 'houseName', width: 110, ellipsis: true, title: '出版单位' },
  { colKey: 'schoolName', width: 100, ellipsis: true, title: '学校' },
  { colKey: 'publishDate', width: 130, ellipsis: true, title: '出版时间' },
  { colKey: 'versionNo', width: 100, ellipsis: true, title: '版本号' },
  { colKey: 'topicNo', width: 100, ellipsis: true, title: '选题号' },
  { colKey: 'edition', width: 100, ellipsis: true, title: '版次' },
  { colKey: 'createTime', width: 200, ellipsis: true, title: '创建时间' },
  { colKey: 'completeRate', width: 120, ellipsis: true, title: '全书完成度' },
  {
    title: '操作',
    colKey: 'opt',
    fixed: 'right',
    foot: '-',
    width: 240,
  },
]
const tableLoading = ref(false)
const queryParams = ref({
  current: 1,
  pageSize: 10,
  total: 10,
  bookOrganize: 1,
})
const selectedRowKeys = ref([])
const info = ref('')

//#region 计算属性相关

// 表格所显示字段
const columnList = computed(() => {
  if (selectType.value == 1) {
    return openBookColumns
  } else {
    return schoolBookColumns
  }
})
const bookPublishStepList = computed(() => {
  if (selectType.value == 1) {
    return publishSteplList.value
  } else {
    return publishSteplList.value.filter((item) => item.schoolFlag == 1)
  }
})

const bookPublishStatusList = computed(() => {
  if (selectType.value == 1) {
    return bookPublishStatusOptions
  } else {
    return bookSchoolStatusOptions
  }
})
//#endregion

//#region 生命周期相关

onMounted(() => {
  checkHasFeedbackList()
  getPublishHouseList()
  getSchoolList()
  getPublishStepList()
  getTypeList()
  getEduSubjectList()
  getBookAreaList()
  getLanguageList()
  getList()
  document.documentElement.removeAttribute('theme-mode')
})

//#endregion

//#region 获取数据相关

// 检测是否有未读反馈
const checkHasFeedbackList = () => {
  checkHasFeedback().then((res) => {
    if (res.data) {
      info.value = res.data
      proxy.$refs['feedbackModalRef'].show()
    }
  })
}
// 获取出版社列表
const getPublishHouseList = () => {
  listPublishHouse().then((res) => {
    publishHouselList.value = res.data
  })
}

// 获取学校
const getSchoolList = () => {
  listSchoolNoPage().then((res) => {
    schoolList.value = res.data
  })
}

// 获取节点
const getPublishStepList = () => {
  listStepNotPage().then((res) => {
    publishSteplList.value = res.data
  })
}

// 获取中图分类
const getTypeList = () => {
  listType().then((res) => {
    typeList.value = res.data
  })
}

// 获取教育学科分类
const getEduSubjectList = () => {
  listSubjectNotPage().then((res) => {
    eduSubjectList.value = res.data
  })
}

// 获取专区分类
const getBookAreaList = () => {
  ListbookAreaNotPage().then((res) => {
    bookAreaList.value = res.data
  })
}

// 获取语种
const getLanguageList = () => {
  listLanguage().then((res) => {
    languageList.value = res.data
  })
}

// 获取教材数据
const getList = () => {
  tableLoading.value = true
  listBook(queryParams.value).then((res) => {
    dataList.value = res.rows
    dataList.value.forEach((e) => {
      e.isAmend = false
      if (e.masterBookName) {
        e.deputyBookName = e.masterBookName
      }
      if (e.children) {
        e.isAmend = true
        e.children = [
          {
            ...e.children[0],
            bookId: `${e.children[0].bookId}amend`,
          },
        ]
      }
    })
    queryParams.value.total = res.total
    tableLoading.value = false
  })
}

//#endregion

//#region 操作相关
const onPageChange = (pageInfo) => {
  queryParams.value.pageSize = pageInfo.pageSize
  queryParams.value.pageNum = pageInfo.current
  queryParams.value.current = pageInfo.current
  getList()
}

// tap切换
const tapSelectChange = (value) => {
  queryParams.value.bookOrganize = value
  queryParams.value.pageNum = 1
  queryParams.value.current = 1
  getList()
}

// 勾选
const handleSelectionChange = (value, ctx) => {
  selectedRowKeys.value = value
}

// 导出按钮操作
function handleExport() {
  // console.log('handleExport', selectedRowKeys.value)
  if (selectedRowKeys.value.length == 0) {
    MessagePlugin.error('请选择要导出的数据')
    return
  }
  const idList = [
    ...new Set(
      selectedRowKeys.value.filter((item) => item.indexOf('amend') == -1),
    ),
  ]
  exportBookList({
    bookIdList: idList,
  }).then((res) => {
    const confirmDia = DialogPlugin.confirm({
      header: '导出成功',
      body: '已发起导出任务，请到任务中心查看',
      theme: 'success',
      cancelBtn: '留在本页',
      confirmBtn: '前往任务中心',
      onClose: () => {
        confirmDia.destroy()
      },
      onConfirm() {
        router.push('/pages/taskCenter')
        confirmDia.destroy()
      },
    })
  })
}

// 查看按钮操作
const handleView = (bookId) => {
  router.push({
    path: '/pages/detail',
    query: { bookId: bookId.replace('amend', '') },
  })
}

// 修正按钮操作
const handleAmend = (row) => {
  form.value = {
    bookId: row.bookId,
    versionNo: row.versionNo,
    recallType: 1,
    reason: '',
  }
  amendOpen.value = true
}

// 确认修正
const confirmAmend = ({ validateResult, firstError }) => {
  if (validateResult === true) {
    amendBook(form.value).then((res) => {
      MessagePlugin.success('提交成功')
      amendOpen.value = false
      getList()
    })
  } else {
    MessagePlugin.warning(firstError)
  }
}
// 搜索按钮操作
const handleQuery = () => {
  queryParams.value.pageNum = 1
  queryParams.value.current = 1
  getList()
}

// 重置按钮操作
const resetQuery = () => {
  handleQuery()
}
//#endregion
</script>

<style lang="less" scoped>
.t-radio-group.t-radio-group--filled {
  border-color: var(--td-bg-color-component);
  padding: 6px;
  border-radius: 8px;
  background-color: #e8f4fe;
  position: relative;
}

.t-radio-group.t-radio-group--primary-filled .t-radio-button.t-is-checked {
  color: #0966b4;
  background-color: #fff;
}

.search-form {
  margin: 20px 0;
}

:global(.dialog-footer) {
  display: flex;
  justify-content: flex-end;
}

:deep(.tdesign-demo-image-viewer__ui-image--img) {
  width: 60px;
  height: 80px;
}
</style>
