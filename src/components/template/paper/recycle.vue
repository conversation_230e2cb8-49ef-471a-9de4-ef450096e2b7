<template>
    <div class="app-container">
        <div>
            <!-- Tab切换 -->
            <div class="tab-header">
                <t-tabs v-model="activeTab" @change="handleTabClick">
                    <t-tab-panel value="1" label="试卷回收站" />
                    <t-tab-panel value="2" label="作业回收站" />
                </t-tabs>
            </div>

            <div class="tool">
                <t-form :data="queryParams" ref="queryRef"  v-show="showSearch" style="margin-bottom: 10px; display: flex; ">
                    <t-form-item name="paperTitle" label="试卷名称" style="margin-right: 10px;">
                        <t-input
                            v-model="queryParams.paperTitle"
                            placeholder="请输入试卷名称"
                            clearable
                            style="width: 200px;"
                            @keyup.enter="handleQuery"
                        />
                    </t-form-item>
                    <t-form-item name="actions" label-width="0" style="display: flex;">
                        <t-button theme="primary" @click="handleQuery" style="margin-right: 5px;">
                            <template #icon><t-icon name="search" /></template>搜索
                        </t-button>
                        <t-button @click="resetQuery" style="margin-right: 5px;">
                            <template #icon><t-icon name="refresh" /></template>重置
                        </t-button>
                        <t-button theme="primary" variant="outline" @click="goBack" style="margin-right: 5px;">
                            <template #icon><t-icon name="arrow-left" /></template>返回
                        </t-button>
                    </t-form-item>
                </t-form>

              <div class="mb8" style="display: flex">
                        <t-checkbox 
                            v-model="isAllSelected" 
                            @change="handleSelectAll"
                            style="margin-right: 5px;"
                        >全选</t-checkbox>


                        <t-button
                            theme="primary"
                            variant="outline"
                            @click="handleBatchRestore"
                            :disabled="selectedPapers.length === 0"
                            style="margin-right: 5px;"
                        >
                            <template #icon><t-icon name="check" /></template>批量恢复
                        </t-button>


                        <t-button
                    
                            theme="danger"
                            variant="outline"
                            @click="handleBatchDelete"
                            :disabled="selectedPapers.length == 0"
                            style="margin-right: 5px;"
                        >
                            <template #icon><t-icon name="delete" /></template>批量删除
                        </t-button>

              </div>
            </div>

            <div class="paper-list">
                <t-row :gutter="[40, 0]">
                    <t-col :span="24" v-for="(item, index) in paperList" :key="index">
                        <t-card class="paper-item" >
                            <div class="paper-content">
                                <t-checkbox 
                                    v-model="item.isSelected"
                                    @change="handleSelectionChange(item)"
                                />
                                
                                <div class="paper-icon">
                                    <t-icon name="delete" size="40px" style="color: #E34D59" />
                                </div>
                                
                                <div class="paper-info">
                                    <h3 class="paper-title">{{ item.paperTitle }}</h3>
                                    <div class="paper-stats">
                                        <span>试题数量: {{ item.questionQuantity }}</span>
                                        <t-divider layout="vertical" />
                                        <span v-if="activeTab === '1'">总分: {{ item.totalScore }}</span>
                                        <template v-if="activeTab === '1'"><t-divider layout="vertical" /></template>
                                        <span>删除时间: {{ item.updateTime }}</span>
                                    </div>
                                </div>

                                <div class="paper-actions">
                                    <t-button theme="primary" variant="text" @click="handlePreview(item)">预览</t-button>
                                    <t-button theme="primary" variant="text" @click="handleRestore(item)" >恢复</t-button>
                                    <t-button theme="danger" variant="text" @click="handleDelete(item)" >彻底删除</t-button>
                                </div>
                            </div>
                        </t-card>
                    </t-col>
                </t-row>
            </div>

            <t-pagination
                v-show="total>0"
                :total="total"
                v-model:current="queryParams.pageNum"
                v-model:pageSize="queryParams.pageSize"
                @change="getList"
            />
        </div>

        <!-- 使用预览组件 -->
        <paper-preview 
            v-model="previewDialogVisible" 
            :paperId="currentPreviewPaperId" 
            :paperType="activeTab"
            @load-error="handlePreviewError"
        />
    </div>
</template>

<script setup name="PaperRecycle">
import { recycleList, restoreFromRecycleBin, delPaper } from "@/api/book/paper";
import { useRouter } from "vue-router";
import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next';
import PaperPreview from './preview/index.vue';

const { proxy } = getCurrentInstance();

const paperList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const total = ref(0);
const route = useRouter();
const activeTab = ref(route.currentRoute.value.query.activeTab || '1'); // 从路由参数获取 activeTab
const typeName = computed(() => activeTab.value === '1' ? '试卷' : '作业');

const data = reactive({
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        paperTitle: null,
        paperType: '1', // 默认查询试卷类型
    }
});

const { queryParams } = toRefs(data);

const router = useRouter();

// 添加选中试卷数组
const selectedPapers = ref([]);

// 添加全选状态变量
const isAllSelected = ref(false);

// 预览相关的响应式变量
const previewDialogVisible = ref(false);
const currentPreviewPaperId = ref(null);

onActivated(() => {
  // 从路由参数获取activeTab值，如果存在则更新当前activeTab
  const routeActiveTab = route.currentRoute.value.query.activeTab;
  if (routeActiveTab) {
    activeTab.value = routeActiveTab;
    // 同步更新查询参数中的paperType
    queryParams.value.paperType = routeActiveTab;
  }
  getList();
})

/** 查询回收站列表 */
function getList() {
    loading.value = true;
    recycleList(queryParams.value).then(response => {
        paperList.value = response.rows.map(item => ({
            ...item,
            isSelected: false // 确保每次加载列表时重置选中状态
        }));
        total.value = response.total;
        loading.value = false;
        // 重置全选状态
        isAllSelected.value = false;
        // 清空已选择的试卷
        selectedPapers.value = [];
    });
}

/** 搜索按钮操作 */
function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 恢复按钮操作 */
function handleRestore(row) {
    const $restoreDialog = DialogPlugin.confirm({
        header: '确认',
        body: `是否确认恢复该${typeName.value}？`,
        onConfirm: () => {
            return restoreFromRecycleBin(row.paperId).then(() => {
                getList();
                MessagePlugin.success("恢复成功");
                $restoreDialog.hide();
            }).catch(error => {
                MessagePlugin.error("操作失败");
                console.error(error);
            }).finally(() => {
                $restoreDialog.hide();
            });
        },
        onClose: () => {
            $restoreDialog.hide();
        }
    });
}

/** 彻底删除按钮操作 */
function handleDelete(row) {
    const $removeDialog = DialogPlugin.confirm({
        header: '确认',
        body: `此操作将永久删除该${typeName.value}，是否继续？`,
        onConfirm: () => {
            return delPaper(row.paperId).then(() => {
                getList();
                MessagePlugin.success("删除成功");
                $removeDialog.hide();
            }).catch(error => {
                MessagePlugin.error("操作失败");
                console.error(error);
            }).finally(() => {
                $removeDialog.hide();
            });
        },
        onClose: () => {
            $removeDialog.hide();
        }
    });
}

/** 处理选择变化 */
function handleSelectionChange(row) {
    if (row.isSelected) {
        selectedPapers.value.push(row);
    } else {
        const index = selectedPapers.value.findIndex(item => item.paperId === row.paperId);
        if (index !== -1) {
            selectedPapers.value.splice(index, 1);
        }
    }
}

/** 批量恢复 */
function handleBatchRestore() {
    if (selectedPapers.value.length === 0) {
        MessagePlugin.error(`请选择要恢复的${typeName.value}`);
        return;
    }
    const paperIds = selectedPapers.value.map(item => item.paperId);
    
    const $restoreDialog = DialogPlugin.confirm({
        header: '确认',
        body: `是否确认恢复选中的${typeName.value}？`,
        onConfirm: () => {
            return restoreFromRecycleBin(paperIds.join(',')).then(() => {
                getList();
                selectedPapers.value = [];
                MessagePlugin.success("批量恢复成功");
                $restoreDialog.hide();
            }).catch(error => {
                MessagePlugin.error("操作失败");
                console.error(error);
            }).finally(() => {
                $restoreDialog.hide();
            });
        },
        onClose: () => {
            $restoreDialog.hide();
        }
    });
}

/** 批量删除 */
function handleBatchDelete() {
    if (selectedPapers.value.length === 0) {
        MessagePlugin.error(`请选择要删除的${typeName.value}`);
        return;
    }
    const paperIds = selectedPapers.value.map(item => item.paperId);
    
    const $removeDialog = DialogPlugin.confirm({
        header: '确认',
        body: `此操作将永久删除选中的${typeName.value}，是否继续？`,
        onConfirm: () => {
            return delPaper(paperIds.join(',')).then(() => {
                getList();
                selectedPapers.value = [];
                MessagePlugin.success("批量删除成功");
                $removeDialog.hide();
            }).catch(error => {
                MessagePlugin.error("操作失败");
                console.error(error);
            }).finally(() => {
                $removeDialog.hide();
            });
        },
        onClose: () => {
            $removeDialog.hide();
        }
    });
}

/** Tab切换操作 */
function handleTabClick(value) {
    queryParams.value.paperType = value;
    getList();
}

/** 返回按钮操作 */
function goBack() {
    router.push({
        path: '/resourceLibrary/paper',
        query: { activeTab: activeTab.value }
    });
}

// 添加预览方法
const handlePreview = (row) => {
    currentPreviewPaperId.value = row.paperId;
    previewDialogVisible.value = true;
};

// 处理预览加载错误
const handlePreviewError = (message) => {
    MessagePlugin.error(message);
};

/** 全选/取消全选操作 */
function handleSelectAll(val) {
    // 更新所有试卷的选中状态
    paperList.value.forEach(paper => {
        paper.isSelected = val;
    });
    
    // 更新已选择的试卷数组
    if (val) {
        selectedPapers.value = [...paperList.value];
    } else {
        selectedPapers.value = [];
    }
}

getList();
</script>

<style scoped>
.tool {
    padding: 20px 20px 0;  /* 修改上边距，移除左侧空白 */

    :deep(.t-form) {
        margin-bottom: 24px;  /* 增加与按钮的间距 */
    }

    .t-row {
        display: flex;
        align-items: center;
        gap: 12px;

        .t-col {
            .t-button {
                width: auto;  /* 修改按钮宽度为自适应 */
                min-width: 120px;  /* 设置最小宽度确保按钮不会太窄 */
            }
        }
    }
}

.paper-list {
    padding: 20px;

    .t-row {
        margin: 0 !important;  /* 移除 row 的默认边距 */
    }

    .t-col {
        width: 100%;
        flex: 0 0 100%;  /* 确保每个卡片占据整行 */
        padding: 0 !important;  /* 移除默认内边距 */
    }

    .paper-item {
        margin-bottom: 16px;
        background-color: #FDF2EC;
        
        .paper-content {
            display: flex;
            align-items: center;
            padding: 16px 24px;
            gap: 20px;  /* 使用 gap 统一间距 */

            :deep(.t-checkbox) {
                flex-shrink: 0;  /* 防止 checkbox 被压缩 */
                margin-right: 0;  /* 移除右边距，使用 gap 控制间距 */
            }
        }

        .paper-icon {
            flex-shrink: 0;  /* 防止图标被压缩 */
        }

        .paper-info {
            flex: 1;
            min-width: 0;  /* 防止文本溢出 */
            
            .paper-title {
                margin: 0 0 8px 0;
                font-size: 16px;
                color: #303133;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .paper-stats {
                color: #666;
                font-size: 14px;
                display: flex;
                align-items: center;
                gap: 15px;  /* 使用 gap 替代分隔线边距 */
            }
        }

        .paper-actions {
            display: flex;
            gap: 12px;
            flex-shrink: 0;  /* 防止按钮被压缩 */
        }
    }
}

.tab-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px 20px;

    :deep(.t-tabs) {
        flex: 1;
    }
}
</style> 
