<template>
  <div>
    <t-menu :defaultExpanded="expanded" :value="activeMenu" theme="light" :expand-mutex="true" default-value="list"
      style="margin-right: 50px" :collapsed="isCollapsed" height="calc(100vh - 40px)">
      <t-submenu v-for="item in menuList" :key="item.value" :value="item.value" :title="item.title">
        <template #icon>
          <t-icon :name="item.icon" />
        </template>
        <t-menu-item v-for="chlid in item.children" :key="chlid.value" :value="chlid.value" :to="chlid.url">
          <span>{{ chlid.title }}</span>
        </t-menu-item>
      </t-submenu>
    </t-menu>
  </div>
</template>

<script setup>
import { useRoute } from 'vue-router'
const route = useRoute()
const props = defineProps({
  isCollapsed: {
    type: Boolean,
    default: false,
  },
})
const menuList = ref([
  {
    title: '教材管理',
    value: 'book',
    icon: 'management',
    children: [
      {
        title: '教材列表',
        value: 'list',
        url: '/pages/list',
      },
      {
        title: '任务中心',
        value: 'taskCenter',
        url: '/pages/taskCenter',
      },
      {
        title: '章节审核',
        value: 'chapter',
        url: '/pages/chapter',
      },
      {
        title: '待办中心',
        value: 'backup',
        url: '/pages/backup',
      },
    ],
  },
  {
    title: '资源库管理',
    value: 'resourceLibrary',
    icon: 'resources',
    children: [
      {
        title: '我的资源',
        value: 'myResource',
        url: '/resourceLibrary/myResource',
      },
      {
        title: '心理健康管理',
        value: 'myPsychologyHealth',
        url: '/resourceLibrary/myPsychologyHealth',
      },
      {
        title: '个人题库',
        value: 'userQuestion',
        url: '/resourceLibrary/userQuestion',
      },
      {
        title: '教材资源',
        value: 'textbook',
        url: '/resourceLibrary/textbook',
      },
      {
        title: '试卷与作业',
        value: 'paper',
        url: '/resourceLibrary/paper',
      },
    ],
  },
  {
    title: '消息中心',
    value: 'message',
    icon: 'message',
    children: [
      {
        title: '我的消息',
        value: 'myMessage',
        url: '/message/myMessage',
      },
    ],
  },
])

const activeMenu = computed(() => {
  const { meta } = route
  return meta.activeMenu || 'list'
})

const expanded = computed(() => {
  const { meta } = route
  return meta.expanded || 'book'
})
</script>

<style lang="less" scoped>
.t-icon-management {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
  background: url('http://dutp-test.oss-cn-beijing.aliyuncs.com/management.svg') no-repeat center center;
}

.t-icon-message {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
  background: url('https://dutp-test.oss-cn-beijing.aliyuncs.com/1743392471359.svg') no-repeat center center;
}

.t-icon-resources {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
  background: url('https://dutp-test.oss-cn-beijing.aliyuncs.com/1743392704857.svg') no-repeat center center;
}

.t-default-menu {
  background: #fafafa;
}
</style>
