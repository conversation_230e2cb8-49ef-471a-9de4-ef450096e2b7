<template>
  <div>
    <div>
      <t-breadcrumb :max-item-width="'150'">
        <t-breadcrumb-item>资源库管理</t-breadcrumb-item>
        <t-breadcrumb-item to="/resourceLibrary/myPsychologyHealth">心理健康管理
        </t-breadcrumb-item>
      </t-breadcrumb>
    </div>

    <div class="main">
      <t-card>
        <div class="main-form">
          <div class="main-form-query">
            <t-form
              ref="formRef"
              :data="queryParams"
              label-width="80px"
              label-align="right"
              layout="inline"
            >
              <t-form-item label="量表名称：">
                <t-input
                  v-model="queryParams.scaleName"
                  placeholder="请输入量表名称"
                  style="width: 200px"
                  :maxcharacter="20"
                />
              </t-form-item>
              <t-form-item label="状态：">
                <t-select
                  v-model="queryParams.status"
                  placeholder="请选择文件类型"
                  style="width: 200px"
                >
                  <t-option
                    v-for="type in fileTypes"
                    :key="type.value"
                    :value="type.value"
                    :label="type.label"
                    clearable
                  >
                    {{ type.label }}
                  </t-option>
                </t-select>
                <t-button
                  theme="primary"
                  class="btn"
                  @click="getList"
                >
                  <template #icon>
                    <SearchIcon style="color: white" />
                  </template>
                  搜索
                </t-button>
                <t-button
                  theme="primary"
                  class="btn"
                  @click="handleQuery"
                >
                  <template #icon>
                    <RefreshIcon style="color: white" />
                  </template>
                  重置
                </t-button>
              </t-form-item>
            </t-form>
          </div>
        </div>
        <div class="main-form">
          <div class="main-form-query">
            <t-checkbox key="all" @change="handleSelectAll" label="全选" />
            <t-button
              theme="primary"
              class="btn"
              @click="handleAdd"
            >
              <template #icon>
                <PlusIcon style="color: white" />
              </template>
              添加测试量表
            </t-button>
            <t-button
              theme="primary"
              class="btn"
              @click="handleImport"
            >
              <template #icon>
                <Download1Icon style="color: white" />
              </template>
              导入量表
            </t-button>
            <t-button
              theme="primary"
              class="btn"
              @click="handleDelete(null)"
            >
              <template #icon>
                <DeleteIcon style="color: white" />
              </template>
              批量删除量表
            </t-button>
          </div>
        </div>
      </t-card>

      <div class="test-list">
        <div
          class="book-grid"
          v-if="scaleList && scaleList.length > 0"
        >
          <div
            class="book-item"
            v-for="scale in scaleList"
            :key="scale.scaleId"
            @click="handleBookSelect(scale)"
          >
            <t-card
              class="book-card"
              :body-style="{ cursor: 'pointer' }"
            >
              <!-- 右上角的图片 -->
              <div class="card-header-img" v-if="scale.status == 1">
                <img src="@/assets/images/enable-psy.png" alt="Icon" class="image-icon" />
              </div>

              <div class="card-header-img" v-else>
                <img src="@/assets/images/unenable-psy.png" alt="Icon" class="image-icon" />
              </div>

              <!--  查看结果  -->
              <div class="flex-container">
                <div class="left-section">
                  <t-checkbox v-model="scale.checked" :disabled="scale.isDelFlag != 1"></t-checkbox>
                  <label
                    for="checkbox"
                    class="text"
                  >
                    <t-tooltip
                      :content="scale.scaleName"
                      placement="top"
                    >
                      <label
                        for="checkbox"
                        class="text-ellipsis"
                      >{{ displayText(scale.scaleName, 20) }}</label>
                    </t-tooltip>
                  </label>
                  <img  style="cursor: pointer;margin-left: 10px"
                        class="image"
                        @click="handleQrcode(scale)"
                        src="@/assets/images/qrcode.png" title="查看二维码"
                  />
                  <t-button
                    theme="primary"
                    shape="round" variant="outline" size="small"
                    style="margin-left: 10px"
                    @click="copyScale(scale)"
                  >
                    <template #icon>
                      <CopyIcon />
                    </template>
                    复制
                  </t-button>
                </div>
                <img  style="cursor: pointer;"
                  class="image"
                  @click="handleDetail(scale)"
                  src="@/assets/images/psyresult.png" title="查看"
                />
              </div>

              <!--  编辑  -->
              <div class="flex-container">
                <div class="left-section">
                  <t-tooltip
                    :content="scale.bookName"
                    placement="top"
                  >
                    <label
                      for="checkbox"
                      class="text-ellipsis"
                    >{{ displayText("引用教材：" + (scale.bookName?scale.bookName:"--"), 25) }}</label>
                  </t-tooltip>
                </div>
                <div
                  style="margin-top: 20px;cursor: pointer;"
                  @click="handleEdit(scale.scaleId)" v-if="scale.isDelFlag == 1"
                >
                  <img
                    class="image"
                    src="@/assets/images/editpsy.png" title="编辑"
                  />
                </div>

                <div style="margin-top: 20px;cursor: pointer;" v-else>
<!--                  <img-->
<!--                    class="image"-->
<!--                    src="@/assets/images/uneditpsy.png" title="不可编辑"-->
<!--                  />-->
                  <img
                    class="image"
                    src="@/assets/images/editpsy.png" @click="showDetail(scale.scaleId)" title="查看"
                  />
                </div>
              </div>

              <!--  删除  -->
              <div class="flex-container">
                <div
                  class="left-section"
                  style="margin-top: 25px;margin-left: -25px;width: 98%"
                >
                  <div
                    class="second-row"
                    style=""
                  >
                    <label
                      for="checkbox"
                      class="text-count"
                    >填报量表个数：{{scale.testCount}} 份</label>
                  </div>
                  <div class="second-row">
                    <label
                      for="checkbox"
                      class="text-count"
                    >创建时间：{{scale.createTime}}</label>
                  </div>
                </div>
                <div
                  style="margin-top: 15px;align-items: flex-start;cursor: pointer;"
                  @click="handleDelete(scale.scaleId)" v-if="scale.isDelFlag == 1"
                >
                  <img
                    class="image"
                    src="@/assets/images/delete-psy.png" title="删除"
                  />
                </div>

                <div
                  style="margin-top: 15px;align-items: flex-start;cursor: pointer;" v-else
                >
                  <img
                    class="image"
                    src="@/assets/images/undelete-psy.png" title="不可删除"
                  />
                </div>
              </div>
            </t-card>
          </div>
        </div>
        <div v-else>
          <t-empty />
        </div>
      </div>
    </div>

    <!--  新增/修改弹窗  -->
    <t-dialog
      v-model:visible="psychologyVisible"
      top="20px"
      :onConfirm="handleSubmitForm"
      :onCancel="Cancel"
      :header="psyTitle"
      attach="body"
      destroy-on-close
      width="80%"
    >
      <detail
        v-model:add-form="addForm"
        :visible="psychologyVisible"
        :importFlag="importFlag"
        @refresh-list="getList"
        @submit-success="handleSubmitSuccess"
        :disabled="disabled"
        ref="refDetail"
      ></detail>
    </t-dialog>

    <!--  查看弹窗  -->
    <t-dialog
      v-model:visible="psychologyDetailVisible"
      :header="psyDetailTitle"
      attach="body"
      destroy-on-close
      width="80%"
      :cancel-btn="null"
      :confirm-btn="null"
    >
      <preview
        v-model:add-form="addForm"
        :visible="psychologyDetailVisible"
        :importFlag="importFlag"
        ref="refDetail"
      ></preview>
    </t-dialog>

    <!--  查看结果弹窗  -->
    <t-dialog
      v-model:visible="resultVisible"
      header="查看结果"
      attach="body"
      destroy-on-close
      width="900px"
      :footer="null"
    >
      <result
        v-if="scale"
        :scale="scale"
      ></result>
    </t-dialog>

    <!--  导入弹窗  -->
    <t-dialog
      v-model:visible="importVisible"
      header="导入量表"
      width="500px"
      :footer="false"
      @closed="cancelCommit"
      @close="cancelCommit"
    >
      <t-form
        ref="importFormRef"
        :model="importForm"
        label-width="100px"
      >
        <div class="template-download mb-10">
          <t-button
            theme="primary"
            variant="text"
            @click="downloadTemplate('matching')"
          >
            <template #icon><t-icon name="download" /></template>
            下载模板
          </t-button>
        </div>

        <t-upload
          :auto-upload="true"
          :files="importList"
          accept=".xlsx,.xls"
          :request-method="handleImportChange"
        >
          <t-button theme="primary">选择文件</t-button>
          <template #tip>
            <p class="t-upload__tip">请上传Excel格式文件，且不超过10MB</p>
          </template>
        </t-upload>
        <!-- 文件列表展示 -->
        <div class="preview-list">
          <t-card
            v-for="(file, index) in importList"
            :key="index"
            class="import-item"
          >
            <div class="import-item-content">
              <div class="import-info">
                <t-icon name="file" />
                <span class="import-name">{{ file.name }}</span>
              </div>
              <t-button
                theme="danger"
                variant="text"
                @click="removeImportFile(index)"
              >
                删除
              </t-button>
              <t-progress
                v-if="file.uploadProgress > 0 && file.uploadProgress < 100"
                :percentage="file.uploadProgress"
                :label="(percent) => `${percent}%`"
                status="success"
              />
            </div>
          </t-card>
        </div>
      </t-form>

      <div class="dialog-footer">
        <t-button
          theme="default"
          @click="cancelCommit"
        >取 消</t-button>
        <t-button
          style="margin-left: 5px"
          theme="primary"
          :disabled="!isAllUploaded(importList)"
          @click="submitImportForm"
        >
          确 定
        </t-button>
      </div>
    </t-dialog>

    <!--  二维码弹窗  -->
    <t-dialog
      v-model:visible="qrCodeVisible"
      header="查看二维码"
      attach="body"
      destroy-on-close
      width="300px"
      :footer="null"
    >
      <div style="text-align: center">
        <!-- 显示二维码 -->
        <qrcode-svg
          v-if="qrCodeUrl"
          ref="qrcodeRef"
          :value="qrCodeUrl"
          :size="200"
        />

        <!-- 下载按钮 -->
        <t-button
          theme="primary"
          style="margin-top: 10px"
          @click="handleDownload"
        >
          <template #icon>
            <Download1Icon style="color: white" />
          </template>
          下载二维码
        </t-button>
      </div>
    </t-dialog>

  </div>
</template>
<script setup>
// 查询表单
import {
  CheckIcon,
  DeleteIcon,
  Download1Icon,
  PlusIcon,
  SearchIcon, CopyIcon, RefreshIcon
} from 'tdesign-icons-vue-next'
import Detail from '@/components/template/resourceLibrary/myPsychologyHealth/detail.vue'
import preview from '@/components/template/resourceLibrary/myPsychologyHealth/preview.vue'
import {
  deletePhychologyHealth,
  listPhychologyHealth,
  getPhychology,
  copyPsychologyHealth,
} from '@/api/resource/psychologyHealth.js'
import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next'
import Result from '@/components/template/resourceLibrary/myPsychologyHealth/result.vue'
import * as XLSX from 'xlsx'
import { isHtmlTag } from '@/utils/matchUtil.js'
import { useRoute } from 'vue-router'
import { QrcodeSvg } from 'qrcode.vue'

const data = reactive({
  form: {},
  queryParams: {
    scaleName: null,
    status: null,
  },
})
const importList = ref([])
const { queryParams, form } = toRefs(data)
const route = useRoute()
const disabled = ref(1)
// 文件类型定义null
const fileTypes = [
  { label: '全部', value: null },
  { label: '启用', value: '1' },
  { label: '停用', value: '2' },
]
const addForm = ref({})
const psychologyVisible = ref(false)
const psychologyDetailVisible = ref(false)
const importFlag = ref(1)
const psyTitle = ref(null)
const psyDetailTitle = ref(null)
const resultVisible = ref(false)
const loading = ref(false)
const scaleList = ref([])
const importVisible = ref(false)
const scale = ref({})
const qrcodeRef = ref(null);
const importForm = ref({
  file: null,
  fileList: [],
})
const refDetail = ref(null)
const qrCodeUrl = ref('')
const qrCodeName = ref(null)
const qrCodeVisible = ref(false)

/** 查看测试结果 */
function handleDetail(scaleData) {
  scale.value = { ...scaleData }
  resultVisible.value = true
}

const handleQrcode = (scaleData) => {
  qrCodeVisible.value = true
  qrCodeName.value = scaleData.scaleName
  console.log(scaleData)
  const href = `${import.meta.env.VITE_PSY_READER_PREVIEW_URL + '?scaleId='+ scaleData.scaleId}`;
  // const href = `http://192.168.0.13/psychology?scaleId=1`;
  console.log(href)

  if (href) {
    qrCodeUrl.value = href
  } else {
    alert('请输入有效的链接！')
  }
}

const isAllUploaded = (list) => {
  return (
    list.length > 0 &&
    list.every((file) => !file.uploadProgress || file.uploadProgress === 100)
  )
}

const handleDownload = async () => {
  if (!qrcodeRef.value) return;

  try {
    // 获取SVG元素
    const svgElement = qrcodeRef.value.$el;

    // 将SVG转换为Canvas
    const canvas = await convertSvgToCanvas(svgElement);

    // 触发下载
    triggerDownload(canvas);
  } catch (error) {
    console.error('下载失败:', error);
  }
}

const convertSvgToCanvas = (svgElement) => {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const data = new XMLSerializer().serializeToString(svgElement);
    const img = new Image();

    img.onload = () => {
      canvas.width = img.width;
      canvas.height = img.height;
      ctx.drawImage(img, 0, 0);
      resolve(canvas);
    };

    img.src = 'data:image/svg+xml;base64,' + btoa(unescape(encodeURIComponent(data)));
  });
};

const triggerDownload = (canvas) => {
  const link = document.createElement('a');
  link.download = qrCodeName.value + '.png';
  link.href = canvas.toDataURL('image/png');
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

/** 获取选中的数据 */
const selectedScales = computed(() => {
  return scaleList.value.filter((item) => item.checked)
})

const showDetail = (scaleId) => {
  psychologyDetailVisible.value = true
  psyDetailTitle.value = '查看心理健康量表'
  getPhychology(scaleId).then((res) => {
    if(res.data.scale.questionSort == 1){
      addForm.value = {
        ...res.data.scale,
        status: res.data.scale.status,
        moocPsychologyHealthScaleQuestion: res.data.questionList
      }
    }else{
      addForm.value = {
        ...res.data.scale,
        status: res.data.scale.status ,
        moocPsychologyHealthScaleFacet:res.data.questionList,
      }
    }

  })
}

/** 添加下载模板方法 */
function downloadTemplate(type) {
  const templateName = '心理健康题模板.xlsx'
  window.location.href = `/files/${templateName}`
}

/** 初始化 */
onMounted(() => {
  if (route.query.scaleId) {
    queryParams.value.scaleId = route.query.scaleId
    queryParams.value.scaleName = route.query.scaleName
  }
  getList()
})

/** 获取列表数据 */
async function getList() {
  loading.value = true
  const response = await listPhychologyHealth({
    ...queryParams.value,
  })
  loading.value = false
  scaleList.value = response.data
  scaleList.value.forEach((item) => {
    item.checked = false
  })
}

/**监听成功之后 */
function handleSubmitSuccess() {
  psychologyVisible.value = false;
  if (importFlag.value == 2) {
    importVisible.value = false
  }
  handleQuery()
}

function cancelCommit(){
  importVisible.value = false
  importList.value = []
}

async function submitImportForm() {
  if (importList.value.length === 0) {
    MessagePlugin.warning('请选择要导入的文件')
    return
  }
  try {
    const allQuestionData = [] // 收集所有题目数据
    for (const file of importList.value) {
      // 读取Excel文件内容
      const workbook = await readExcelFile(file.raw)
      const worksheet = workbook.Sheets[workbook.SheetNames[0]]
      const rows = XLSX.utils.sheet_to_json(worksheet)
      console.log(rows)
      for (let i = 0; i < rows.length; i++) {
        const row = rows[i]
        console.log(!row['题目内容'])
        console.log(!row['选项1'])
        if (!row['题目内容'] || !row['选项1']) {
          console.warn('跳过无效行:', row)
          continue
        }
        const quesId = generateRandom13DigitNumber()
        const opId = generateRandom13DigitNumber()
        const questionData = {
          questionContent: row['题目内容'] || '',
          sort: row['题目序号'] || '',
          moocPsychologyHealthScaleQuestionOption: [],
          questionId: quesId,
        }
        for (let j = 1; j <= 5; j++) {
          const optionContent = row[`选项${j}`]
          if (optionContent) {
            questionData.moocPsychologyHealthScaleQuestionOption.push({
              optionContent: optionContent,
              optionIndex: j - 1,
              optionId:opId,
              questionId: quesId,
            })
          }
        }
        allQuestionData.push(questionData)
      }
    }
    psyTitle.value = '创建心理健康量表'
    addForm.value = {
      scaleName: '',
      scanQuestion: '',
      evaluationMethod: '',
      evaluateReference: '',
      scaleType: 1,
      questionSort: 1,
      showSortType: 1,
      status: 1,
      moocPsychologyHealthScaleQuestion:allQuestionData
    }
    psychologyVisible.value = true
    importVisible.value = false
    importFlag.value = 2
  } catch (error) {
    console.error('导入失败:', error)
  }
}

function generateRandom13DigitNumber() {
  return Math.floor(Math.random() * 9e12 + 1e12);
}

/** 重置 */
function handleQuery() {
  queryParams.value = {
    scaleName: null,
    status: null,
    scaleId: null,
  }
  getList()
}

function handleImportChange(file) {
  return new Promise((resolve, reject) => {
    // 检查文件类型
    const isExcel =
      file.raw.type ===
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
      file.raw.type === 'application/vnd.ms-excel'
    if (!isExcel) {
      MessagePlugin.error('只能上传Excel文件！')
      //proxy.$modal.msgError('只能上传Excel文件！')
      reject(new Error('只能上传Excel文件！'))
      return
    }
    // 检查文件大小
    const isLt10M = file.raw.size / 1024 / 1024 < 10
    if (!isLt10M) {
      MessagePlugin.error('文件大小不能超过 10MB!')
      //proxy.$modal.msgError('文件大小不能超过 10MB!')
      reject(new Error('文件大小不能超过 10MB!'))
      return
    }

    file.uploadProgress = 0
    importList.value.push(file)

    // 模拟上传进度
    let progress = 0
    const progressInterval = setInterval(() => {
      progress += 10
      file.uploadProgress = Math.min(progress, 100)

      if (progress >= 100) {
        clearInterval(progressInterval)
        resolve({ status: 'success', response: { data: file } })
      }
    }, 300)
  })
}

// 添加读取Excel文件的辅助函数
const readExcelFile = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target.result)
        const workbook = XLSX.read(data, { type: 'array' })
        resolve(workbook)
      } catch (error) {
        reject(error)
      }
    }
    reader.onerror = (error) => reject(error)
    reader.readAsArrayBuffer(file)
  })
}

const removeImportFile = (index) => {
  importList.value.splice(index, 1)
}

/** 删除列表数据 */
function handleDelete(scaleId) {
  if (!scaleId) {
    if (selectedScales.value.map((item) => item.scaleId)?.length === 0) {
      MessagePlugin.error('请先选择要删除的数据！')
      return
    }
  }
  const _scaleIds = scaleId || selectedScales.value.map((item) => item.scaleId)
  const confirmDia = DialogPlugin.confirm({
    header: '删除量表',
    body: '确定要删除量表？',
    theme: 'warning',
    onCancel: () => {
      confirmDia.destroy()
    },
    onClose: () => {
      confirmDia.destroy()
    },
    onConfirm() {
      deletePhychologyHealth(_scaleIds)
        .then((res) => {
          confirmDia.hide()
          MessagePlugin.success('删除成功')
          setTimeout(() => {
            getList()
          }, 500)
        })
        .catch((err) => {
          confirmDia.hide()
          // MessagePlugin.error('删除失败')
        })
    },
  })
}

/** 修改数据 */
function handleEdit(scaleId) {
  psychologyVisible.value = true
  psyTitle.value = '编辑心理健康量表'
  disabled.value = 2
  getPhychology(scaleId).then((res) => {
    console.log(res);
    if(res.data.scale.questionSort == 1){
      addForm.value = {
        ...res.data.scale,
        status: res.data.scale.status,
        moocPsychologyHealthScaleQuestion: res.data.questionList
      }
    console.log(addForm.value)
    }else{
      addForm.value = {
        ...res.data.scale,
        status: res.data.scale.status ,
        moocPsychologyHealthScaleFacet:res.data.questionList,
      }
    }

  })
}

function displayText(str, length) {
  return str.length > length ? str.substring(0, length) + '...' : str
}

/** 新增数据 */
function handleAdd() {
  addForm.value = {
    scaleName: '',
    scanQuestion: '',
    evaluationMethod: '',
    evaluateReference: '',
    scaleType: 1,
    questionSort: 1,
    showSortType: 1,
    status: 1,
    showSumScore:1,
  }
  disabled.value = 1
  psychologyVisible.value = true
  psyTitle.value = '创建心理健康量表'
}

/** 导入数据 */
function handleImport() {
  importVisible.value = true
}

function tagColor(status) {
  return status == 1 ? '#8BC34A' : 'red'
}

/** 全选 */
function handleSelectAll() {
  const allSelected = scaleList.value.filter((item) => item.isDelFlag == 1)
    .every((item) => item.checked);

  // 对 delflag 为 0 的数据进行全选或反选
  scaleList.value.forEach((item) => {
    if (item.isDelFlag == 1) {
      // 如果所有 delflag 为 0 的项已被选中，则执行反选，否则全选
      item.checked = !allSelected;
    }
  });
}

/** 处理量表选择 */
function handleBookSelect(scale) {
  scale.selected = !scale.selected
}

/** 提交表单 */
function handleSubmitForm() {
  refDetail.value?.submitForm()
}

/**复制 */
function copyScale(scale){
  copyPsychologyHealth(scale).then(res =>{
    MessagePlugin.success('复制成功'),
    getList()
    }
  )
}
</script>


<style scoped lang="less">
.main {
  .main-form {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 20px 0;

    .main-form-query {
      .btn {
        margin-left: 20px;
      }
    }
  }
}

.test-list {
  padding: 10px 0;
  justify-content: space-between;
}

.card {
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-content {
  display: none;
  position: absolute;
  right: 0;
  background-color: #f9f9f9;
  min-width: 100px;
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
  z-index: 1;
}

.book-grid {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  justify-content: flex-start;
  gap: 20px; /* 控制项目之间的间距 */
  padding: 20px 0; /* 容器的内边距 */
}

.dropdown-content a {
  color: black;
  padding: 12px 16px;
  text-decoration: none;
  display: block;
}

.dropdown-content a:hover {
  background-color: #f1f1f1;
}

.dropdown:hover.dropdown-content {
  display: block;
}

.book-item {
  width: calc(33% - 10px); /* 假设一行3个，减去gap的影响 */
  box-sizing: border-box;
}

.book-card {
  width: 100%;
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

// 响应式调整，例如小屏幕下一行显示一个或两个
@media (max-width: 1200px) {
  .book-item {
    width: calc(50% - 20px); // 一行2个
  }
}

@media (max-width: 768px) {
  .book-item {
    width: 100%; // 一行1个
  }
}

.flex-container {
  display: flex; /* 使用 Flexbox 布局 */
  justify-content: space-between; /* 左右两侧元素分开 */
  align-items: center; /* 垂直居中对齐 */
}

.left-section {
  display: flex; /* 左侧部分使用 Flexbox 布局 */
  align-items: center; /* 垂直居中对齐 */
}

.checkbox {
  margin-right: 10px; /* 多选框与文本之间的间距 */
}

.text {
  margin-right: 10px; /* 文本与按钮之间的间距 */
  font-size: 18px;
  font-weight: normal;
  color: rgba(0, 0, 0, 0.85);
  line-height: 18px;
  font-style: normal;
  text-transform: none;
}

.text-book {
  font-weight: normal;
  font-size: 16px;
  color: #585e76;
  margin-left: 25px;
  line-height: 16px;
  font-style: normal;
  text-transform: none;
}

.text-count {
  background: #f2f5fa;
  font-weight: normal;
  font-size: 15px;
  color: #585e76;
  line-height: 16px;
  font-style: normal;
  text-transform: none;
}

.second-row {
  background-color: #f2f5fa;
  margin-left: 25px;
  margin-top: -15px;
  padding: 8px;
}

.button:hover {
  background-color: #0056b3; /* 鼠标悬停时按钮背景颜色 */
}

.image {
  width: 32px;
  height: auto;
  text-align: center;
}

.text-enabled {
  color: #3ab288;
}

.text-disabled {
  color: red;
}

.mb-10 {
  margin-bottom: 10px;
}

.template-download {
  display: flex;
  gap: 20px;
  justify-content: center;
  margin-bottom: 20px;
  padding: 10px;
  border-bottom: 1px solid #ebeef5;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.import-item {
  margin-bottom: 10px;
}
.import-item-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px;
}

.import-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.t-icon {
  font-size: 20px;
  color: #909399;
}

.import-name {
  color: #606266;
}

.card-header-img {
  position: absolute;
  top: -17px;
  right: -12px;
}

.image-icon {
  width: 50px;
  height: 50px;
}
</style>
