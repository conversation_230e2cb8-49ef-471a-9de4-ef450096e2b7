<template>
  <div>
    <!-- 搜索表单 -->
    <t-form
      v-show="showSearch"
      ref="queryRef"
      :data="searchForm"
      layout="inline"
      label-width="0"
      style="margin-bottom: 20px"
    >
      <t-form-item>
        <t-input
          v-model="searchForm.catalogName"
          placeholder="请输入目录名称"
          clearable
          @keyup-enter="handleSearch"
          @input="syncSearchNames"
        />
      </t-form-item>

      <t-button theme="primary" @click="handleSearch">
        <template #icon><t-icon name="search" /></template>
        搜索
      </t-button>
    </t-form>

    <!-- 目录树 -->
    <div class="folder-header">
      <t-text strong>文件夹</t-text>
      <t-dropdown :options="getRootFolderOptions()" trigger="click">
        <t-button variant="text" shape="square" class="more-btn">
          <t-icon name="more" />
        </t-button>
      </t-dropdown>
    </div>
    <t-tree
      style="max-width: 600px"
      :data="processedCatalogs"
      :keys="{
        children: 'children',
        label: 'catalogName',
        value: 'catalogId',
      }"
      draggable
      expand-all
      :allow-drop="allowDrop"
      :allow-drag="allowDrag"
      hover
      activable
      @drag-start="handleDragStart"
      @drag-enter="handleDragEnter"
      @drag-leave="handleDragLeave"
      @drag-over="handleDragOver"
      @drag-end="handleDragEnd"
      @drop="handleDrop"
      @click="(context) => $emit('click-catalog', context.node)"
    >
      <template #operations="{ node }">
        <t-dropdown :options="getDropdownOptions(node)" trigger="click">
          <t-button variant="text" shape="square">
            <t-icon name="more" />
          </t-button>
        </t-dropdown>
      </template>
    </t-tree>

    <!-- 添加/编辑目录对话框 -->
    <t-dialog
      :visible="catalogOpen"
      :title="catalogTitle"
      width="500"
      @close="cancel"
    >
      <t-form ref="catalogRef" :data="catalogForm" :rules="rules">
        <t-form-item label="目录名称：">
          <t-input
            v-model="catalogForm.catalogName"
            placeholder="请输入目录名称"
          />
        </t-form-item>
      </t-form>
      <template #footer>
        <t-button theme="primary" @click="catalogSubmitForm">确 定</t-button>
        <t-button theme="default" @click="cancel">取 消</t-button>
      </template>
    </t-dialog>

    <!-- 移动目录对话框 -->
    <t-dialog :visible="catalogMoveOpen" :title="catalogTitle" @close="cancel">
      <t-form ref="catalogRef" :data="catalogForm" :rules="rules">
        <t-form-item label="目标位置">
          <t-tree-select
            v-model="catalogForm.moveParentIds"
            :data="[{ catalogId: '0', catalogName: '根目录', children: processedCatalogs }]"
            :keys="{
              value: 'catalogId',
              label: 'catalogName',
              children: 'children',
            }"
            clearable
            @change="changeCatalogParentId"
          />
        </t-form-item>
      </t-form>
      <template #footer>
        <t-button theme="primary" @click="catalogSubmitForm">确 定</t-button>
        <t-button
          theme="default"
          @click="
            () => {
              cancel()
              $emit('refresh-catalogs')
            }
          "
          >取 消</t-button
        >
      </template>
    </t-dialog>
  </div>
</template>

<script setup>
import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next'
import { computed, getCurrentInstance, ref } from 'vue'

const { proxy } = getCurrentInstance()

// Props定义
const props = defineProps({
  catalogs: {
    type: Array,
    required: true,
  },
  showSearch: {
    type: Boolean,
    default: true,
  },
  allowDrop: {
    type: Function,
    default: null,
  },
  allowDrag: {
    type: Function,
    default: null,
  },
})

// Emits定义
const emit = defineEmits([
  'search',
  'click-catalog',
  'add-catalog',
  'delete-catalog',
  'update-catalog',
  'move-catalog',
  'node-drop',
  'node-drag-start',
  'node-drag-enter',
  'node-drag-leave',
  'node-drag-over',
  'node-drag-end',
  'refresh-catalogs',
])

// 内部状态
const searchForm = ref({
  catalogName: null,
  folderName: null,
})

// 处理数据转换
const processedCatalogs = computed(() => {
  if (!props.catalogs) return []

  const processCatalog = (item) => {
    const processed = {
      catalogId: item.catalogId || item.folderId,
      catalogName: item.catalogName || item.folderName,
      children: item.children ? item.children.map(processCatalog) : [],
    }
    return processed
  }

  return props.catalogs.map(processCatalog)
})

// 拖拽相关方法
const allowDrop = (draggingNode, dropNode, type) => {
  return props.allowDrop ? props.allowDrop(draggingNode, dropNode, type) : true
}

const allowDrag = (draggingNode) => {
  return props.allowDrag ? props.allowDrag(draggingNode) : true
}

const handleDragStart = (node, ev) => {
  emit('node-drag-start', { node, ev })
}

const handleDragEnter = (draggingNode, dropNode, ev) => {
  emit('node-drag-enter', { draggingNode, dropNode, ev })
}

const handleDragLeave = (draggingNode, dropNode, ev) => {
  emit('node-drag-leave', { draggingNode, dropNode, ev })
}

const handleDragOver = (draggingNode, dropNode, ev) => {
  emit('node-drag-over', { draggingNode, dropNode, ev })
}

const handleDragEnd = (draggingNode, dropNode, dropType, ev) => {
  emit('node-drag-end', { draggingNode, dropNode, dropType, ev })
}
const handleDrop = (draggingNode, ev) => {
  const dropNode = draggingNode.dropNode
  draggingNode = draggingNode.dragNode

  const moveData = {
    catalogId: draggingNode.data.catalogId || draggingNode.data.folderId,
    folderId: draggingNode.data.folderId || draggingNode.data.catalogId,
    catalogName: draggingNode.data.catalogName || draggingNode.data.folderName,
    folderName: draggingNode.data.folderName || draggingNode.data.catalogName,
    parentId: dropNode.data.catalogId || dropNode.data.folderId || 0,
    parentFolderId: dropNode.data.folderId || dropNode.data.catalogId || 0,
    moveParentIds: dropNode.data.catalogId || dropNode.data.folderId || 0,
  }

  // 检查是否试图将节点移动到自身
  if (moveData.catalogId === moveData.parentId) {
    showError('不能把目录移动到自己下面')
    return
  }

  // 设置表单数据并打开移动弹窗
  catalogForm.value = { ...moveData }
  catalogTitle.value = '移动目录'
  catalogMoveOpen.value = true

  // 阻止默认的拖拽行为
  if (ev) {
    ev.preventDefault()
    ev.stopPropagation()
  }
}

// 新增状态变量
const catalogOpen = ref(false)
const catalogMoveOpen = ref(false)
const catalogTitle = ref('')
const catalogForm = ref({})

// 取消按钮
function cancel() {
  catalogOpen.value = false
  catalogMoveOpen.value = false
  catalogForm.value = {}
  // 刷新目录列表，恢复原始状态
  emit('refresh-catalogs')
}

// 修改处理目录移动的方法
function changeCatalogParentId() {
  // moveParentIds 现在直接就是选中的目录ID
  catalogForm.value.parentId = catalogForm.value.moveParentIds
}

// 修改验证规则，只保留 catalogName
const rules = {
  catalogName: [
    { required: true, message: '目录名称不能为空', trigger: 'blur' },
    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' },
  ],
}

// 添加处理新增目录的方法
const handleAddCatalog = (data) => {
  catalogForm.value = {
    parentId: data.data.catalogId || data.data.folderId || 0,
    catalogName: '',
    folderName: '',
  }
  catalogTitle.value = '新增目录'
  catalogOpen.value = true
}

// 添加处理根目录新增的方法
const handleAddRootCatalog = () => {
  catalogForm.value = {
    parentId: 0, // 确保 parentId 为 0
    catalogName: '',
    folderName: '',
  }
  catalogTitle.value = '新增根目录'
  catalogOpen.value = true
}

// 添加处理重命名的方法
const handleUpdateCatalog = (data) => {
  data=data.data
  catalogForm.value = {
    catalogId: data.catalogId || data.folderId,
    folderId: data.folderId || data.catalogId,
    catalogName: data.catalogName || data.folderName,
    folderName: data.folderName || data.catalogName,
  }
  catalogTitle.value = '重命名目录'
  catalogOpen.value = true
}

// 添加处理移动的方法
const handleMoveCatalog = (data) => {
  data=data.data
  catalogForm.value = {
    catalogId: data.catalogId || data.folderId,
    folderId: data.folderId || data.catalogId,
    catalogName: data.catalogName || data.folderName,
    folderName: data.folderName || data.catalogName,
    moveParentIds: [],
    parentId: null,
  }
  catalogTitle.value = '移动目录'
  catalogMoveOpen.value = true
}

// 修改处理删除的方法
const handleDeleteCatalog = (node) => {
  if (!node?.data) {
    MessagePlugin.error('无效的目录数据')
    return
  }
  
  const data = node.data
  const dialogInstance = DialogPlugin.confirm({
    header: '确认删除',
    body: `是否确认删除目录"${data.catalogName || data.folderName}"？`,
    confirmBtn: '确认',
    cancelBtn: '取消',
    onConfirm: () => {
      emit('delete-catalog', {
        catalogId: data.catalogId || data.folderId,
        folderId: data.folderId || data.catalogId,
      })
      dialogInstance.destroy()
    },
    onClose: () => {
      dialogInstance.destroy()
    }
  })
}

// 添加下拉菜单选项方法
const getDropdownOptions = (node) => {
  return [
    { content: '新建子目录', value: 1, onClick: () => handleAddCatalog(node) },
    { content: '删除', value: 2, onClick: () => handleDeleteCatalog(node) },
    { content: '重命名', value: 3, onClick: () => handleUpdateCatalog(node) },
    { content: '移动', value: 4, onClick: () => handleMoveCatalog(node) },
  ]
}

// 添加根文件夹下拉菜单选项方法
const getRootFolderOptions = () => {
  return [
    { content: '新增子目录', value: 1, onClick: () => handleAddRootCatalog() },
  ]
}

// 修改错误提示方法
const showError = (message) => {
  MessagePlugin.error(message)
}

// 修改提交表单方法
const catalogSubmitForm = async () => {
  if (!proxy.$refs.catalogRef) {
    showError('表单引用获取失败')
    return
  }

  try {
    const valid = await proxy.$refs.catalogRef.validate()
    if (valid) {
      const formData = { ...catalogForm.value }

      // 确保名称字段同步
      if (formData.catalogName) {
        formData.folderName = formData.catalogName
      }

      // 处理移动操作
      if (catalogMoveOpen.value) {
        if (formData.catalogId === formData.parentId) {
          showError('不能把目录移动到自己下面')
          return
        }
        emit('move-catalog', formData)
      }
      // 处理新增或更新操作
      else {
        if (formData.catalogId != null) {
          emit('update-catalog', formData)
        } else {
          emit('add-catalog', formData)
        }
      }

      // 关闭弹窗
      catalogOpen.value = false
      catalogMoveOpen.value = false
      // 重置表单
      catalogForm.value = {}
    }
  } catch (error) {
    console.error('表单验证失败:', error)
    showError('表单验证失败，请检查输入')
  }
}

// 添加同步搜索名称的方法
const syncSearchNames = (value) => {
  searchForm.value.folderName = value
}

// 修改搜索处理方法
const handleSearch = () => {
  emit('search', {
    ...searchForm.value,
    folderName: searchForm.value.catalogName,
  })
}
</script>

<style scoped>
.custom-tree-node {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  width: 100%;
}

.folder-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.folder-header .more-btn {
  padding: 4px 8px;
}

.folder-header .more-btn:hover {
  background-color: #f5f7fa;
  border-radius: 4px;
}
</style>
