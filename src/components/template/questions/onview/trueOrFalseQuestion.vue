<template>
  <div class="data-item">
    <div class="data-item-top" v-if="localConfig?.questionTypeShow == 1">
      <slot name="selection" /><b>判断题</b>
    </div>
    <div class="data-item-em">

      <div class="data-item-remark"
        v-if="data.questionRemark && data.questionRemark != '<p><br></p>' && data.questionRemark != test">
        <div v-html="data.questionRemark"></div>
      </div>
      <div class="data-item-qtitle">
        <label v-if="props.config?.contentLabel"><b>题干：</b></label>
        <div v-html="data.questionContent">
        </div>
      </div>

    </div>
    <ul v-if="data.options?.length > 0" class="data-item-option count sorting">
      <li v-for="(optItem, index) in data.options" :key="index" class="option-item" :class="{
        'is-right-answer': localConfig.hasAnswer && optItem.rightFlag === 1,
      }">
        <!-- <div class="option-item-content" style="margin-right: 40px" v-html="optItem.optionContent">
        </div> -->
        <div class="option-item-content-trueoffalse">
          <OptionContentTemplate :optionContent="optItem.optionContent" />
        </div>

      </li>
    </ul>
    <div v-if="localConfig.hasAnswer" class="data-item-analysis">
      <label><b>解析：</b></label>
      <div v-html="data.analysis" style="margin-left: 10px;">
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import OptionContentTemplate from './optionContentTemplate.vue';
const props = defineProps({
  data: {
    type: Object,
    required: true,
  },
  config: {
    type: Object,
    // 引入showAnswer配置是因为的该组件也用在编辑器中，试题正确答案以及解析不需要打印出来。
    default: { contentLabel: false, hasAnswer: true },
  },
})
const defaultCfg = {
  contentLabel: false,
  hasAnswer: true,
  questionTypeShow: true,
}
let localConfig = $ref({
  ...defaultCfg,
  ...props.config,
})

const test = ref('<p style="line-height: 2;"><br></p>')
watch(
  () => props.config,
  (nCfg) => {
    if (nCfg) {
      console.log(nCfg)
      localConfig = {
        ...defaultCfg,
        ...nCfg,
      }
    }
  },
  {
    deep: true,
  }
)
</script>
<style lang="less" scoped>
@import url('./question-data-item-style.less');
</style>
