<template>
  <div class="data-item">
    <div v-if="localConfig?.questionTypeShow == 1" class="data-item-top">
      <slot name="selection" /><b>编程题</b>
    </div>
    <div class="data-item-em">
      <div v-if="data.questionRemark && data.questionRemark != '<p><br></p>'">
        <div v-html="data.questionRemark"></div>
      </div>
      <label v-if="props.config?.contentLabel"><b>题干：</b></label>
      <div v-html="revertUserQuestionContent(data)">
      </div>
    </div>
    <div v-if="localConfig.hasAnswer" class="answer-content">
      <div class="code-content">
        <label><b>语言：</b></label>
        <span v-if="codeContent?.language">{{ codeContent.language }}</span>
      </div>
      <template-resourceLibrary-components-CodeEditor v-model="codeContent.code" :language="codeContent.language"
        :read-only="true" />
      <div class="answer-content">
        <label><b>参考答案：</b></label>
        <div v-html="data.rightAnswer">
        </div>
      </div>
      <div class="data-item-analysis">
        <label><b>解析：</b></label>
        <div style="margin-left: 10px;" v-html="data.analysis">
        </div>
      </div>
    </div>
    <t-image-viewer :visible="visibleImg" :images="[imgUrl]" @close="closeViewer">
    </t-image-viewer>
  </div>
</template>
<script setup>
import { useImageView } from '@/hooks/imageView'
import { emptyProgrammingSelectionTemplate } from '@/utils/quetions-utils'

import {
  revertUserQuestionContent,
} from './question-content-revert-mixin'
const { visibleImg, imgUrl, closeViewer } = useImageView()
const props = defineProps({
  data: {
    type: Object,
    required: true,
  },
  config: {
    type: Object,
    // 引入showAnswer配置是因为的该组件也用在编辑器中，试题正确答案以及解析不需要打印出来。
    default: { contentLabel: false, hasAnswer: true, questionTypeShow: true },
  },
})
const codeContent = $ref(emptyProgrammingSelectionTemplate)
try {
  const tmpCodeContent = JSON.parse(props.data?.codeContent || '{}')
  Object.assign(codeContent, tmpCodeContent)
} catch (err) { }

const defaultCfg = {
  contentLabel: false,
  hasAnswer: true,
  questionTypeShow: true,
}
let localConfig = $ref({
  ...defaultCfg,
  ...props.config,
})
onMounted(() => { })
watch(
  () => props.config,
  (nCfg) => {
    if (nCfg) {
      localConfig = {
        ...defaultCfg,
        ...nCfg,
      }
    }
  },
  {
    deep: true,
  }
)
</script>
<style lang="less" scoped>
@import url('./question-data-item-style.less');
</style>
