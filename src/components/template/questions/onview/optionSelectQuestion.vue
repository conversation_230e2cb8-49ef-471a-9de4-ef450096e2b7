<template>

  <div class="data-item">
    <div v-if="localConfig?.questionTypeShow == 1" class="data-item-top">

      <slot name="selection" /><b>{{
        isSingleOptionSelectQuetionType(data.questionType) ? '单选题' : '多选题'
      }}</b>
    </div>
    <div class="data-item-em">

      <div v-if="data.questionRemark && data.questionRemark != '<p><br></p>' && data.questionRemark != test">
        <div class="data-item-remark" v-html="data.questionRemark"></div>
      </div>
      <div class="data-item-qtitle">
        <label v-if="props.config?.contentLabel"><b>题干：</b></label>
        <div v-html="data.questionContent"></div>
      </div>

    </div>
    <ul v-if="dataList.length > 0" class="data-item-option count">
      <li v-for="(optItem, index) in dataList" :key="index" class="option-item" :class="{
        'is-right-answer': localConfig.hasAnswer && optItem.rightFlag === 1,
      }">
        <div class="option-item-content-trueoffalse">
          <OptionContentTemplate :optionContent="optItem.optionContent" />
        </div>

        <!-- <div class="option-item-content" v-html="optItem.optionContent"></div> -->
      </li>
    </ul>
    <div v-if="localConfig.hasAnswer" class="data-item-analysis">
      <label><b>解析：</b></label>
      <div style="margin-left: 10px;" v-html="data.analysis"></div>
    </div>
    <t-image-viewer :visible="visibleImg" :images="[imgUrl]" @close="closeViewer">
    </t-image-viewer>
  </div>

</template>
<script setup lang="ts">
import OptionContentTemplate from './optionContentTemplate.vue';
import { useImageView } from '@/hooks/imageView'
import { isSingleOptionSelectQuetionType } from '@/utils/questionTypeUtil'
const { visibleImg, imgUrl, closeViewer } = useImageView()
const props = defineProps({
  data: {
    type: Object,
    required: true,
  },
  config: {
    type: Object,
    // 引入showAnswer配置是因为的该组件也用在编辑器中，试题正确答案以及解析不需要打印出来。
    default: { contentLabel: false, hasAnswer: true },
  },
})

const dataList = ref(props.data.options)

const test = ref('<p style="line-height: 2;"><br></p>')

const defaultCfg = { contentLabel: false, hasAnswer: true, questionTypeShow: '1' }
let localConfig = $ref({
  ...defaultCfg,
  ...props.config,
})


watch(
  () => props.config,
  (nCfg) => {
    if (nCfg) {
      localConfig = {
        ...defaultCfg,
        ...nCfg,
      }

    }
  },
  {
    deep: true,
  },
)

watch(() => props.data, (newData) => {
  console.log('newData', newData)
  if (newData?.options?.length > 0) {
    dataList.value = newData.options

  }
}, {
  deep: true,
})

</script>
<style lang="less">
.option-item-content p {
  display: block !important;
}
</style>
<style lang="less" scoped>
@import url('./question-data-item-style.less');
</style>
