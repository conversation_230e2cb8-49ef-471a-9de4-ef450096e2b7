<template>
  <div class="data-item">
    <div v-if="localConfig?.questionTypeShow == 1" class="data-item-top">
      <slot name="selection" /><b>连线题</b>
    </div>
    <div class="data-item-em">
      <div v-if="data.questionRemark && data.questionRemark != '<p><br></p>' && data.questionRemark != test"
        class="data-item-remark">
        <div v-html="data.questionRemark"></div>
      </div>
      <div class="data-item-qtitle"
        :style="{ fontWeight: !data.questionRemark && data.questionRemark === '<p><br></p>' ? 'blod' : 'normal', borderBottom: !data.questionRemark && data.questionRemark === '<p><br></p>' ? '1px solid #e8eaec' : 'none' }">
        <label v-if="props.config?.contentLabel"><b>题干：</b></label>
        <div v-html="data.questionContent"></div>
      </div>

    </div>

    <!-- 连线题  封面显示顺序  图片>音频>视频 -->
    <div :id="connectionPaintCanvas" class="option-con matching-question">

      <div class="matching-question-left">
        <div v-for="(optionItem, optionIndex) in leftOption" :key="optionIndex">
          <div class="marching-question-item" :style="`height:${config.optionHeight}px;`">
            <OptionContentTemplate :optionContent="optionItem.optionContent" />
          </div>
        </div>
      </div>
      <div class="matching-question-paint">
        <svg v-show="localConfig.hasAnswer" width="100%" height="100%" xmlns="http://www.w3.org/2000/svg"></svg>
      </div>
      <div class="matching-question-right">
        <div v-for="(optionItem, optionIndex) in rightOption" :key="optionIndex">
          <div class="marching-question-item" :style="`height:${config.optionHeight}px;`">
            <OptionContentTemplate :optionContent="optionItem.optionContent" />
          </div>
        </div>
      </div>

    </div>
    <div v-if="localConfig.hasAnswer" class="data-item-analysis">
      <label><b>解析：</b></label>
      <div style="margin-left: 10px;" v-html="data.analysis"></div>
    </div>




  </div>
</template>

<script setup>
import { throttle } from 'lodash'
import { nextTick, onMounted, computed } from 'vue'

import { uuid } from '@/utils/quetions-utils'

import OptionContentTemplate from './optionContentTemplate.vue';

// 业务数据
const connectionPaintCanvas = `c${uuid()}`
const imgUrl = ref('')
const imgArr = ref([])
const videoUrl = ref('')
const audioUrl = ref('')
const visibleImg = ref(false)
const show = ref(false)
const dislogTitle = ref('')

// 关闭弹窗
const handleClose = () => {
  videoUrl.value = '';
  audioUrl.value = '';
  show.value = false;
}
const test = ref('<p style="line-height: 2;"><br></p>')
// 视频打开
const handleVideoClick = (src, type) => {
  console.log(src, type)

  if (type === 'video') {
    videoUrl.value = src;
    dislogTitle.value = '视频'
  } else {
    console.log(src, type)
    audioUrl.value = src;
    dislogTitle.value = '音频'
  }

  show.value = true;
}

const handleClick = (src) => {

  src.forEach(item => {
    console.log(item)
    imgArr.value.push(item.src)
  })

  const getValue = sessionStorage.getItem('v');
  if (getValue === '1') return
  sessionStorage.setItem('v', '1');

  visibleImg.value = true;
};

const closeViewer = () => {
  sessionStorage.removeItem('v');
  imgArr.value = [];
  visibleImg.value = false;
}

// 宏定义
const props = defineProps({
  leftOption: {
    type: Array,
    required: true,
  },
  rightOption: {
    type: Array,
    required: true,
  },
  matchingResult: {
    type: Array,
    required: true,
  },
  data: {
    type: Object,
    required: true,
  },
  config: {
    type: Object,
    // 引入showAnswer配置是因为的该组件也用在编辑器中，试题正确答案以及解析不需要打印出来。
    default: { contentLabel: false, hasAnswer: true },
  },
})
const defaultCfg = { contentLabel: false, hasAnswer: true, questionTypeShow: true }
let localConfig = $ref({
  ...defaultCfg,
  ...props.config,
})

// 方法
function initCanvas() {
  // 画布大小
  const canvasEle = document.querySelector(`#${connectionPaintCanvas} svg`)
  if (!canvasEle) {
    return
  }
  const computedStyle = window.getComputedStyle(canvasEle)
  const width = parseInt(computedStyle.width)
  const height = parseInt(computedStyle.height)
  return {
    height,
    width,
    x: canvasEle.getBoundingClientRect().left,
    y: canvasEle.getBoundingClientRect().top,
  }
}

function getAnchorCenter(index, totalItems, height, width, isLeft = true) {
  // 计算每个选项的高度间隔
  const itemHeight = height / totalItems
  // 计算当前选项的中心 Y 坐标
  const y = itemHeight * index + 6
  // 左侧选项的 X 坐标为 0，右侧选项的 X 坐标为 SVG 的宽度
  const x = isLeft ? 0 : width
  return { x, y }
}

function startDrawing(linePositions) {
  const svg = document.querySelector(`#${connectionPaintCanvas} svg`)
  // 清空之前的线条
  svg.innerHTML = ''

  linePositions.forEach(({ start, end }) => {
    // 创建线条元素
    const line = document.createElementNS('http://www.w3.org/2000/svg', 'line')
    line.setAttribute('x1', start.x)
    line.setAttribute('y1', start.y)
    line.setAttribute('x2', end.x)
    line.setAttribute('y2', end.y)
    line.setAttribute('stroke', '#000') // 线条颜色
    line.setAttribute('stroke-width', '2') // 线条宽度
    svg.appendChild(line)
  })
}

const initPositions = throttle(() => {
  const canvasSize = initCanvas()
  if (!canvasSize) {
    return
  }
  const { height, width } = canvasSize
  const linePositions = []

  // 遍历匹配结果，计算每个匹配项的起点和终点坐标
  props.matchingResult.forEach(({ source, target }) => {
    const start = getAnchorCenter(
      source,
      props.leftOption.length,
      height,
      width,
      true,
    )
    const end = getAnchorCenter(
      target,
      props.rightOption.length,
      height,
      width,
      false,
    )
    linePositions.push({ start, end })
  })

  // 开始绘制线条
  startDrawing(linePositions)
}, 500)

// 监听
onMounted(() => {
  nextTick(() => {
    if (!document.querySelector(`#${connectionPaintCanvas} svg`)) {
      return
    }
    if (
      props.leftOption &&
      props.rightOption &&
      props.leftOption.length &&
      props.rightOption.length &&
      localConfig.hasAnswer
    ) {
      initPositions()
    }
    // 监听 connectionPaintCanvas 的大小变化
    const canvasElement = document.querySelector(`#${connectionPaintCanvas}`)
    if (canvasElement) {
      const resizeObserver = new ResizeObserver(() => {
        // 当大小变化时，重新计算并绘制线条
        initPositions()
      })
      resizeObserver.observe(canvasElement)
    }
  })
})
watch(
  () => props.config,
  (nCfg) => {
    if (nCfg) {
      localConfig = {
        ...defaultCfg,
        ...nCfg,
      }
    }
  },
  {
    deep: true,
  },
)
</script>

<style lang="less" scoped>
@import url('./question-data-item-style.less');


.matching-question {
  display: flex;
  justify-content: space-around;

  .matching-question-left {
    flex: 1;

    .marching-question-item {
      border: 1px solid #ddd;
      border-radius: 4px;
      overflow-y: auto;
      margin-bottom: 20px;
      padding: 5px;
    }

    div:last-child {
      padding-bottom: 0;
    }

    .matching-question-opt {
      padding: 10px 5px;
      border: 1px solid #ddd;
      border-radius: 4px;
      display: flex;
      margin-bottom: 16px;

      .matching-question-opt-img {
        width: 80px;
        height: 60px;
        flex-shrink: 0;
        cursor: pointer;

        video {
          width: 80px;
          height: 60px;
          flex-shrink: 0;
        }

        img {
          width: 80px;
          height: 60px;
          flex-shrink: 0;
        }

        audio {
          width: 80px;
          height: 60px;
          flex-shrink: 0;
        }
      }

      .matching-question-opt-content {
        overflow-y: auto;
        margin-left: 10px;
        width: 100%;

        &::-webkit-scrollbar {
          width: 4px;

        }
      }
    }
  }

  .matching-question-right {
    flex: 1;

    .marching-question-item {
      border: 1px solid #ddd;
      border-radius: 4px;
      overflow-y: auto;
      margin-bottom: 20px;
      padding: 5px;
    }

    div:last-child {
      padding-bottom: 0;
    }

    .matching-question-opt {
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      display: flex;
      margin-bottom: 16px;



      .matching-question-opt-img {
        flex-shrink: 0;
        cursor: pointer;

        video {
          width: 80px;
          height: 60px;
          flex-shrink: 0;
        }

        img {
          width: 80px;
          height: 60px;
          flex-shrink: 0;
        }

        audio {
          width: 80px;
          height: 60px;
          flex-shrink: 0;
        }
      }

      .matching-question-opt-content {
        overflow-y: auto;
        width: 100%;
        margin-left: 10px;

        &::-webkit-scrollbar {
          width: 4px;

        }
      }
    }
  }

  .matching-question-left,
  .matching-question-right {
    display: flex;
    flex-direction: column;
    min-width: 50px;

    .matching-question-opt {
      flex-grow: 1;
      min-height: 20px;
      margin-bottom: 20px;
      word-break: break-all;
    }
  }

  .matching-question-paint {
    width: 100px;
    flex-shrink: 0;
    min-width: 50px;
  }
}

/* 限制图片宽度，添加到scoped样式中 */
:deep(.matching-question-opt img) {

  height: auto;
  object-fit: contain;
  display: block;
  margin: 0 auto;
}

:deep(.option-item-content img) {
  display: block;
  margin: 0 auto;
}

.umo-node-focused {
  .matching-question-opt {
    background-color: #fff;
  }
}
</style>
