.data-item {
  width: 100%;
  box-sizing: border-box;
  min-height: 100px;
  border: 1px solid #ccc;
  border-radius: 5px;
  padding: 22px 20px 20px 20px;
  color: #333;
 
  &:last-child {
    margin-bottom: 0;
  }
  ul {
    list-style: none;
  }
  .data-item-top {
    margin-bottom: 20px;
    font-weight: bold;
    font-size: 16px;
    width: 100%;
  }
  .data-item-em-remark {
    font-weight: bold;
    color: #333;
    line-height: 28px;
    border-bottom: 1px solid #eaeaea;
    padding-bottom: 10px;
    margin-bottom: 20px;
  }
  .data-item-em {
    :deep(img) {
      max-width: 100%;
    }
    .data-item-remark {
      font-size: 14px;
      line-height: 28px;
      background-color: #fef9e5;
      padding: 10px;
      border: 1px solid #f3d19e;
      border-radius: 4px;
      color: #eec543;
    }

    .data-item-qtitle {
      margin: 10px 0;
    }
    :deep(code) {
      white-space: pre-wrap; /* 保留空格和换行符，并自动换行 */
      word-wrap: break-word; /* 防止长单词溢出容器 */
      word-break: break-all; /* 允许在任意字符处断行（可选） */
    }
  }
  .data-item-option {
    width: 100%;
    &.true-or-false {
      display: flex;
    }
    &.sorting {
      display: block;
    }
    &:last-child {
      margin-bottom: 0;
    }
    .option-item {
      text-align: left;
      margin-bottom: 14px;
      &:last-child {
        margin-bottom: 0;
      }
      &.is-right-answer {
        &::after {
          content: '';
          display: inline-block;
          background: url(@/assets/icons/checkedWhiteNike.svg) no-repeat;
          background-size: contain;
          height: 14px;
          width: 14px;
        }
        .right-answer-icon {
          display: inline-block;
        }
      }
      .right-answer-icon {
        display: none;
      }
    }
    &.count {
      .option-item {
        counter-increment: my-counter; /* 计数器递增 */
        & > img {
          width: 76px;
          height: 100%;
        }
        .option-item-text {
          overflow-y: auto;
          &::-webkit-scrollbar {
            width: 4px;
          }
          &::-webkit-scrollbar-thumb {
            background-color: #ccc;
            border-radius: 2px;
          }
        }
        .option-item-content {
          display: flex;
          font-size: 14px;
          border: 1px solid #eaeaea;
          border-radius: 4px;
          padding: 5px;

          ::v-deep(p) {
            display: block;
          }
        }

        .option-item-content-trueoffalse {
          max-height: 50px;
          display: flex;
          font-size: 14px;
          border: 1px solid #eaeaea;
          border-radius: 4px;
          padding: 5px;
        }
      }
    }
  }
  .data-item-analysis {
    display: flex;
    margin-top: 10px;
    label {
      line-height: 1.4;
      min-width: 30px;
    }
    p {
      flex: 1;
    }
  }
}
