<template>
  <div class="data-item">
    <div v-if="localConfig?.questionTypeShow == 1" class="data-item-top">
      <slot name="selection" /><b>排序题</b>
    </div>
    <div class="data-item-em">

      <div v-if="data.questionRemark && data.questionRemark != '<p><br></p>' && data.questionRemark != test"
        class="data-item-remark">
        <div v-html="data.questionRemark"></div>
      </div>
      <div class="data-item-qtitle">
        <label v-if="props.config?.contentLabel"><b>题干：</b></label>
        <div v-html="data.questionContent" style="margin: 10px 0;">
        </div>
      </div>

    </div>
    <ul v-if="options.length > 0" class="data-item-option count sorting">
      <li v-for="(optItem, index) in options" :key="index" class="option-item" style="margin-bottom: 20px;">
        <div class="option-item-content" :style="`height:${config.sortOptionHeight || 90}px;`">
          <!-- v-html="optItem.optionContent" -->
          <OptionContentTemplate :optionContent="optItem.optionContent" />
        </div>
      </li>
    </ul>
    <div v-if="localConfig.hasAnswer" class="data-item-analysis">
      <label><b>解析：</b></label>
      <div style="margin-left: 10px;" v-html="data.analysis"></div>
    </div>

  </div>
</template>
<script setup lang="ts">
import OptionContentTemplate from './optionContentTemplate.vue';
const props = defineProps({
  data: {
    type: Object,
    required: true
  },
  config: {
    type: Object,
    // 引入showAnswer配置是因为的该组件也用在编辑器中，试题正确答案以及解析不需要打印出来。
    default: () => {
      return {
        contentLabel: false,
        hasAnswer: true
      }
    }
  }
})
const defaultCfg = { contentLabel: false, hasAnswer: true, questionTypeShow: true }
let localConfig = $ref({
  ...defaultCfg,
  ...props.config
})
const show = ref(false)
const videoUrl = ref('')
const audioUrl = ref('')
const titleDialog = ref('')
const visibleImg = ref(false)
const test = ref('<p style="line-height: 2;"><br></p>')
const imgArr = ref([])
// 直接用计算属性，简单粗暴
const options = $computed(() => {
  return props.data.options ?? []
})

// 视频、音频弹窗处理 参数url，类型video/audio
const handleVideoClick = (url: string, type: string) => {
  if (type == 'video') {
    videoUrl.value = url
    titleDialog.value = '视频'
  } else {
    audioUrl.value = url
    titleDialog.value = '音频'
  }
  show.value = true
}

// 视频、音频弹窗关闭
const handleClose = () => {
  show.value = false
  videoUrl.value = ''
  audioUrl.value = ''
  titleDialog.value = ''
}

//图片弹窗处理   
const handleClick = (src: any) => {
  src.forEach(item => {
    imgArr.value.push(item.src)
  })

  const getValue = sessionStorage.getItem('v');
  if (getValue === '1') return
  sessionStorage.setItem('v', '1');

  visibleImg.value = true;
}


const closeViewer = () => {
  sessionStorage.removeItem('v');
  imgArr.value = [];
  visibleImg.value = false;
}


const classifiedContent = computed(() => (contentHtml) => {
  // 创建一个临时div元素来解析HTML
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = contentHtml;
  //初始化结果对象
  const result = {
    text: '',
    images: [],
    videos: [],
    audios: []
  };



  //递归处理所有子节点
  function processNodes(nodes) {
    nodes.forEach(node => {
      if (node.nodeType === Node.TEXT_NODE) {
        // 处理文本节点
        const text = node.textContent.trim();
        if (text) {
          result.text += text + ' ';
        }
      } else if (node.nodeType === Node.ELEMENT_NODE) {
        // 处理元素节点
        if (node.tagName === 'IMG') {
          result.images.push({
            src: node.getAttribute('src') || '',
            alt: node.getAttribute('alt') || '',
            width: node.getAttribute('width') || '',
            height: node.getAttribute('height') || ''
          });
        } else if (node.tagName === 'VIDEO') {
          const video = {
            src: '',
            type: '',
            poster: node.getAttribute('poster') || '',
            controls: node.hasAttribute('controls')
          };

          // 获取视频源
          const sources = node.querySelectorAll('source');
          if (sources.length > 0) {
            video.src = sources[0].getAttribute('src') || '';
            video.type = sources[0].getAttribute('type') || '';
          }

          result.videos.push(video);
        } else if (node.tagName === 'AUDIO') {
          const audio = {
            src: '',
            type: '',
            controls: node.hasAttribute('controls')
          };

          // 获取音频源
          const sources = node.querySelectorAll('source');
          if (sources.length > 0) {
            audio.src = sources[0].getAttribute('src') || '';
            audio.type = sources[0].getAttribute('type') || '';
          }

          result.audios.push(audio);
        } else {
          // 递归处理其他元素的子节点
          if (node.childNodes && node.childNodes.length > 0) {
            processNodes(Array.from(node.childNodes));
          }
        }
      }
    });
  }

  processNodes(Array.from(tempDiv.childNodes));

  // 清理文本内容（去除多余空格）
  result.text = result.text.trim();

  return result;
});


watch(
  () => props.config,
  (nCfg) => {
    if (nCfg) {
      localConfig = {
        ...defaultCfg,
        ...nCfg
      }
    }
  },
  {
    deep: true
  }
)
</script>
<style lang="less" scoped>
@import url('./question-data-item-style.less');
</style>
