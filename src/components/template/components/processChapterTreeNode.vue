<template>
  <div class="tree-list">
    <div v-for="item in innerDataList" :key="item.id" class="tree-item">
      <div class="tree-header">
        <div class="tree-checkbox">
          <div v-if="item.children?.length" @click="handleShowToggle(item)">
            <ChevronDownIcon v-if="!isOpen" style="margin-right: 5px" />
            <ChevronUpIcon v-else />
          </div>

          <div  v-if="isAmend == 0">
            <t-checkbox
              v-if="item.parentId == 0"
              v-model="item.checked"
              :style="{ marginLeft: item.children?.length ? '' : '20px' }"
            ></t-checkbox>
          </div>
        </div>
        <div class="tree-title">
          <div class="tree-name">{{ item.name }}</div>
          <div v-if="item.parentId == 0" class="tree-tag">
            <t-tag
              v-if="item.free == 2 && bookOrganize != 2"
              shape="round"
              theme="success"
              variant="outline"
              size="large"
              >可试读</t-tag
            >
            <t-tag
              v-if="item.free == 1 && bookOrganize != 2"
              shape="round"
              theme="default"
              variant="outline"
              size="large"
              >不可试读</t-tag
            >
          </div>
        </div>
      </div>
      <div v-if="item.parentId == 0" class="tree-content">
        <div class="tree-author">编写者:{{ item.editor }}</div>
        <div class="tree-buttons">
          <t-button
            theme="default"
            size="large"
            variant="text"
            @click="handleEditContent(item)"
          >
            <template #default>
              <i class="iconfont-edit"></i>
              编写内容
            </template>
          </t-button>
          <t-button
            theme="default"
            size="large"
            variant="text"
            @click="jumpReader(item)">
            <template #icon>
              <i class="iconfont-edit iconfont-reader" />
              阅读器预览
            </template>
          </t-button>
          <t-button
            theme="default"
            size="large"
            variant="text"
            @click="handlePreview(item)">
            <template #icon>
              <i class="iconfont-edit iconfont-search" />
              预览
            </template>
          </t-button>
        </div>
      </div>
      <div v-if="item.parentId == 0" class="tree-line">
        <span style="margin-right: 10px">完成度</span>
        <t-progress :percentage="item.completeRate" style="width: 270px" />
      </div>
      <TreeNode
        v-if="isOpen && item.children && item.children.length > 0"
        :data-list="isOpen ? item.children : []"
      />
    </div>

  </div>
</template>

<script setup name="TreeNode">
import { ChevronDownIcon, ChevronUpIcon } from 'tdesign-icons-vue-next'
import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next'

import {
  queryRejectedReasonInfo,
  revokedChapterApply,
} from '@/api/book/bookChapterAuditLog'
import { getRedisToken, updateChapter } from '@/api/book/chapter'
import { useRouter } from 'vue-router'

const { proxy } = getCurrentInstance()
const props = defineProps({
  dataList: {
    type: Array,
    required: true,
  },
  processId: {
    type: String,
    default: '',
  },
  stepId: {
    type: String,
    default: '',
  },
  auditUserId: {
    type: String,
    default: '',
  },
  versionId: {
    type: String,
    default: '',
  },
  bookOrganize: {
    type: String,
    default: '',
  },
  isAmend: {
    type: Number,
    default: 0,
  },
})
const emit = defineEmits(['refresh','editChapter'])

const isOpen = ref(false)
const form = ref({})
const completeRateDialogVisible = ref(false)
const rejectedDialogVisible = ref(false)
const revokedDialogVisible = ref(false)
const innerDataList = ref(props.dataList || [])
const router = useRouter()

//#region 监听器相关

watch(
  () => props.dataList,
  (value) => {
    innerDataList.value = value
  },
  {
    deep: true,
  },
)

//#endregion
//#region 生命周期相关

// onMounted(() => {
//   innerDataList.value = props.dataList;
// })

//#endregion
//#region 操作相关

// 刷新数据
function refresh() {
  emit('refresh')
}

// 跳转阅读器
function jumpReader(item) {
  const { chapterId } = item
  const { bookId } = item
  getRedisToken({ bookId:bookId,chapterId:chapterId }).then((res) => {
    const { data } = res
    if (data) {
      window.open(
        `${`${import.meta.env.VITE_READER_PREVIEW_URL}?token=${data}`}`,
      )
    }
  })
}

// 编辑章节内容
function handleEditContent(item) {
  emit('editChapter', item)
}

// 切换展示展示子菜单
function handleShowToggle() {

  isOpen.value = !isOpen.value
}

// 对外暴露选中结果
function handleSelect() {
  const selectdataList = innerDataList.value.filter((item) => item.checked)
  return selectdataList
}

// 对外暴露全选
function handleCheckAll() {
  innerDataList.value.forEach((item) => {
    item.checked = true
  })
}

// 对外暴露取消全选
function handleCancelCheckAll() {
  innerDataList.value.forEach((item) => {
    item.checked = false
  })
}
// 预览章节
function handlePreview(item) {
  router.push({
    path: '/bookPreview',
    query: {
      chapterId: item.chapterId,
      bookId: item.bookId,
      bookPreviewType: 2,
      formType: 1,
      funType: 2,
      masterFlag: props.masterFlag,
      bookOrganize: props.bookOrganize,
      stepId: props.stepId,
      processId: props.processId,
      auditUserId: props.auditUserId,
    },
  })
}
//#endregion

//#region 暴露函数相关

defineExpose({
  handleSelect,
  handleCheckAll,
  handleCancelCheckAll,
})


//#endregion
</script>

<style scoped lang="less">
.tree-list {
  .tree-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    .tree-checkbox {
      display: flex;
      align-items: center;
    }
    .tree-title {
      font-size: 16px;
      font-weight: bold;
      padding-left: 20px;
      display: flex;
      align-items: center;

      .tree-name {
        padding-right: 20px;
      }

      .tree-tag {
        .t-tag {
          margin-right: 10px;
        }
        .error-icon {
          width: 20px;
          height: 20px;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          background: #f56c6c;
          border-radius: 50%;
          color: #fff;
          font-size: 12px;
        }
      }
    }
  }

  .tree-content {
    display: flex;
    justify-content: space-between;
    font-size: 14px;

    .tree-author {
      color: #666;
      padding-left: 50px;
    }

    .tree-buttons {
      display: flex;
      align-content: center;

      .t-button {
        color: #0966b4;
        :global(.t-button__text) {
          position: relative;
          z-index: 1;
          display: inline-flex;
          justify-content: center;
          align-items: center;
        }
      }
      .iconfont-edit {
        display: inline-flex;
        width: 16px;
        height: 16px;
        background: url('@/assets/images/edit.svg') no-repeat;
        background-size: contain;
        margin-right: 5px;
      }

      .iconfont-import {
        background: url('@/assets/images/import.svg') no-repeat;
      }

      .iconfont-search {
        background: url('@/assets/images/search.svg') no-repeat;
      }

      .iconfont-submit {
        background: url('@/assets/images/submit.svg') no-repeat;
      }

      .iconfont-editer {
        background: url('@/assets/images/editer.svg') no-repeat;
      }

      .iconfont-print {
        background: url('@/assets/images/print.svg') no-repeat;
      }

      .iconfont-cancel {
        background: url('@/assets/images/cancel.svg') no-repeat;
      }
      .iconfont-reader {
        background: url('@/assets/images/search.svg') no-repeat;
      }
    }
  }

  .tree-item {
    padding: 20px 10px;

    &:hover {
      background: #f6f6f6;

      cursor: pointer;
    }
  }

  .tree-line {
    font-size: 14px;
    display: flex;
    padding-left: 50px;

    .tree-set {
      margin-left: 27px;
      color: #0966b4;
      cursor: pointer;
    }

    .demo-progress .el-progress--line {
      margin-bottom: 15px;
      max-width: 600px;
    }
  }

  .tree-setting {
    .tree-setting-title {
      color: #999;

      span {
        color: #666;
      }

      strong {
        font-size: 16px;
        padding: 0 3px;
      }

      margin-bottom: 10px;
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
  }
}
</style>
