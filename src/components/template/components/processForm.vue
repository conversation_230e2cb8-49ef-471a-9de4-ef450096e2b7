<template>
  <t-form ref="approvalFormRef" :data="approvalForm" @submit="handleApproval" :rules="approvalRules" labt-width="120px">
    <t-row  class="mb20">
      <t-col :span="11">
        <t-form-item label="审批人" name="dealUserId">
          <t-select
            v-model="approvalForm.dealUserId"
            placeholder="请选择"
          >
            <t-option
              v-for="item in userList"
              :key="item.userId"
              :label="item.nickName + item.phonenumber"
              :value="item.userId"
            />
          </t-select>
        </t-form-item>
      </t-col>
    </t-row>
    <t-row  class="mb20">
      <t-col :span="5">
        <t-form-item label="审批结果" name="state">
          <t-select
            v-model="approvalForm.state"
            placeholder="请选择"
            style="width: 500px"
          >
            <t-option
              label="通过"
              value="2"
            />
            <t-option
              v-if="!(isAmend == 1 && stepId == 2)"
              label="驳回"
              value="3"
            />
          </t-select>
        </t-form-item>
      </t-col>
      <t-col :span="5" :offset="1">
        <t-form-item label="审批时间" name="processDate" required>
          <t-date-picker
            :enable-time-picker="true"
            style="width: 100%"
            v-model="approvalForm.processDate"
            clearable
            placeholder="请选择审批时间"
            :presets="presets"
          >
          </t-date-picker>
        </t-form-item>
      </t-col>
    </t-row>
    <t-row  class="mb20">
      <t-col :span="11">
        <t-form-item label="审批意见" name="reason" required>
          <t-textarea
            placeholder="请输入内容"
            :maxlength="200"
            show-word-limit
            :autosize="{ minRows: 4, maxRows: 6 }"
            v-model="approvalForm.reason"
          >
          </t-textarea>
        </t-form-item>
      </t-col>
    </t-row>
    <div class="footerBtn">
      <t-button
        theme="default"
        style="margin-right: 10px"
        @click="closeModel"
      >取消</t-button>
      <t-button theme="primary" type="submit">确认</t-button>
    </div>
  </t-form>
  <modal
    v-if="nextNodeVisible"
    :visible="nextNodeVisible"
    :header="`送审`"
    :footer="false"
    width="700px"
    @close="nextNodeVisible = false"
  >
    <div>
      <t-form :data="approvalForm" @submit="handleNextNode" :rules="nextStepRules" labt-width="120px">
        <t-row  class="mb20">
          <t-col :span="11">
            <t-form-item v-if="approvalForm.state == 3" label="审批环节">
              <span>{{ prvStep.stepName }}</span>
            </t-form-item>
            <t-form-item v-else label="审批环节" name="stepId" required>
              <t-select
                v-model="approvalForm.stepId"
                style="width: 500px"
              >
                <t-option
                  v-for="item in publishSteplList"
                  :key="item.stepId"
                  :label="item.stepName"
                  :value="item.stepId"
                  :disabled="item.disabled"
                />
              </t-select>
            </t-form-item>
          </t-col>
        </t-row>
        <t-row  class="mb20">
          <t-col :span="11">
            <t-form-item label="审批部门" name="deptId" required>
              <t-tree-select
                v-model="approvalForm.deptId"
                :data="deptOptions"
                :onChange="deptChange"
                :tree-props="{
                              keys:{
                                  value: 'id',
                                  label: 'label',
                                  children: 'children',
                              }
                            }"
                value-key="id"
                placeholder="请选择归属部门"
                check-strictly
              />
            </t-form-item>
          </t-col>

        </t-row>
        <t-row  class="mb20">
          <t-col :span="11">
            <t-form-item label="审批人" name="userId" required>
              <t-select
                v-model="approvalForm.userId"
                style="width: 500px"
              >
                <t-option
                  v-for="item in deptUserList"
                  :key="item.userId"
                  :label="item.nickName + item.phonenumber"
                  :value="item.userId"
                />
              </t-select>
            </t-form-item>
          </t-col>
        </t-row>
        <div class="footerBtn">
          <t-button
            theme="default"
            style="margin-right: 10px"
            @click="nextNodeVisible = false"
          >取消</t-button>
          <t-button theme="primary" type="submit">确认</t-button>
        </div>
      </t-form>
    </div>
  </modal>
</template>

<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router'
const route = useRoute()
const router = useRouter()
import { listStepNotPage } from '@/api/book/bookPublishStep.js'
import { listType } from '@/api/book/type.js'
import { getBookGroup, groupUserList } from '@/api/book/bookGroup.js'
import { listDept } from '@/api/system/detp.js'
import { listUserByDeptId,listNoPage,deptTreeSelect } from '@/api/system/user.js'
import { addProcess,getPrevProcessInfo } from '@/api/book/process.js';
import dayjs from 'dayjs';

import {
  bookTypeList,
  bookNatureTypeList,
  bookShelfStatusOptions,
  bookPublishStatusOptions,
  bookSchoolStatusOptions,
  getOptionDesc
} from '@/utils/quetions-utils'
import { onBeforeMount, onMounted,onBeforeUnmount, ref } from 'vue'
import { MessagePlugin } from 'tdesign-vue-next'
import { getInfo } from '@/api/login'
import { getNowTime } from '@/utils/dateTimeUtil'

const props = defineProps({
  bookId: {
    type: String,
    default: '',
  },
  processId: {
    type: String,
    default: '',
  },
  stepId: {
    type: String,
    default: '',
  },
  auditUserId: {
    type: String,
    default: '',
  },
  versionId: {
    type: String,
    default: '',
  },
  bookOrganize: {
    type: String,
    default: '',
  },
  isAmend: {
    type: Number,
    default: 0,
  },
})

const emit = defineEmits(['closeDialog'])

const bookId = ref(props.bookId);
const stepId = ref(props.stepId);
const processId = ref(props.processId);
const auditUserId = ref(props.auditUserId);
const versionId = ref(props.versionId);
const isAmend = ref(props.isAmend);
const publishSteplList = ref([])
const nextNodeVisible = ref(false)
const userList = ref([])
const deptList = ref([]);
const deptUserList = ref([]);
const prvStep = ref({});
const deptOptions = ref([]);
const approvalFormRef = ref(null);
const approvalForm = ref({
  dealUserId:'',
  state:'',
  processDate:getNowTime(),
  reason:'',
  stepId:'',
  deptId:'',
  userId:'',
  nextProcess:{},
});
const approvalRules ={
  ceshi : [
    { required: true, message: '审批人不能为空', trigger: 'blur' },
  ],
  dealUserId: [
    { required: true, message: '审批人不能为空', trigger: 'blur' },
    { required: true, message: '审批人不能为空', trigger: 'change' }
  ],
  state:[
    { required: true, message: '审批结果不能为空', trigger: 'blur' },
    { required: true, message: '审批结果不能为空', trigger: 'change' }
  ],
  processDate:[
    { required: true, message: '审批时间不能为空', trigger: 'blur' },
    { required: true, message: '审批时间不能为空', trigger: 'change' }
  ],
  reason:[
    { required: true, message: '审批意见不能为空', trigger: 'blur' }
  ],
}

const nextStepRules = ref({
  stepId:[
    { required: true, message: '审批环节不能为空', trigger: 'blur' },
    { required: true, message: '审批环节不能为空', trigger: 'change' },
  ],
  deptId:[
    { required: true, message: '部门不能为空', trigger: 'blur' },
    { required: true, message: '部门不能为空', trigger: 'change' },
  ],
  userId:[
    { required: true, message: '审批人不能为空', trigger: 'blur' },
    { required: true, message: '审批人不能为空', trigger: 'change' },
  ]
})
const presets = ref({
  此刻:  dayjs().format('YYYY-MM-DD HH:mm:ss')
});
// 审批确认
const handleApproval = ({ validateResult, firstError }) => {
  if (validateResult === true) {
    // 来稿确认环节 审批需要送审 驳回不需要
    if (stepId.value == 2 ){
      if (approvalForm.value.state == 2) {
        // 来搞确认通过逻辑  状态更新为已驳回 处理人还是自己
        openNextNode(approvalForm.value.state);
      } else {
        // 来搞确认驳回逻辑 退回到书稿联系人
        approvalSubmit();
        // closeModel();
      }
    }else if (stepId.value == 15 ){
      // 发布审核环节 审批不需要送审  驳回需要;
      if (approvalForm.value.state == 2) {
        // 发布审核通过逻辑 状态更新为已发版
        approvalSubmit();
        // closeModel();
      } else {
        // 驳回 返回到上一环节 需要弹窗选择批人
        openNextNode(approvalForm.value.state);
      }
    }else {
      openNextNode(approvalForm.value.state);
    }
  }
}
// 获取节点
function getPublishStepList() {
  const param = {
    schoolFlag: props.bookOrganize == 2 ? 1 : '',
  }
  listStepNotPage(param).then((res) => {
    publishSteplList.value = res.data || []
    publishSteplList.value = publishSteplList.value.slice(1,-1);
  })
}

// 打开送审模态框 state: 2 通过 3驳回
function openNextNode(state){
  // 通过逻辑
  if (state == 2){
    // 如果是发布节点 只可以选择发布审核节点
    if (stepId.value == 14){
      publishSteplList.value = publishSteplList.value.filter((item) => item.stepId == 15);
    }else{
      // 如果是普通节点 可以选择当前节点后除发布审核的节点
      publishSteplList.value.map((item) => {
        if (parseInt(item.stepId) <= parseInt(stepId.value) || item.stepId == 15) {
          item.disabled = true;
        }
      })
    }
    approvalForm.value.stepId = '';
    approvalForm.value.deptId = '';
    approvalForm.value.userId = '';

  }
  // 驳回逻辑
  if (state == 3){
    // 如果不是来稿确认环节 查询上次审批流程的节点信息
    if (stepId.value != 2){
      getPrevProcessInfo({bookId: bookId.value,stepId:stepId.value}).then((response) => {
        prvStep.value = response.data
      })
    }
  }
  nextNodeVisible.value = true
}

// 送审确认按钮
function handleNextNode({ validateResult, firstError }){
  if (validateResult === true) {
    approvalSubmit();
  }
}

// 保存审批数据
function approvalSubmit(){
  // 驳回
  if (approvalForm.value.state == 3){
    approvalForm.value.nextProcess = {
      bookId: bookId.value,
      stepId: stepId.value == 2 ? 2 : prvStep.value.stepId, // 来稿确认节点驳回后节点还是来稿确认
      deptId: approvalForm.value.deptId,
      userId: approvalForm.value.userId,
      state:1,
      prevProcessId: processId.value,
      versionId:versionId.value,
    }
  }else{
    // 通过
    approvalForm.value.nextProcess = {
      bookId: bookId.value,
      stepId: approvalForm.value.stepId,
      deptId: approvalForm.value.deptId,
      userId: approvalForm.value.userId,
      state:1,
      prevProcessId: processId.value,
      versionId:versionId.value,
    }
  }
  approvalForm.value.processId = processId.value;
  approvalForm.value.bookId = bookId.value;
  approvalForm.value.versionId = versionId.value,
  approvalForm.value.state = approvalForm.value.state;
  approvalForm.value.processDate = approvalForm.value.processDate;
  approvalForm.value.dealUserId = approvalForm.value.dealUserId;
  approvalForm.value.additionFlag = 0;
  approvalForm.value.reason = approvalForm.value.reason;
  approvalForm.value.stepId = stepId.value;
  approvalForm.value.auditUserId = auditUserId.value;
  addProcess(approvalForm.value).then((response) => {
    nextNodeVisible.value = false;
    router.push({
      path: '/pages/backup',
      query: { bookOrganize: props.bookOrganize },
    })
    closeModel();
  })

}

// 查询部门列表
function getListDept(){
  listDept().then((response) => {
    deptList.value = response.data
  })
}
// 根据部门查询人员
function deptChange(deptId){
  approvalForm.value.userId = '';
  listUserByDeptId(deptId).then((response) => {
    deptUserList.value = response.data
  })
}

// 获取用户列表
function getUserList() {
  listNoPage().then((response) => {
    userList.value = response.data
  })
}

// 获取当前登录人
function getUser() {
  getInfo().then((res) => {
    approvalForm.value.dealUserId = res.user.userId;
  })
}


// 关闭弹窗
function closeModel() {
  emit('closeDialog')
}

// 查询部门下拉树结构
function getDeptTree() {
  deptTreeSelect().then((response) => {
    deptOptions.value = response.data;
  });
}

function initData(){
  getUser();
  getListDept();
  getDeptTree();
  getUserList();
  getPublishStepList();
}
onMounted (() => {
  initData();
})

</script>

<style scoped lang="less">
  .mb20 {
    margin-bottom: 20px;
  }

  .mt30 {
    margin-top: 30px;
  }

  .form-title {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    font-size: 16px;
    font-weight: bold;
    color: #666;

    &::before {
      content: "";
      display: inline-block;
      width: 4px;
      height: 16px;
      background-color: #409eff;
      border-radius: 8px;
      margin-right: 8px;
    }
  }
  .book-approve-header{
    width: 100%;
    height: 30px;
    display: flex;
    .book-approve-header-left{
      width: 60px;
      height: 30px;
    }
    .book-approve-header-node{
      width: 200px;
      height: 30px;
    }
  }
  .footerBtn {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
  }
</style>