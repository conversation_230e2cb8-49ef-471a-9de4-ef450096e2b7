<template>
  <div>
    <div class="back">
      <div class="back-btn-bg">
        <div class="back-btn" @click="$router.go(-1)">
          <ChevronLeftIcon />
          <span>返回</span>
        </div>
        <div class="back-title">
          当前节点：{{ auditType == 1 ? '章节提交' : '章节撤销申请' }}
        </div>
      </div>
      <div><t-button @click="audit">审批</t-button></div>
    </div>
    <div class="approvePage-main">
      <div class="approvePage-left">
        <TreeNodeApprove :data-list="chapterNodeList" />
      </div>
      <div class="approvePage-right">
        <div style="text-align: center; font-size: 30px; margin: 20px 0">
          {{ chapterName }}
        </div>
        <div v-if="revokedReason">撤销理由：{{ revokedReason }}</div>
        <iframe
          ref="iframeRef"
          :src="href"
          frameborder="0"
          class="iframePage"
          @load="onIframeLoad"
        ></iframe>
      </div>
    </div>

    <!-- 审核弹窗 -->
    <t-dialog
      v-model:visible="auditOpen"
      header="审核"
      placement="center"
      width="650px"
      :show-in-attached-element="true"
      @confirm="auditFormSubmit"
      @close="auditOpen = false"
    >
      <t-form
        ref="auditFormRef"
        :data="form"
        :rules="{
          chapterStatus: [
            { required: true, message: '审批结果', type: 'error', trigger: 'change' },
          ],
        }"
        label-width="100px"
      >
        <t-form-item label="审批结果" name="chapterStatus" required>
          <t-select v-model="form.chapterStatus" clearable style="width: 200px">
            <t-option :value="2" label="同意"></t-option>
            <t-option :value="3" label="驳回"></t-option>
          </t-select>
        </t-form-item>
        <t-form-item label="审批意见" name="remark">
          <t-textarea
            v-model="form.remark"
            placeholder="请输入审批意见"
            :maxlength="200"
            show-word-limit
            :autosize="{ minRows: 2, maxRows: 4 }"
          />
        </t-form-item>
      </t-form>
    </t-dialog>
  </div>
</template>

<script setup name="approvePage">
import { ChevronLeftIcon } from 'tdesign-icons-vue-next'
import { MessagePlugin } from 'tdesign-vue-next'
import { useRoute, useRouter } from 'vue-router'
import { auditChapter } from '@/api/book/bookChapterAuditLog.js'
import { getChapterContentInfo } from '@/api/chapterContent'
import { chapterCatalogList } from '@/api/book/chapter.js'
import TreeNodeApprove from '../components/TreeNodeApprove.vue'

const { proxy } = getCurrentInstance()
const route = useRoute()
const router = useRouter()
const auditOpen = ref(false)
const auditType = ref('')
const revokedReason = ref('')
const pageConfigList = ref([])
const chapterNodeList = ref([])
const chapterName = ref('')
const chapterId = ref(null)
const bookId = ref(null)
const form = ref({ logId: null })
const href = ref('')
const iframeRef = ref(null)
const iframeLoaded = ref(false)
const messageQueue = ref([]) // 消息队列
const ALLOWED_CHILD_ORIGIN = import.meta.env.VITE_READER_PREVIEW_URL
  .replace(/^['"]|['"]$/g, '')
  .replace(/^(https?:\/\/[^/]+).*/, '$1');

// ---------------- 生命周期 ----------------
onMounted(() => {
  chapterId.value = route.query.chapterId
  bookId.value = route.query.bookId
  form.value.logId = route.query.logId
  auditType.value = route.query.auditType
  revokedReason.value = route.query.revokedReason
  chapterName.value = route.query.chapterName
  getChapterDetail()
  chapterCatalogue()

  href.value =
    `${import.meta.env.VITE_READER_PREVIEW_URL}?k=${bookId.value}&cid=${chapterId.value}&fromType=2&operationFlag=false`

  setupPostMessageListener()

  // 超时检测
  setTimeout(() => {
    if (!iframeLoaded.value) {
      console.warn('iframe连接超时，尝试重新建立连接')
      sendMessageToIframe({
        type: 'parent_ready',
        message: '父页面准备就绪(超时重试)',
      })
    }
  }, 5000)
})

onUnmounted(() => {
  window.removeEventListener('message', handlePostMessage)
})

// ---------------- postMessage 通信 ----------------
function setupPostMessageListener() {
  window.addEventListener('message', handlePostMessage, false)
}

function handlePostMessage(event) {
  if (event.origin !== ALLOWED_CHILD_ORIGIN) {
    console.warn('拦截非法消息，来自：', event.origin)
    return
  }

  console.log('父页面收到消息:', event.data)

  switch (event.data.type) {
    case 'iframe_ready':
      iframeLoaded.value = true
      processMessageQueue()
      // 发送初始化数据
      sendMessageToIframe({
        type: 'init_data',
        bookId: bookId.value,
        chapterId: chapterId.value,
        auditType: auditType.value,
      })
      break
    case 'iframe_error':
      console.error('iframe错误:', event.data.error)
      MessagePlugin.error('预览页面加载失败')
      break
    case 'content_loaded':
      console.log('内容加载完成')
      break
    case 'user_interaction':
      console.log('用户交互:', event.data.action)
      break
    case 'heartbeat':
      console.log('收到iframe心跳')
      break
    default:
      console.log('未知消息类型:', event.data.type)
  }
}


function sendMessageToIframe(data) {
  if (iframeRef.value && iframeRef.value.contentWindow) {
    try {
      iframeRef.value.contentWindow.postMessage(data, ALLOWED_CHILD_ORIGIN)
      console.log('父页面向iframe发送消息:', data)
    } catch (err) {
      console.error('发送消息失败:', err)
    }
  } else {
    console.warn('iframe未准备好，消息入队')
    messageQueue.value.push(data)
  }
}

function processMessageQueue() {
  if (messageQueue.value.length) {
    console.log(`发送队列中的 ${messageQueue.value.length} 条消息`)
    messageQueue.value.forEach(msg => sendMessageToIframe(msg))
    messageQueue.value = []
  }
}

function onIframeLoad() {
  setTimeout(() => {
    sendMessageToIframe({
      type: 'parent_ready',
      message: '父页面已准备就绪',
    })
  }, 1000)
}

// ---------------- 业务操作 ----------------
function getChapterDetail() {
  getChapterContentInfo(chapterId.value).then((res) => {
    const { data } = res
    pageConfigList.value = data ? JSON.parse(data.content).content || [] : []
  })
}

async function chapterCatalogue() {
  const res = await chapterCatalogList(chapterId.value)
  if (res.code === 200) {
    chapterNodeList.value = res.data
  }
}

function audit() {
  form.value = { logId: form.value.logId, chapterStatus: null, remark: null }
  auditOpen.value = true
}

function auditFormSubmit() {
  proxy.$refs['auditFormRef'].validate({ showErrorMessage: true }).then(valid => {
    if (valid === true) {
      auditChapter(form.value).then(() => {
        MessagePlugin.success('审核成功')
        auditOpen.value = false
        router.push({ path: '/pages/chapter' })
      })
    }
  })
}

</script>


<style lang="less" scoped>
.back {
  display: flex;
  width: 100%;
  padding-bottom: 20px;
  justify-content: space-between;
  border-bottom: 1px solid #f1f1f1;

  .back-btn-bg {
    display: flex;
    align-items: center;

    .back-title {
      margin-left: 60px;
      color: #666;
      font-size: 14px;
    }

    .back-btn {
      cursor: pointer;
      display: flex;
      align-items: center;
      color: #999;
      font-size: 14px;

      span {
        margin-left: 5px;
      }
    }
  }
}
/* 针对所有结构标签内的h1 */
:deep(h1) {
  font-size: 2em !important; /* 强制覆盖默认值 */
}

.approvePage-main {
  display: flex;

  .approvePage-left {
    width: 300px;

    background-color: #fff;
    overflow-y: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
    &::-webkit-scrollbar {
      display: none;
    }
    height: calc(100vh - 220px);
    border-right: 1px solid #ddd;
  }

  .approvePage-right {
    flex: 1;
    height: calc(100vh - 220px);
    overflow-y: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
    &::-webkit-scrollbar {
      display: none;
    }
    background-color: #fafafa;
    .approvePage-page {
      width: 900px;
      margin: 0 auto;
      min-height: calc(100vh - 220px);
      background-color: #fff;
      padding: 20px;
      margin: 20px auto;
    }
    .iframePage {
      width: 100%;
      height: 100%;
    }
  }
}
</style>