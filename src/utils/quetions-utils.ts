import { v4 as uuidv4 } from 'uuid'

export const uuid = () => {
  return uuidv4()
}

export const deepCopyObject = (obj: any) => {
  return JSON.parse(JSON.stringify(obj))
}

export const OPTION_EN = [
  'A',
  'B',
  'C',
  'D',
  'E',
  'F',
  'G',
  'H',
  'I',
  'J',
  'K',
  'L',
  'M',
  'N',
  'O',
  'P',
  'Q',
  'R',
  'S',
  'T',
  'U',
  'V',
  'W',
  'X',
  'Y',
  'Z',
]

export const QuestionTypeSecondLevelEnum = {
  SingleChoiceQuestion: {
    label: '单选题',
    value: '1',
  },
  MultipleChoiceQuestion: {
    label: '多选题',
    value: '2',
  },
  Completion: {
    label: '填空',
    value: '3',
  },
  Sorting: {
    label: '排序',
    value: '4',
  },
  Matching: {
    label: '连线',
    value: '5',
  },
  SimpleQuestion: {
    label: '简答',
    value: '6',
  },
  TrueOrFalse: {
    label: '判断',
    value: '7',
  },
  ProgrammingFillInBlanks: {
    label: '编程',
    value: '8',
  },
}

export const QuestionNewTypes = {
  CommonQuestion: {
    label: t('questions.addingQuestionItem'),
    value: 'addingQuestionItem',
  },
  ImportFromExternalProfile: {
    label: t('questions.importFromExternalProfile'),
    value: 'importFromExternalProfile',
  },
  ImportFromQuestionItemRepo: {
    label: t('questions.ImportFromQuestionItemRepo'),
    value: 'importFromQuestionItemRepo',
  },
}

// 2025.1.13新改版的题型选择下拉选项:
export const QuestionImportCategoryDropdownOptions = [
  {
    label: QuestionNewTypes.CommonQuestion.label,
    value: QuestionNewTypes.CommonQuestion.value,
  },
  {
    label: QuestionNewTypes.ImportFromExternalProfile.label,
    value: QuestionNewTypes.ImportFromExternalProfile.value,
  },
  {
    label: QuestionNewTypes.ImportFromQuestionItemRepo.label,
    value: QuestionNewTypes.ImportFromQuestionItemRepo.value,
  },
]
// 2025.1.13新改版的题目类型选择:
export const QuestionTypesDescriptive = [
  {
    label: QuestionTypeSecondLevelEnum.SingleChoiceQuestion.label,
    value: QuestionTypeSecondLevelEnum.SingleChoiceQuestion.value,
    disabled: false,
  },
  {
    label: QuestionTypeSecondLevelEnum.MultipleChoiceQuestion.label,
    value: QuestionTypeSecondLevelEnum.MultipleChoiceQuestion.value,
    disabled: false,
  },
  {
    label: QuestionTypeSecondLevelEnum.Completion.label,
    value: QuestionTypeSecondLevelEnum.Completion.value,
    disabled: false,
  },
  {
    label: QuestionTypeSecondLevelEnum.TrueOrFalse.label,
    value: QuestionTypeSecondLevelEnum.TrueOrFalse.value,
    disabled: false,
  },
  {
    label: QuestionTypeSecondLevelEnum.Sorting.label,
    value: QuestionTypeSecondLevelEnum.Sorting.value,
    disabled: false,
  },
  {
    label: QuestionTypeSecondLevelEnum.Matching.label,
    value: QuestionTypeSecondLevelEnum.Matching.value,
    disabled: false,
  },
  {
    label: QuestionTypeSecondLevelEnum.SimpleQuestion.label,
    value: QuestionTypeSecondLevelEnum.SimpleQuestion.value,
    disabled: false,
  },
  {
    label: QuestionTypeSecondLevelEnum.ProgrammingFillInBlanks.label,
    value: QuestionTypeSecondLevelEnum.ProgrammingFillInBlanks.value,
    disabled: false,
  },
]

export const emptyProgrammingSelectionTemplate = {
  code: '',
  language: 'java',
}

export const emptyCommonQuestionTemplate = {
  // firstLevelType: '',
  questionType: QuestionTypeSecondLevelEnum.SingleChoiceQuestion.value, // 默认单选题
  questionContent: '', //题干名称
  questionRemark: '', // 题目备注
  options: [{ optionContent: '', optionId: uuid(), rightFlag: 0 }], //选项 (单选题，多选题）
  // sortingOptions: [{val: '', id: uuid()}, {val: '', id: uuid()}], // 排序题的选项
  // rightKey: [], //正确答案 (对错题)
  analysis: '', //解析
  rightAnswer: '',
  collapseFlag: false, // 是否折叠
  answerInShort: '', // 简答
  // matchingOptions: {leftOption: [], rightOption: [], matchingResult: []},
  disorder: null,
  createSource: 1, // 0：在教材管理中创建，1：在编辑器编辑页面创建
  codeContent: JSON.stringify(emptyProgrammingSelectionTemplate),
}

export const questionType = [
  {
    label: t('questions.single'),
    value: 1,
  },
  {
    label: t('questions.multiple'),
    value: 2,
  },
  {
    label: t('questions.fillInTheBlanks'),
    value: 3,
  },
  {
    label: t('questions.judge'),
    value: 4,
  },
  {
    label: t('questions.orderby'),
    value: 5,
  },
  {
    label: t('questions.connection'),
    value: 6,
  },

  {
    label: t('questions.shortAnswer'),
    value: 7,
  },
  {
    label: t('questions.programmingFill'),
    value: 8,
  },
  {
    label: t('questions.programmingAnswer'),
    value: 9,
  },
]

//任务类型
export const taskTypeList = [
  {
    label: '全部',
    value: '',
  },
  {
    label: '数据导出',
    value: '1',
  },
  {
    label: '教材导入',
    value: '2',
  },
  {
    label: '教材复制',
    value: '3',
  },
  {
    label: '章节导入',
    value: '4',
  },
  {
    label: '教材导出',
    value: '5',
  },
  {
    label: '更新题注',
    value: '6',
  },
  {
    label: '修正',
    value: '7',
  },
  {
    label: '章节导出',
    value: '10',
  },
]

//任务状态
export const taskStatusList = [
  {
    label: '全部',
    value: '',
  },
  {
    label: '未开始',
    value: '0',
  },
  {
    label: '进行中',
    value: '1',
  },
  {
    label: '成功',
    value: '2',
  },
  {
    label: '失败',
    value: '3',
  },
]

// 主副教材
export const mainSubTextbookList = [
  {
    label: '全部',
    value: '',
  },
  {
    label: '常规教材',
    value: '1',
  },
  {
    label: '主教材',
    value: '2',
  },
  {
    label: '副教材',
    value: '3',
  },
]

export const feedbackTypeList = [
  {
    value: 1,
    label: '错别字',
  },
  {
    value: 2,
    label: ' 逻辑错误',
  },
  {
    value: 3,
    label: '内错误',
  },
  {
    value: 4,
    label: '图片错误',
  },
  {
    value: 5,
    label: '其他',
  },
]

export const feedbackStatusList = [
  {
    value: 0,
    label: '待处理',
  },
  {
    value: 1,
    label: '已处理',
  },
]

export const downloadOptions = [
  {
    value: 1,
    label: '允许',
  },
  {
    value: 2,
    label: '不允许',
  },
]

export const statusList = [
  {
    value: 0,
    label: '不需处理',
  },
  {
    value: 1,
    label: '待审核',
  },
  {
    value: 2,
    label: '已通过',
  },
  {
    value: 3,
    label: '已驳回',
  },
]

export const chapterAuditStatusList = [
  {
    value: 1,
    label: '待审核',
  },
  {
    value: 2,
    label: '已通过',
  },
  {
    value: 3,
    label: ' 已驳回',
  },
  {
    value: 4,
    label: '已取消',
  },
]

//#region 教材相关
export const bookTypeList = [
  {
    value: 1,
    label: '公开教材',
  },
  {
    value: 2,
    label: '校本教材',
  },
]
export const bookNatureTypeList = [
  {
    value: 1,
    label: '常规教材',
  },
  {
    value: 2,
    label: '主教材',
  },
  {
    value: 3,
    label: '副教材',
  },
]
// 教材销售状态 上架状态 1已上架 2未上架 3召回 4即将上架
export const bookShelfStatusOptions = [
  {
    value: 1,
    label: '已上架',
  },
  {
    value: 2,
    label: '未上架',
  },
  {
    value: 3,
    label: '召回',
  },
  {
    value: 4,
    label: '即将上架',
  },
]
//公开教材出版状态 出版状态 1未出版 2已出版
export const bookPublishStatusOptions = [
  {
    value: 1,
    label: '未出版',
  },
  {
    value: 2,
    label: '已出版',
  },
]
//校本教材出版状态 出版状态 1已创建 2已完成
export const bookSchoolStatusOptions = [
  {
    value: 1,
    label: '已创建',
  },
  {
    value: 2,
    label: '已完成',
  },
]

export const questionTypeOptions = [
  { value: 1, label: '单选题' },
  { value: 2, label: '多选题' },
  { value: 3, label: '填空题' },
  { value: 4, label: '排序题' },
  { value: 5, label: '连线题' },
  { value: 6, label: '简答题' },
  { value: 7, label: '判断题' },
  { value: 8, label: '编程题' },
]

export const questionTypeMap = questionTypeOptions.reduce((map, item) => {
  map[item.value] = item.label
  return map
}, {})

// 获取文本
export const getOptionDesc = (
  option_list: {
    value: string | number
    label?: string
    desc?: string
  }[],
  value: string | number,
) => {
  if (!option_list) {
    return ''
  }
  const selectOptionList = option_list.filter((item) => item.value == value)
  if (selectOptionList.length == 0) {
    return ''
  }
  return selectOptionList[0].desc || selectOptionList[0].label || ''
}

export const programmingLanguageList = [
  { value: 'markup', label: 'HTML' },
  { value: 'css', label: 'CSS' },
  { value: 'javascript', label: 'JavaScript' },
  { value: 'c', label: 'C' },
  { value: 'csharp', label: 'C#' },
  { value: 'cpp', label: 'C++' },
  { value: 'java', label: 'Java' },
  { value: 'python', label: 'Python' },
  { value: 'matlab', label: 'Matlab' },
  { value: 'erlang', label: 'Erlang' },
  { value: 'go', label: 'Go' },
  { value: 'json', label: 'JSON' },
  { value: 'less', label: 'Less' },
  { value: 'markdown', label: 'Markdown' },
  { value: 'php', label: 'PHP' },
  { value: 'rust', label: 'Rust' },
  { value: 'sass', label: 'Sass' },
  { value: 'scss', label: 'Scss' },
  { value: 'shell', label: 'Shell' },
  { value: 'sql', label: 'SQL' },
  { value: 'typescript', label: 'TypeScript' },
  { value: 'xml', label: 'XML' },
  { value: 'plaintext', label: 'Plain Text' },
]
