{"name": "DutpEditor", "version": "1.0.0", "type": "module", "description": "dutp editor", "keywords": ["dutp editor", "document editor", "docs editor", "word editor", "office edtior", "web editor", "richtext editor", "wysiwyg editor", "vue editor", "vue3 editor", "ai editor", "editor", "web office", "online office"], "author": {"name": "dutp", "email": "<EMAIL>", "url": "https://www.dutp.cn"}, "license": "MIT", "homepage": "https://dutp.cn", "bugs": {"url": "https://dutp.cn", "email": "<EMAIL>"}, "files": ["dist"], "exports": {".": {"import": "./dist/umo-editor.js"}, "./style": {"import": "./dist/style.css"}}, "types": "./types/index.d.ts", "scripts": {"build:tsc": "vue-tsc --noEmit && vite build", "build:prod": "npm run fix-memory-limit && vite build", "build:stage": "npm run fix-memory-limit && vite build --mode staging", "build:release": "npm run fix-memory-limit && vite build --mode release", "dev": "vite", "format": "prettier --write .", "check:code": "eslint --cache src", "check:style": "stylelint src/**/*.{css,less,vue}", "check:types": "vue-tsc --noEmit", "lint:code": "eslint --cache --fix src", "lint:style": "stylelint --fix src/**/*.{css,less,vue}", "test": "NODE_NO_WARNINGS=1 vitest", "test:coverage": "npm run test --coverage", "test:watch": "npm run test --watch --coverage", "prepare": "husky", "prepublishOnly": "npm run build", "fix-memory-limit": "cross-env LIMIT=4096 increase-memory-limit", "postinstall": "patch-package"}, "dependencies": {"@eslint/object-schema": "^2.1.4", "@imgly/background-removal": "1.5.5", "@tiptap/core": "2.24.2", "@tiptap/extension-bold": "2.24.2", "@tiptap/extension-bubble-menu": "2.24.2", "@tiptap/extension-bullet-list": "2.24.2", "@tiptap/extension-character-count": "2.24.2", "@tiptap/extension-color": "2.24.2", "@tiptap/extension-document": "2.24.2", "@tiptap/extension-dropcursor": "2.24.2", "@tiptap/extension-focus": "2.24.2", "@tiptap/extension-heading": "2.24.2", "@tiptap/extension-highlight": "2.24.2", "@tiptap/extension-horizontal-rule": "2.24.2", "@tiptap/extension-image": "2.24.2", "@tiptap/extension-invisible-characters": "2.24.2", "@tiptap/extension-link": "2.24.2", "@tiptap/extension-list-item": "2.24.2", "@tiptap/extension-ordered-list": "2.24.2", "@tiptap/extension-placeholder": "2.24.2", "@tiptap/extension-subscript": "2.24.2", "@tiptap/extension-superscript": "2.24.2", "@tiptap/extension-table": "2.24.2", "@tiptap/extension-table-cell": "2.24.2", "@tiptap/extension-table-header": "2.24.2", "@tiptap/extension-table-row": "2.24.2", "@tiptap/extension-task-item": "2.24.2", "@tiptap/extension-task-list": "2.24.2", "@tiptap/extension-text-align": "2.24.2", "@tiptap/extension-text-style": "2.24.2", "@tiptap/extension-typography": "2.24.2", "@tiptap/extension-underline": "2.24.2", "@tiptap/pm": "2.24.2", "@tiptap/starter-kit": "2.24.2", "@tiptap/vue-3": "2.24.2", "@tool-belt/type-predicates": "^1.3.0", "@types/svg64": "^1.1.2", "@umoteam/editor-external": "4.5.0", "@vue-monaco/editor": "^0.0.6", "@vueuse/core": "^11.3.0", "@wangeditor/editor": "5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "ali-oss": "^6.21.0", "axios": "^1.7.8", "buffer-image-size": "^0.6.4", "cross-env": "^7.0.3", "disable-devtool": "^0.3.8", "docx": "^8.5.0", "dom-to-image-more": "^3.4.3", "echarts": "^5.5.1", "echarts-wordcloud": "^2.1.0", "es-drager": "1.2.11", "file-saver": "^2.0.5", "file64": "^1.0.4", "hotkeys-js": "^3.13.7", "html2canvas": "^1.4.1", "increase-memory-limit": "^1.0.7", "install": "^0.13.0", "js-base64": "^3.7.7", "js-cookie": "3.0.5", "jsbarcode": "^3.11.6", "jsencrypt": "3.3.2", "jspdf": "^3.0.1", "katex": "^0.16.11", "lodash": "^4.17.21", "mermaid": "^11.2.0", "npm": "^10.9.1", "nzh": "^1.0.13", "patch-package": "^8.0.0", "plyr": "^3.7.8", "pretty-bytes": "^6.1.1", "print-js": "^1.6.0", "prism-code-editor": "^3.4.0", "prismjs": "^1.29.0", "prosemirror-docx": "^0.2.0", "prosemirror-transform": "^1.10.0", "qrcode-svg": "^1.1.0", "qrcode.vue": "^3.6.0", "resize-observer-polyfill": "^1.5.1", "sign-canvas-plus": "^2.0.3", "smooth-signature": "^1.0.15", "svg64": "^2.0.0", "tippy.js": "^6.3.7", "ts-deepmerge": "^7.0.1", "uuid": "^11.0.3", "vue-cropper": "^1.1.4", "vue-draggable-plus": "^0.6.0", "vue-i18n": "^10.0.0", "vue-prism-component": "^2.0.0", "vue-router": "4.5.0", "xlsx": "^0.18.5", "zeed-dom": "^0.14.0"}, "devDependencies": {"@bithero/monaco-editor-vite-plugin": "^1.0.2", "@eslint/eslintrc": "^3.1.0", "@eslint/js": "^9.10.0", "@intlify/unplugin-vue-i18n": "^5.0.0", "@sereneinserenade/tiptap-search-and-replace": "^0.1.1", "@testing-library/vue": "^8.1.0", "@types/dom-to-image": "^2.6.7", "@types/file-saver": "^2.0.7", "@types/node": "^22.5.4", "@types/qrcode-svg": "^1.1.5", "@types/uuid": "10.0.0", "@typescript-eslint/eslint-plugin": "^8.5.0", "@typescript-eslint/parser": "^8.5.0", "@vitejs/plugin-vue": "^4.6.2", "@vue-macros/reactivity-transform": "^1.0.4", "@vue-macros/volar": "^0.29.1", "@vue/eslint-config-prettier": "^9.0.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-functional": "^5.0.8", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-unused-imports": "^4.1.3", "eslint-plugin-vue": "^9.28.0", "globals": "^15.9.0", "husky": "^9.1.5", "jsdom": "^25.0.0", "less": "^4.2.0", "less-loader": "^12.2.0", "lint-staged": "^15.2.10", "monaco-editor": "^0.52.2", "postcss": "^8.3.3", "postcss-html": "^1.7.0", "prettier": "^3.3.3", "stylelint": "^16.9.0", "stylelint-config-recommended-less": "^3.0.1", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-less": "^3.0.1", "tdesign-vue-next": "1.10.0", "typescript": "5.5.4", "typescript-eslint": "8.5.0", "unplugin-auto-import": "0.18.2", "unplugin-vue": "5.1.5", "unplugin-vue-components": "0.27.4", "unplugin-vue-macros": "2.11.11", "vite": "^4.5.3", "vite-plugin-inspect": "^0.8.7", "vite-plugin-monaco-editor": "^1.1.0", "vite-plugin-svg-icons": "^2.0.1", "vite-tsconfig-paths": "^5.0.1", "vitest": "^2.0.5", "vue": "3.5.2", "vue-tsc": "^2.1.6"}, "engines": {"node": ">=18.0.0"}, "lint-staged": {"*.{js,jsx,mjs,cjs,ts,tsx,mts,cts,vue,less,css,yml,yaml,json,md,html}": ["prettier --write src"], "*.{css,less,vue}": ["stylelint --write src/**/*.{css,less,vue}"], "*.{js,jsx,mjs,cjs,ts,tsx,mts,cts,vue}": ["eslint --fix src"]}, "peerDependencies": {"snabbdom": "^3.6.2"}}